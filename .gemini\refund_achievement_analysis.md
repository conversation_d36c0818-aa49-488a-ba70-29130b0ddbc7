# BSP业绩系统-退款业绩生成分析文档

## 1. 数据相关退出点详细分析

退款业绩生成流程复杂，涉及多个模块和外部调用，因此存在较多的数据退出点。这些退出点是保证数据一致性和流程正确性的关键。

### 1.1 主流程退出点 (AchievementRefundHandler)

#### 1.1.1 获取分布式锁失败
**代码位置**: `AchievementRefundHandler.java:178-182`
**代码片段**:
```java
Boolean lockAcquired = stringRedisTemplate.opsForValue().setIfAbsent(lockKey, "processing", 5, TimeUnit.MINUTES);
if (!Boolean.TRUE.equals(lockAcquired)) {
    log.info("任务ID {} 获取分布式锁失败，跳过", refundTask.getTaskId());
    return;
}
```
**退出原因**: 当前退款任务正在被另一个服务实例处理，为避免重复执行，当前流程直接退出。
**数据依赖**: Redis 分布式锁。
**提供数据**: 无。
**默认值建议**: ❌ **不可处理**。这是核心的并发控制机制，必须退出。

#### 1.1.2 获取售后订单详情失败
**代码位置**: `AchievementRefundHandler.java:268-272`
**代码片段**:
```java
AfterSalesOrderDetailResp response = innerService.queryAfterSalesOrderDetail(refundTask.getAftersaleOrderNo());
if (response == null) {
    log.error("查询售后订单详情失败，售后订单号: {}，直接退出", refundTask.getAftersaleOrderNo());
    return;
}
```
**退出原因**: 调用 `innerService` 无法获取售后订单的详细信息（如退款金额、商品、规格等）。
**数据依赖**: 售后中台的 `queryAfterSalesOrderDetail` 接口。
**提供数据**: `AfterSalesOrderDetailResp`，包含退款计算所需的所有核心数据。
**默认值建议**: ⚠️ **谨慎处理**。理论上可构造一个默认的 `response` 对象，但这可能导致错误的退款计算。更好的策略是任务失败重试。

#### 1.1.3 找不到原始商品业绩
**代码位置**: `AchievementRefundHandler.java:277-281`
**代码片段**:
```java
if (CollectionUtils.isEmpty(productDetailList)) {
    log.warn("查询业绩商品明细数据为空，订单ID: {}...，直接退出", orderId, productId, orderProductCode);
    return;
}
```
**退出原因**: 根据退款任务中的订单ID和商品信息，在 `achievement_product_detail` 表中找不到任何对应的、可以用来冲销的原始业绩记录。
**数据依赖**: `achievement_product_detail` 表。
**提供数据**: `List<AchievementProductDetailModel>`，作为生成退款记录的模板。
**默认值建议**: ❌ **不可处理**。没有原始业绩就无法生成退款业绩，必须退出。这通常意味着数据延迟或不一致。

### 1.2 广告通特殊逻辑退出点 (AchievementRefundWebsiteHandler)

#### 1.2.1 未配置广告通产品
**代码位置**: `AchievementRefundWebsiteHandler.java:101-104`
**代码片段**:
```java
String advertisementConfig = productAchievementConfig.getAdvertisement();
if (StrUtil.isBlank(advertisementConfig)) {
    return;
}
```
**退出原因**: 系统配置中没有定义哪些产品属于“广告通”产品。
**数据依赖**: `productAchievementConfig.advertisement` 配置项。
**提供数据**: 广告通产品ID列表。
**默认值建议**: ❌ **不可处理**。核心配置缺失，无法执行广告通冲销逻辑，必须退出。

#### 1.2.2 找不到关联的广告通业绩
**代码位置**: `AchievementRefundWebsiteHandler.java:125` (隐含退出点)
**代码片段**:
```java
List<AchievementProductDetailModel> advertisementProducts = queryAdvertisementProducts(businessMonthIds,
        advertisementProductIds, originalProduct.getCustomerId());
// 如果 advertisementProducts 为空，后续的 for 循环不会执行，方法结束
```
**退出原因**: 在网站产品退款后，根据时间范围、客户ID等条件，没有找到任何关联的、需要被冲销的广告通业绩。
**数据依赖**: `achievement_product_detail` 表中历史的广告通业绩。
**提供数据**: 需要被处理的广告通业绩列表。
**默认值建议**: ❌ **不可处理**。这是正常的业务场景，说明该网站订单没有关联的广告通业绩需要处理。

#### 1.2.3 网站产品检查条件不满足
**代码位置**: `AchievementRefundWebsiteHandler.java:300-303`
**代码片段**:
```java
if (!checkWebsiteProductsAllZero(businessMonth, product.getCustomerId(), websiteOrderProductId)) {
    return;
}
```
**退出原因**: `checkWebsiteProductsAllZero` 方法检查发现，客户名下还存在其他有效的网站产品（新开或升级），因此不满足冲销广告通业绩的条件。
**数据依赖**: 客户名下所有网站产品的历史业绩数据。
**提供数据**: 是否可以执行冲销的布尔判断。
**默认值建议**: ❌ **不可处理**。这是核心业务规则，防止错误冲销。

## 2. 数据依赖关系梳理

### 2.1 核心数据流向图

```mermaid
graph TD
    A[MqOrderRefundInfoModel] --> B{AchievementRefundHandler};
    B --> C[innerService.queryAfterSalesOrderDetail];
    B --> D[查询原始业绩];
    
    subgraph 生成退款记录
        direction LR
        E{计算退款比例} --> F[AchievementRefundCommonHandler];
        D & C --> F --> G[生成负向流水];
    end

    B --> H{是否网站产品?};
    H -->|是| I[AchievementRefundWebsiteHandler];
    H -->|否| J[保存退款记录];

    subgraph 广告通处理
        direction LR
        I --> K[查找关联广告通业绩];
        K --> L{检查冲销条件};
        L --> M[反向生成广告通负向业绩];
        M --> J;
    end

    G --> J;
```

### 2.2 数据依赖详情表

| 数据类型 | 来源接口/方法 | 关键字段 | 影响业绩项 | 缺失影响 |
|---|---|---|---|---|
| **售后订单详情** | `innerService.queryAfterSalesOrderDetail` | `refundAmount`, `paidAmount`, 退款商品/规格 | 计算退款比例，确定退款范围。 | **高** - 流程无法继续。 |
| **原始业绩记录** | `achievement_product_detail` 等表 | 原始业绩的各项金额、归属人 | 作为模板，生成负向记录。 | **高** - 无法生成退款。 |
| **商务月信息** | `businessMonthService.getMonthInfo` | `businessMonthId`, `month` | 确定退款业绩归属的统计周期。 | **中** - 可能导致统计错误。 |
| **广告通配置** | `productAchievementConfig` | `advertisement`, `specId` | 定位需要冲销的广告通产品和规格。 | **高** - 网站退款时无法冲销广告通。 |

## 3. 默认值处理策略

### 3.1 推荐实施的默认值策略

#### 3.1.1 高优先级（已实施或建议）

| 数据项 | 默认值策略 | 实现方式 | 风险等级 |
|---|---|---|---|
| **退款比例** | 若 `paidAmount` 为0或 `null`，退款比例默认为1 | 在 `generateRefundProductFromItem` 中判断，`multiplier = BigDecimal.ONE;` | 低 |
| **售后合同编号** | 若获取合同接口失败，则不设置该字段 | 在 `createRefundProduct` 中 `try-catch` 异常，仅记录日志。 | 低 |

#### 3.1.2 中优先级（谨慎实施）

| 数据项 | 默认值策略 | 实现方式 | 风险等级 |
|---|---|---|---|
| **售后订单详情** | 任务失败，等待下次调度重试 | 在 `addRefundTaskHandler` 的 `catch` 块中增加失败次数，而不是构造默认对象。 | 中 |
| **商务月信息** | 若 `getMonthInfo` 失败，使用退款任务的创建时间计算默认商务月 | 在 `generateRefundProductFromItem` 中增加 `try-catch`，若获取商务月失败，则根据 `refundTask.getCreateTime()` 计算。 | 中 |

### 3.2 开关控制建议

对于广告通这种复杂的冲销逻辑，建议增加配置开关，以便在出现问题时能快速禁用该功能，而不影响正常的退款流程。

```java
// application.yml

achievement:
  refund:
    website-handler:
      enabled: true # 总开关，控制是否执行广告通冲销逻辑
```

```java
// AchievementRefundHandler.java

@Value("${achievement.refund.website-handler.enabled:true}")
private boolean websiteHandlerEnabled;

// ... 在调用 a chievementRefundWebsiteHandler 之前检查开关
if (websiteHandlerEnabled && productDetail.getProductId().equals(Long.valueOf(productAchievementConfig.getWebsite()))) {
    achievementRefundWebsiteHandler.processWebsiteProductRefund(...);
}
```

## 4. 数据流程优化方案

```mermaid
graph TD
    A[processPendingRefundTasks] --> B{获取分布式锁};
    B -->|成功| C[获取售后详情];
    B -->|失败| Z1[退出];
    
    C -->|成功| D[查询原始业绩];
    C -->|失败| Z2[记录异常并重试];

    D -->|存在| E[生成各层级负向业绩流水];
    D -->|不存在| Z3[记录异常并退出];

    E --> F{是否网站产品?};
    F -->|是| G[执行广告通冲销逻辑];
    F -->|否| H[直接保存];

    G --> H[保存所有流水];
    H --> I[更新任务状态为成功];

    subgraph 异常处理
        C -- 异常 --> X[更新任务失败次数];
        D -- 异常 --> X;
        E -- 异常 --> X;
        G -- 异常 --> X;
        H -- 异常 --> X;
        X --> Y[抛出异常，触发事务回滚];
    end
```