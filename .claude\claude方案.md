# BSP业绩系统跨境业绩生成数据退出点分析方案

## 1. 数据相关退出点详细分析

### 1.1 支付业绩生成数据退出点（handlerPaidAchievement方法）

#### 1.1.1 订单信息获取失败退出
**代码位置**: AchievementHandler.java:766-768  
**代码片段**: 
```java
OrderSimpleInfoResponse orderInfo = innerService.getOrderSimpleInfo(model.getOrderId());
if (null == orderInfo) {
    throw new RuntimeException(String.format("订单id:%s,获取订单信息失败", model.getOrderId()));
}
```
**退出原因**: 无法获取订单基础信息  
**数据依赖**: 订单服务的订单详情接口  
**提供数据**: 订单基础信息（订单号、客户ID、商品信息、规格信息等）给后续流程  
**默认值建议**: ⚠️ 部分可处理 - 可构造基础订单信息默认值，但需谨慎处理关键字段

#### 1.1.2 分期商品信息缺失退出
**代码位置**: AchievementHandler.java:834-836  
**代码片段**: 
```java
if (CollectionUtils.isEmpty(orderInfo.getInstallmentSpecResponseList())) {
    return;
}
```
**退出原因**: 分期订单缺少分期商品规格信息  
**数据依赖**: 订单的分期商品规格列表（InstallmentSpecResponseList）  
**提供数据**: 分期商品规格信息给业绩计算流程  
**默认值建议**: ✅ 可处理 - 可使用非分期商品规格（SpecResponseList）作为默认值

#### 1.1.3 业绩规格数据为空退出
**代码位置**: AchievementHandler.java:867-869  
**代码片段**: 
```java
if (CollectionUtils.isEmpty(specDtoList)) {
    return;
}
```
**退出原因**: 没有符合生成业绩条件的规格数据  
**数据依赖**: 
- 商品规格信息（ProductListForAchievementResponse）
- 商品服务项次数统计（specQuantityMap）
- 订单规格项信息（OrderSimpleProductSpecItemResponse）
**提供数据**: 规格维度业绩数据（AchievementSpecDetailDto）  
**默认值建议**: ✅ 可处理 - 可构造默认规格业绩数据，使用零值或基础配置

#### 1.1.4 分期商品集合为空退出
**代码位置**: AchievementHandler.java:900-902  
**代码片段**: 
```java
if (CollectionUtils.isEmpty(orderInfo.getInstallmentProductResponseList())) {
    return;
}
```
**退出原因**: 分期订单缺少分期商品信息  
**数据依赖**: 订单分期商品列表（InstallmentProductResponseList）  
**提供数据**: 分期商品基础信息给业绩计算  
**默认值建议**: ✅ 可处理 - 可使用普通商品信息（ProductResponseList）作为默认值

#### 1.1.5 订单商品集合为空退出
**代码位置**: AchievementHandler.java:921-923  
**代码片段**: 
```java
if (CollectionUtils.isEmpty(orderSimpleProductList)) {
    return;
}
```
**退出原因**: 没有匹配的订单商品信息  
**数据依赖**: 过滤后的订单商品列表  
**提供数据**: 商品基础信息给业绩计算  
**默认值建议**: ✅ 可处理 - 可构造基础商品信息，包含商品ID、名称等关键字段

#### 1.1.6 商品信息获取失败退出
**代码位置**: AchievementHandler.java:933-936  
**代码片段**: 
```java
OrderSimpleProductResponse orderSimpleProductResponse = productIdByEntityMap.get(achievementProductDetailDto.getOrderProductId());
if (null == orderSimpleProductResponse) {
    log.error("订单商品信息不存在，订单id:{},订单商品编号:{}", orderInfo.getOrderId(), achievementProductDetailDto.getOrderProductId());
    continue;
}
```
**退出原因**: 根据订单商品编号找不到对应商品信息  
**数据依赖**: 订单商品映射关系  
**提供数据**: 商品标准价、应付金额等  
**默认值建议**: ✅ 可处理 - 可使用商品基础价格或零值作为默认值

### 1.2 生产业绩生成数据退出点（newHandlerServiceAchievement方法）

#### 1.2.1 有效规格列表为空退出
**代码位置**: AchievementHandler.java:1128-1130  
**代码片段**: 
```java
if (ObjectUtil.isEmpty(specDetailModelList)) {
    return;
}
```
**退出原因**: 没有生产完成的规格单可生成业绩  
**数据依赖**: 支付完成时生成的规格业绩记录（AchievementSpecDetailModel）  
**提供数据**: 规格维度服务业绩数据  
**默认值建议**: ⚠️ 谨慎处理 - 需要基于已存在的支付业绩数据，可考虑创建零值服务业绩

#### 1.2.2 主单人分类业绩构建失败退出
**代码位置**: AchievementHandler.java:1149-1151  
**代码片段**: 
```java
if (CollectionUtils.isEmpty(newMastCategoryDetailModelList)) {
    return;
}
```
**退出原因**: 主单人分类业绩构建失败  
**数据依赖**: 
- 主单人规格业绩数据
- 分类业绩构建逻辑（AchievementCategoryWrapper）
**提供数据**: 分类维度业绩数据（AchievementCategoryDetailModel）  
**默认值建议**: ✅ 可处理 - 可构造默认分类业绩，汇总规格数据

#### 1.2.3 主单人商品业绩构建失败退出
**代码位置**: AchievementHandler.java:1153-1155  
**代码片段**: 
```java
if (CollectionUtils.isEmpty(newMastProductDetailModelList)) {
    return;
}
```
**退出原因**: 主单人商品业绩构建失败  
**数据依赖**: 
- 主单人分类业绩数据
- 商品业绩构建逻辑（AchievementProductWrapper）
**提供数据**: 商品维度业绩数据（AchievementProductDetailModel）  
**默认值建议**: ✅ 可处理 - 可构造默认商品业绩，汇总分类数据

#### 1.2.4 分单人分类业绩构建失败退出
**代码位置**: AchievementHandler.java:1167-1169  
**代码片段**: 
```java
if (CollectionUtils.isEmpty(newSubCategoryDetailModelList)) {
    return;
}
```
**退出原因**: 分单人分类业绩构建失败  
**数据依赖**: 
- 分单人规格业绩数据
- 分类业绩构建逻辑
**提供数据**: 分类维度业绩数据  
**默认值建议**: ✅ 可处理 - 可构造默认分类业绩

#### 1.2.5 分单人商品业绩构建失败退出
**代码位置**: AchievementHandler.java:1171-1173  
**代码片段**: 
```java
if (CollectionUtils.isEmpty(newSubProductDetailModelList)) {
    return;
}
```
**退出原因**: 分单人商品业绩构建失败  
**数据依赖**: 
- 分单人分类业绩数据
- 商品业绩构建逻辑
**提供数据**: 商品维度业绩数据  
**默认值建议**: ✅ 可处理 - 可构造默认商品业绩

### 1.3 数据获取相关退出点

#### 1.3.1 服务详情信息缺失
**代码位置**: AchievementHandler.java:1030-1033  
**代码片段**: 
```java
ServiceDelivery serviceDetail = innerService.getServiceDetail(orderSimpleProductResponse.getServeId(), orderInfo.getOrderSaleType());
if(null == serviceDetail){
    log.info("服务查询计费项数据为空 orderInfo：{}", orderInfo.getOrderNo());
    continue;
}
```
**退出原因**: 无法获取服务交付详情信息  
**数据依赖**: 服务交付详情接口（ServiceDelivery）  
**提供数据**: 计费项信息、服务项数量等  
**默认值建议**: ✅ 可处理 - 可构造默认服务信息，使用基础计费规则

#### 1.3.2 客户信息缺失处理
**代码位置**: AchievementHandler.java:1450-1453  
**代码片段**: 
```java
if (customer != null) {
    // 设置客户地区信息
} /*else {
    throw new RuntimeException(String.format("调用二方接口未找到客户信息,客户id:%s", achModel.getCustomerId()));
}*/
```
**退出原因**: 无法获取客户信息（已被注释，当前会继续执行）  
**数据依赖**: 客户信息查询接口  
**提供数据**: 客户省市区信息  
**默认值建议**: ✅ 可处理 - 使用默认地区信息

#### 1.3.3 商务人员信息缺失
**代码位置**: AchievementHandler.java:1459-1469  
**代码片段**: 
```java
UserInfoDetailResp business = innerService.getUserInfoDetail(ach.getBusinessId());
if (ObjectUtil.isNotEmpty(business)) {
    OrgPathInfoDTO businessOrgInfo = business.getOrgPathInfoDTO();
    if (ObjectUtil.isEmpty(businessOrgInfo)) {
        log.info("订单id：{}商务id：{}所属机构不存在", orderId, ach.getBusinessId());
    } else {
        // 设置组织信息
    }
}
```
**退出原因**: 无法获取商务人员组织架构信息  
**数据依赖**: 用户信息详情接口、组织架构信息  
**提供数据**: 公司、事业部、部门信息  
**默认值建议**: ✅ 可处理 - 使用默认组织架构信息

#### 1.3.4 合同信息获取失败
**代码位置**: AchievementHandler.java:1400-1407  
**代码片段**: 
```java
QueryContractDetailResponse contractDetail = innerService.getContractDetail(orderId);
if (contractDetail == null) {
    throw new RuntimeException(String.format("订单id:%s ,contractDetail未找到合同信息", orderId));
}
ContractDto contractDto = contractDetail.getContractDto();
if (contractDto == null) {
    throw new RuntimeException(String.format("订单id:%s ,contractDetail未找到合同信息", orderId));
}
```
**退出原因**: 无法获取合同信息  
**数据依赖**: 合同详情查询接口  
**提供数据**: 合同编号等  
**默认值建议**: ✅ 可处理 - 可使用订单号作为合同编号默认值

#### 1.3.5 商务月信息缺失
**代码位置**: AchievementHandler.java:1472-1477  
**代码片段**: 
```java
if (monthInfo != null) {
    ach.setBusinessMonth(monthInfo.getMonth());
    ach.setBusinessMonthId(monthInfo.getMonthId());
} else {
    throw new RuntimeException(String.format("根据流水创建时间未找到商务月,创建时间:%s ,订单id:%s", DateUtil.format(achModel.getStatisticsTime(), DatePattern.NORM_DATETIME_PATTERN), orderId));
}
```
**退出原因**: 无法根据时间获取商务月信息  
**数据依赖**: 商务月配置信息  
**提供数据**: 商务月ID和月份信息  
**默认值建议**: ✅ 可处理 - 可根据时间计算默认商务月信息

## 2. 数据依赖关系梳理

### 2.1 核心数据流向图
```
MqOrderPaymentInfoModel (订单支付信息)
    ↓
OrderSimpleInfoResponse (订单基础信息)
    ├── SpecResponseList (商品规格列表)
    ├── ProductResponseList (商品列表)  
    ├── InstallmentSpecResponseList (分期规格列表)
    └── InstallmentProductResponseList (分期商品列表)
    ↓
ProductListForAchievementResponse (业绩商品信息)
    ├── 商品基础信息
    ├── 政策配置信息
    └── 广告标识等
    ↓
AchievementSpecDetailDto (规格业绩DTO)
    ↓
AchievementCategoryDetailDto (分类业绩DTO - 汇总规格)
    ↓  
AchievementProductDetailDto (商品业绩DTO - 汇总分类)
    ↓
AchievementSpecDetailModel → AchievementCategoryDetailModel → AchievementProductDetailModel
```

### 2.2 关键数据依赖链
1. **订单维度**：订单ID → 订单详情 → 商品规格 → 业绩基础数据
2. **商品维度**：商品ID → 商品信息 → 政策配置 → 业绩计算规则
3. **客户维度**：客户ID → 客户信息 → 地区信息 → 业绩归属
4. **商务维度**：商务ID → 用户信息 → 组织架构 → 业绩分配
5. **时间维度**：创建时间 → 商务月 → 业绩统计周期
6. **服务维度**：服务ID → 服务详情 → 计费项 → 业绩计算
7. **合同维度**：订单ID → 合同详情 → 合同编号 → 业绩记录

### 2.3 数据依赖详情表

| 数据类型 | 来源接口/方法 | 关键字段 | 影响业绩项 | 缺失影响 |
|----------|--------------|----------|------------|----------|
| 订单信息 | innerService.getOrderSimpleInfo | orderId, customerId, orderSource | 基础业绩数据 | 高 - 无法生成业绩 |
| 商品规格 | orderInfo.getSpecResponseList | productSpecId, productId | 规格业绩 | 高 - 无规格数据 |
| 商品信息 | innerService.getProductListForAchievement | 商品配置、政策信息 | 业绩计算规则 | 高 - 无计算依据 |
| 分期信息 | orderInfo.getInstallmentSpecResponseList | 分期规格、分期金额 | 分期业绩处理 | 中 - 可用普通商品 |
| 服务信息 | innerService.getServiceDetail | 计费项、服务量 | 业绩金额计算 | 中 - 可用默认值 |
| 客户信息 | innerService.queryCustomerInfo | 省市区信息 | 地区业绩统计 | 低 - 不影响核心业绩 |
| 商务信息 | innerService.getUserInfoDetail | 组织架构信息 | 业绩归属 | 中 - 影响统计维度 |
| 合同信息 | innerService.getContractDetail | 合同编号 | 业绩记录 | 低 - 可用订单号 |
| 商务月 | businessMonthService.getMonthInfo | 商务月ID、月份 | 业绩统计周期 | 中 - 可计算默认值 |

## 3. 默认值处理策略

### 3.1 推荐实施的默认值策略

#### 3.1.1 高优先级（立即实施）
| 数据项 | 默认值策略 | 实现方式 | 风险等级 |
|--------|------------|----------|----------|
| 分期商品信息 | 使用普通商品信息 | 当InstallmentProductResponseList为空时，使用ProductResponseList | 低 |
| 分期规格信息 | 使用普通规格信息 | 当InstallmentSpecResponseList为空时，使用SpecResponseList | 低 |
| 客户地区信息 | 默认地区 | 设置默认省市区编码和名称 | 低 |
| 服务计费信息 | 基础计费规则 | 构造默认ServiceDelivery对象 | 低 |

#### 3.1.2 中优先级（谨慎实施）
| 数据项 | 默认值策略 | 实现方式 | 风险等级 |
|--------|------------|----------|----------|
| 商务组织信息 | 默认部门 | 设置系统默认公司、事业部、部门信息 | 中 |
| 合同编号 | 订单号 | contractNo = orderNo | 中 |
| 商务月信息 | 时间计算 | 根据创建时间计算对应的商务月 | 中 |
| 商品业绩数据 | 零值记录 | 创建金额为0的业绩记录保证数据完整性 | 中 |

#### 3.1.3 低优先级（特殊场景）
| 数据项 | 默认值策略 | 实现方式 | 风险等级 |
|--------|------------|----------|----------|
| 规格业绩DTO | 基础结构 | 构造包含基本字段的AchievementSpecDetailDto | 高 |
| 订单基础信息 | 最小集合 | 仅在关键数据缺失时构造最小订单信息 | 高 |

### 3.2 默认值配置建议

#### 3.2.1 配置化默认值
```java
// 默认地区信息
public static final String DEFAULT_PROVINCE_CODE = "000000";
public static final String DEFAULT_PROVINCE_NAME = "未知省份";
public static final String DEFAULT_CITY_CODE = "000000";
public static final String DEFAULT_CITY_NAME = "未知城市";

// 默认组织信息
public static final Long DEFAULT_COMPANY_ID = 0L;
public static final String DEFAULT_COMPANY_NAME = "默认公司";
public static final Long DEFAULT_DIVISION_ID = 0L;
public static final String DEFAULT_DIVISION_NAME = "默认事业部";

// 默认服务信息
public static final int DEFAULT_ITEM_NUM = 1;
public static final String DEFAULT_ITEM_UNIT = "个";
```

#### 3.2.2 开关控制
```java
// 是否启用默认值处理
@Value("${achievement.default.enabled:true}")
private boolean defaultValueEnabled;

// 具体功能开关
@Value("${achievement.default.customer-info:true}")
private boolean defaultCustomerInfo;

@Value("${achievement.default.business-org:false}")  
private boolean defaultBusinessOrg;
```

## 4. 数据流程优化方案

### 4.1 支付业绩生成流程优化

```mermaid
graph TD
    A[processAchievement] --> B[handelCrossBorderTask]
    B --> C[handlerPaidAchievement]
    
    C --> D{获取订单信息}
    D -->|成功| E[获取商品规格信息]
    D -->|失败| D1[记录异常+构造基础订单]
    D1 --> E
    
    E --> F[处理分期信息]
    F --> F1{分期数据检查}
    F1 -->|完整| G[使用分期数据]
    F1 -->|缺失| F2[使用普通商品数据]
    
    F2 --> G
    G --> H[生成规格业绩DTO]
    H --> H1{规格数据检查}
    H1 -->|有数据| I[继续处理]
    H1 -->|无数据| H2[构造默认规格数据]
    H2 --> I
    
    I --> J[商品信息处理]
    J --> J1{商品数据检查}
    J1 -->|完整| K[正常处理]
    J1 -->|缺失| J2[使用默认商品信息]
    J2 --> K
    
    K --> L[补充辅助信息]
    L --> L1[客户信息]
    L1 --> L2[商务信息]
    L2 --> L3[合同信息]
    L3 --> L4[商务月信息]
    L4 --> M[计算并保存业绩]
    
    style D1 fill:#fff2cc
    style F2 fill:#fff2cc
    style H2 fill:#fff2cc
    style J2 fill:#fff2cc
```

### 4.2 生产业绩生成流程优化

```mermaid
graph TD
    A[newHandlerServiceAchievement] --> B{获取有效规格列表}
    B -->|有数据| C[处理主单人数据]
    B -->|无数据| B1[查找支付业绩数据]
    B1 --> B2[构造服务业绩数据]
    B2 --> C
    
    C --> D[构建规格业绩]
    D --> E{分类业绩构建}
    E -->|成功| F[构建商品业绩]
    E -->|失败| E1[构造默认分类业绩]
    E1 --> F
    
    F --> F1{商品业绩构建}
    F1 -->|成功| G[处理分单人数据]
    F1 -->|失败| F2[构造默认商品业绩]
    F2 --> G
    
    G --> H[汇总所有业绩数据]
    H --> I[保存业绩数据]
    
    style B1 fill:#fff2cc
    style B2 fill:#fff2cc
    style E1 fill:#fff2cc
    style F2 fill:#fff2cc
```