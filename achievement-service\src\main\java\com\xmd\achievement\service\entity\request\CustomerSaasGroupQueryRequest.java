package com.xmd.achievement.service.entity.request;

import javax.validation.constraints.Min;

import com.xmd.achievement.service.entity.page.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class CustomerSaasGroupQueryRequest extends PageRequest {
    
    @Schema(description = "开始商务月，格式YYYYMM")
    private String startMonth;
    
    @Schema(description = "结束商务月，格式YYYYMM")
    private String endMonth;
    
    @Schema(description = "客户ID，精确匹配")
    private String customerId;
    
    @Schema(description = "客户名称，模糊查询")
    private String customerName;
    
    @Schema(description = "商务ID，精确匹配")
    private String businessId;
    
    @Schema(description = "商务名称，模糊查询")
    private String businessName;
}