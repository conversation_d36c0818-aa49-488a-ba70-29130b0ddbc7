# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Build and Development Commands

### Build & Package
- **Build entire project**: `mvn clean package`
- **Build specific module**: `mvn clean package -pl achievement-service`
- **Skip tests**: `mvn clean package -DskipTests=true`

### Application Startup
- **Start application**: `./achievement-service/src/main/assembly/bin/start.sh [profile]`
- **Stop application**: `./achievement-service/src/main/assembly/bin/stop.sh`
- **Run in IDE**: Main class is `com.xmd.achievement.BspAchievementServiceApplication`
- **Default port**: 8016
- **Context path**: `/achievement-web`

### Testing
- **Run tests**: `mvn test`
- **Run single test**: `mvn test -Dtest=ClassName`
- Note: Tests are currently skipped in maven-surefire-plugin configuration

### Profiles
Available profiles: `dev`, `test`, `pre`, `release`
- Configure via: `--spring.profiles.active=dev`

## Project Architecture

### Multi-Module Maven Structure
- **achievement-service**: Main Spring Boot application with REST controllers, business logic, and job handlers
- **achievement-dao**: Data access layer with MyBatis Plus entities and mappers  
- **achievement-cache**: Redis caching layer
- **achievement-util**: Common utilities and helpers
- **achievement-support**: Supporting components and configurations

### Key Technology Stack
- **Framework**: Spring Boot 2.3.6, Spring 5.2.11
- **Database**: MySQL 8.0.29 with Druid connection pool
- **ORM**: MyBatis Plus 3.5.2
- **Cache**: Redis with Redisson 3.17.7
- **Message Queue**: RocketMQ 2.0.3
- **Job Scheduling**: XXL-Job 1.0.11
- **Monitoring**: CAT 3.0.0
- **Documentation**: Knife4j (Swagger) 4.3.0

### Package Structure
- **Controllers**: `com.xmd.achievement.web.controller.*`
- **Services**: `com.xmd.achievement.service.*`
- **Entities**: `com.xmd.achievement.dao.entity.*`
- **Job Handlers**: `com.xmd.achievement.async.job.handler.*`
- **MQ Consumers**: `com.xmd.achievement.async.mq.consumer.*`
- **Business Handlers**: `com.xmd.achievement.handler.*`

### Key Business Components
- **Achievement Calculation**: Complex business logic for performance calculations with specialized handlers
- **Customer Management**: New/old customer classification and SaaS customer tracking
- **Export Services**: Performance data export functionality
- **Job Processing**: Scheduled tasks for data synchronization and reports
- **Message Queue Integration**: Order payment and service completion processing
- **Organization Reporting**: Daily and monthly performance reports

### Database
- **Mapper Scan**: `com.xmd.achievement.dao.mapper`
- **Entity Package**: `com.xmd.achievement.dao.entity`
- **Mapper XML**: `classpath:mapper/*Mapper.xml`

### Configuration Notes
- Application uses profile-specific YAML files (`application-{profile}.yml`)
- MyBatis Plus configuration includes automatic fill handlers
- Redis cluster configuration with connection pooling
- RocketMQ producer/consumer setup with retry mechanisms