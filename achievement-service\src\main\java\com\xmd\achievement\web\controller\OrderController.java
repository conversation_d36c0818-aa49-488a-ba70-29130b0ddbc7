package com.xmd.achievement.web.controller;

import cn.hutool.json.JSONUtil;
import com.xmd.achievement.service.IOrderService;
import com.xmd.achievement.service.entity.request.ManualOrderRequest;
import com.xmd.achievement.service.entity.response.ManualOrderExcelResponse;
import com.xmd.achievement.service.entity.response.ManualOrderResponse;
import com.xmd.achievement.service.entity.response.ManualOrderWrapperResponse;
import com.xmd.achievement.web.entity.response.WebResult;
import com.xxl.job.core.biz.model.ReturnT;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */

@Tag(name = "订单数据处理")
@Slf4j
@RestController
@RequestMapping("order")
public class OrderController {

    @Resource
    private IOrderService orderService;

    @Operation(summary = "手动订单处理")
    @PostMapping("manualOrder")
    public WebResult<ManualOrderWrapperResponse> manualOrder(@RequestBody @Valid ManualOrderRequest request) {
        return WebResult.success(orderService.manualOrder(request));
    }

    @Operation(summary = "导入手动订单Excel")
    @PostMapping("importManualOrderExcel")
    public WebResult<List<ManualOrderExcelResponse>> importManualOrderExcel(@RequestParam("file") MultipartFile file) {
        return WebResult.success(orderService.manualOrderExcel(file));
    }

    @Operation(summary = "导出失败数据Excel")
    @GetMapping("exportFailDataExcel")
    public void exportFailDataExcel(@RequestParam("failFileCode") String failFileCode, HttpServletResponse response) {
        orderService.exportFailDataExcel(failFileCode, response);
    }

    @Operation(summary = "修复MqOrderPaymentInfo表中缺失的orderProductId")
    @PostMapping("/fixMissingOrderProductId")
    public WebResult<String> fixMissingOrderProductId() {
        orderService.fixMissingOrderProductId();
        return WebResult.success("修复任务已完成，请查看日志获取详细信息");
    }

    @Operation(summary = "获取修复orderProductId的进度状态")
    @GetMapping("/getFixOrderProductIdProgress")
    public WebResult<String> getFixOrderProductIdProgress() {
        return WebResult.success(orderService.getFixOrderProductIdProgress());
    }

    @Operation(summary = "清除修复orderProductId的进度状态")
    @PostMapping("/clearFixOrderProductIdProgress")
    public WebResult<Boolean> clearFixOrderProductIdProgress() {
        return WebResult.success(orderService.clearFixOrderProductIdProgress());
    }
}
