package com.xmd.achievement.service.entity.response;

import lombok.Data;

import java.util.List;

/**
 * 手动订单处理包装响应对象
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Data
public class ManualOrderWrapperResponse {
    
    private List<ManualOrderResponse> dataList;
    
    private List<ManualOrderResponse> exceptionList;
    
    private Integer failCount;
    
    private String failFileCode;
    
    private Integer successCount;
    
    private Integer totalCount;
    
    public ManualOrderWrapperResponse() {
        this.failCount = 0;
        this.successCount = 0;
        this.totalCount = 0;
    }
}