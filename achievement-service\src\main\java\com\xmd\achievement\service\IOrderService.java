package com.xmd.achievement.service;

import com.xmd.achievement.service.entity.request.ManualOrderRequest;
import com.xmd.achievement.service.entity.response.ManualOrderExcelResponse;
import com.xmd.achievement.service.entity.response.ManualOrderResponse;
import com.xmd.achievement.service.entity.response.ManualOrderWrapperResponse;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.List;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
public interface IOrderService {
    ManualOrderWrapperResponse manualOrder(@Valid ManualOrderRequest request);

    List<ManualOrderExcelResponse> manualOrderExcel(MultipartFile file);

    /**
     * 导出失败数据Excel
     */
    void exportFailDataExcel(String failFileCode, javax.servlet.http.HttpServletResponse response);

    /**
     * 修复MqOrderPaymentInfo表中缺失的orderProductId
     */
    void fixMissingOrderProductId();

    /**
     * 获取修复orderProductId的进度状态
     */
    String getFixOrderProductIdProgress();

    /**
     * 清除修复orderProductId的进度状态
     */
    boolean clearFixOrderProductIdProgress();
}
