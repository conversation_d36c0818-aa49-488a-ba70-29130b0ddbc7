server:
  port: 8016
spring:
  datasource:
    url: jdbc:mysql://***********:3306/bsp_achievement?useSSL=false&useUnicode=true&characterEncoding=UTF-8&allowMultiQueries=true&useJDBCCompliantTimezoneShift=true&useLegacyDatetimeCode=false&serverTimezone=Hongkong
    username: tduser
    password: PKPJtg5ppyAWHQK
  redis:
    password: epRedis@019
    cluster:
      nodes: redis.kt:7000,redis.kt:7001,redis.kt:7002
  servlet:
    multipart:
      max-file-size: 5MB
      max-request-size: 5MB

rocketmq:
  name-server: mq.kt:9876
  producer:
    access-key: mqkjuser
    secret-key: rMwtnGKelNMACmNd
    group: insight_tools_group
  consumer:
    accessKey: mqkjuser
    secretKey: rMwtnGKelNMACmNd

xxl:
  job:
    accessToken:
    admin:
      #调度中心地址
      addresses: http://************:8880/xxl-job-admin/
    executor:
      port: 9016
      appname: bsp-achievement
      address:
      ip:
      logpath: /data/applogs/xxl-job/jobhandler
      logretentiondays: 3
      oneTimesJob:
        timeout: 10000

mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl


#打印sql日志，开发环境使用
logging:
  level:
    com.xmd.achievement.dao.mapper: debug
  config: classpath:logback-pre.xml

mq:
  topic:
    orderChangeStatus: CBO_ORDER_CHANGE_ORDER_STATUS_TOPIC
    serveInProgress: BSP_SERVE_IN_SERVICE_NOTIFY_TOPIC
    serveFinishTime: BSP_SERVE_TIME_CHARGING_NOTIFY_TOPIC
    orderRefund: CBO_AFTER_SALES_ORDER_REFUND_TOPIC
    orderTransferFund: CBO_AFTER_SALES_ORDER_STATUS_CHANGE_TOPIC
    orderRefundCallback: CBO_AFTER_SALES_ORDER_REFUND_RETURN_TOPIC
    orderTransferFundCallback: CBO_AFTER_SALES_ORDER_STATUS_CHANGE_RETURN_TOPIC
  group:
    orderChangeStatus: BSP_ACHIEVEMENT_ORDER_CHANGE_ORDER_STATUS_GROUP
    serveInProgress: BSP_ACHIEVEMENT_IN_SERVICE_NOTIFY_TOPIC_GROUP
    serveFinishTime: BSP_ACHIEVEMENT_FINISH_TIME_NOTIFY_TOPIC_GROUP
    orderRefund: CBO_ACHIEVEMENT_AFTER_SALES_ORDER_REFUND_TOPIC_GROUP
    orderTransferFund: CBO_ACHIEVEMENT_AFTER_SALES_ORDER_STATUS_CHANGE_TOPIC_GROUP
    orderRefundCallback: CBO_AFTER_SALES_ORDER_REFUND_RETURN_TOPIC_GROUP
    orderTransferFundCallback: CBO_AFTER_SALES_ORDER_STATUS_CHANGE_RETURN_TOPIC_GROUP

#公私钥管理
ppk:
  #公私钥对管理
  test:
    #持有私钥
    private_key:
    #持有公钥
    public_key:

#http调用的url管理
third-url:
  # 管理
  bspManagement:
    verifyUserInfo: http://************/management-web/api/user/verifyUserInfo
    queryUserInfoDetail: http://************/management-web/api/user/queryUserInfoDetail
    queryCustomerInfo: http://************/management-web/api/crm/queryCustomerInfo
    queryKjOrgTree: http://************/management-web/api/org/queryKjOrgTree
    queryListOrg: http://************/management-web/api/org/queryListOrg
    pageQueryKjListOrg: http://************/management-web/api/org/pageQueryKjListOrg
    queryLeveRelation: http://************/management-web/api/org/queryLeveRelation
    queryOrgFunctionList: http://************/management-web/api/org/getOrgFunctionList
    getOrgFunctionById: http://************/management-web/api/org/getOrgFunctionById
    getOrgBusiness: http://************/management-web/api/org/getOrgBusiness
    getCustomerLossDateList: http://************/management-web/api/crm/getCustomerLossDateList
    sendWxMessage: http://************/management-web/api/enp/sendWxMessage
  contractManagement:
    queryContractDetail: http://************/contract-web/api/contract/queryContractDetail
  orderManagement:
    simpleOrderDetail: http://************/order-web/api/order/inner/simpleOrderDetail
    specStatistics: http://************/order-web/api/order/inner/specStatistics
  itemManagement:
    productsForAchievement: http://************/item-web/api/item/getProductsForAchievement
    getProductDiscountRulesListForAchievement: http://************/item-web/api/item/getProductDiscountRulesListForAchievement
    specDetail: http://************/item-web/api/item/getSpecListByProductIdForPromotion
    allProductForAchievement: http://************/item-web/api/item/getAllProductForAchievement
    getProductListForOrder: http://************/item-web/api/item/getProductListForOrder
  crmManagement:
    getCustomerInfoByCondition: https://test-scrm.ceboss.cn/scrm-web/cbo/getCustomerInfoByCondition
    getProtectByCustomerId: https://test-scrm.ceboss.cn/scrm-web/cbo/getProtectByCustomerId
    getCustomerLossDateListCrm: https://test-scrm.ceboss.cn/scrm-web/cbo/getCustomerLossDateList
    secretKey: 31b9763b659c49489d348c2e6a261a64
  adManagement:
    queryCustomerRechargeTime: https://pre-wb.gboss.tech/cbo-wb-ads/inner/queryCustomerRechargeTime
    queryCustomerAllRechargeTime: https://pre-wb.gboss.tech/cbo-wb-ads/inner/queryCustomerAllRechargeTime
  serveManagement:
    getServeList: https://pre-serve.gboss.tech/serve-web/api/internal/list
    getConfigList: https://pre-serve.gboss.tech/serve-web/api/internal/getConfigList
    getServerDetail: https://pre-serve.gboss.tech/serve-web/api/internal/getServeDetail
  ehrManagement:
    empUrl: http://pre-emp.gboss.tech/emp-web
    orgInfoById: /org/getOrgInfoById
  refundOrderManagement:
    queryAfterSalesOrderInfo: http://************/refund-order-web/api/after-sales-order/info
    queryAfterSalesOrderDetail: http://************/refund-order-web/api/after-sales-order/detail

robot:
  force-alarm-address: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=d1a1cc67-e9a5-4dba-99c4-7281cf729d12

#特殊商品规格政策成本
special-products:
  items:
    - id: 1871743279815249920
      name: "SaaS核心"
  specs:
    - id: 1871745445942579200
      name: "全球数字门户SaaS核心"
prepaid-charge-category:
  id: 1869990744779730944
product-achievement:
  website: 1871743279815249920
  websiteName: "数字门户-产品"
  advertisement: 1869990399835975680,1919666360067833856
  specId: 1869991154940719104,1869991225572798464,1869991293222727680,1869991354887385088,1869991424772878336,1869991475469430784,1919667601351131136,1919667511672717312
# 提成减半的广告通规格分类
half-commission:
  product-ids:
    - 1869990399835975680
    - 1897232718486085632
  category-ids:
    - 1869990769454821376
    - 1897233138948284416