package com.xmd.achievement.service.impl;


import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xmd.achievement.dao.entity.AchievementPolicyModel;
import com.xmd.achievement.dao.entity.AchievementProductDetailModel;
import com.xmd.achievement.dao.entity.BaseModel;
import com.xmd.achievement.dao.entity.BusinessMonthModel;
import com.xmd.achievement.dao.entity.MqOrderPaymentInfoModel;
import com.xmd.achievement.dao.entity.MqOrderRefundInfoModel;
import com.xmd.achievement.dao.repository.IAchievementPolicyRepository;
import com.xmd.achievement.dao.repository.IAchievementProductDetailRepository;
import com.xmd.achievement.dao.repository.IMqOrderPaymentInfoRepository;
import com.xmd.achievement.dao.repository.IMqOrderRefundInfoRepository;
import com.xmd.achievement.rpc.InnerService;
import com.xmd.achievement.rpc.entity.dto.InstallmentDetailResponse;
import com.xmd.achievement.rpc.entity.dto.OrderSimpleInfoResponse;
import com.xmd.achievement.rpc.entity.dto.OrderSimpleProductResponse;
import com.xmd.achievement.service.IAchievementSpecDetailService;
import com.xmd.achievement.service.IBusinessMonthService;
import com.xmd.achievement.service.IFreezeMonthErrorLogService;
import com.xmd.achievement.service.IOrderService;
import com.xmd.achievement.service.entity.dto.ManualOrderDto;
import com.xmd.achievement.service.entity.request.ManualOrderRequest;
import com.xmd.achievement.service.entity.response.ManualOrderExcelResponse;
import com.xmd.achievement.service.entity.response.ManualOrderResponse;
import com.xmd.achievement.service.entity.response.ManualOrderWrapperResponse;
import com.xmd.achievement.support.constant.NumberConstants;
import com.xmd.achievement.support.constant.enums.AchStatus;
import com.xmd.achievement.support.constant.enums.AchievementSourceEnum;
import com.xmd.achievement.support.constant.enums.DeleteFlagEnum;
import com.xmd.achievement.support.constant.enums.OrderSaleTypeEnum;
import com.xmd.achievement.support.constant.enums.PayStatusEnum;
import com.xmd.achievement.support.constant.enums.PayTypeEnum;
import com.xmd.achievement.support.constant.enums.TaskStatusEnum;
import com.xmd.achievement.support.constant.enums.TaskTypeEnum;
import com.xmd.achievement.support.constant.exceptions.BusinessException;
import com.xmd.achievement.util.enums.CalculateTypeEnum;
import com.xmd.achievement.web.entity.response.WebCodeMessageEnum;
import com.xmd.achievement.web.util.UserContext;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import cn.idev.excel.EasyExcel;
import cn.idev.excel.context.AnalysisContext;
import cn.idev.excel.read.listener.ReadListener;
import cn.idev.excel.write.metadata.WriteSheet;
import lombok.extern.slf4j.Slf4j;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */

@Slf4j
@Service
public class OrderServiceImpl implements IOrderService {
    @Resource
    private InnerService innerService;

    @Resource
    IMqOrderPaymentInfoRepository mqOrderPaymentRepository;

    @Resource
    IMqOrderRefundInfoRepository mqOrderRefundInfoRepository;

    @Resource
    private IAchievementSpecDetailService achievementSpecService;

    @Resource
    private IAchievementProductDetailRepository achievementProductDetailRepository;

    @Resource
    private IBusinessMonthService businessMonthService;

    @Resource
    private IFreezeMonthErrorLogService freezeMonthErrorLogService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private IAchievementPolicyRepository achievementPolicyRepository;

    private final static String EXECUTE_SUCCESS = "success";
    private final static String EXECUTE_FAIL = "fail";
    private static final String REDIS_KEY_FIX_ORDER_PRODUCT_ID = "order:fix_order_product_id:progress";
    private static final String REDIS_KEY_FAIL_DATA_PREFIX = "order:fail_data:";
    private static final int REDIS_EXPIRE_DAYS = 1;
    private static final int REDIS_FAIL_DATA_EXPIRE_HOURS = 2;
    private static final int PAGE_SIZE = 1000;

    private static final String[] EXCEL_MIME_TYPES = {
            "application/vnd.ms-excel", // .xls
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" // .xlsx
    };

    // 允许的文件扩展名
    private static final String[] EXCEL_EXTENSIONS = {
            "xls",
            "xlsx"
    };

    private final Lock lock=new ReentrantLock(true);
    @Override
    public ManualOrderWrapperResponse manualOrder(ManualOrderRequest request) {
        List<ManualOrderDto> manualOrders = request.getManualOrders();
        manualOrders = new ArrayList<>(manualOrders.stream()
                .collect(Collectors.toMap(
                        dto -> Arrays.asList(dto.getOrderProductCode(), dto.getOrderNo()),
                        Function.identity(),
                        (existing, replacement) -> existing
                ))
                .values());
        
        List<ManualOrderResponse> results = new ArrayList<>(); 
        List<ManualOrderResponse> exceptionList = new ArrayList<>();
        
        if (CollectionUtils.isEmpty(manualOrders)){
            // 即使没有数据，也生成failFileCode
            String failFileCode = generateFailFileCode();
            storeFailDataToRedis(failFileCode, results);
            return buildWrapperResponse(results, exceptionList, failFileCode);
        }
        
        lock.lock();
        try {
            manualOrders.forEach(manualOrder -> {
                ManualOrderResponse result = initResult(manualOrder);
                orderHandler(manualOrder,result);
                results.add(result);
                
                if (EXECUTE_FAIL.equals(result.getIsSuccess())) {
                    exceptionList.add(result);
                }
            });
        }catch (Exception e){
            log.error("手动结算订单业绩失败");
            log.error(e.getMessage());
        }finally {
            lock.unlock();
        }
        
        // 总是生成failFileCode并存储数据到Redis
        String failFileCode = generateFailFileCode();
        storeFailDataToRedis(failFileCode, results);
        
        return buildWrapperResponse(results, exceptionList, failFileCode);
    }

    public void orderHandler(ManualOrderDto manualOrder, ManualOrderResponse result) {
        BusinessMonthModel monthInfo = businessMonthService.getMonthInfo(DateUtil.parse(manualOrder.getOrderTime()));
        if (businessMonthService.isMonthFrozen(monthInfo)) {
            freezeMonthErrorLogService.add(monthInfo.getMonthId(), manualOrder);
            result.setMessage(WebCodeMessageEnum.BUSINESS_MONTH_FROZEN_ERROR.getMsg());
            result.setIsSuccess(EXECUTE_FAIL);
            return;
        }
        OrderSimpleInfoResponse orderSimple = rpcData(manualOrder);
        if (Objects.isNull(orderSimple)) {
            result.setMessage(WebCodeMessageEnum.MANUAL_ORDER_NOT_FOUND.getMsg());
            result.setIsSuccess(EXECUTE_FAIL);
            return;
        }
        List<OrderSimpleProductResponse> products = orderSimple.getProductResponseList();
        if (CollectionUtils.isEmpty(products)) {
            result.setMessage(WebCodeMessageEnum.MANUAL_ORDER_PRODUCT_LIST_EMPTY.getMsg());
            result.setIsSuccess(EXECUTE_FAIL);
            return;
        }
        OrderSimpleProductResponse info = products.stream()
                .filter(p -> manualOrder.getOrderProductCode().equals(p.getOrderProductCode()))
                .findFirst().orElse(null);
        if (Objects.isNull(info)) {
            result.setMessage(WebCodeMessageEnum.MANUAL_ORDER_SPECIFIED_PRODUCT_NOT_FOUND.getMsg());
            result.setIsSuccess(EXECUTE_FAIL);
            return;
        }
        if (!achievementPolicyCheck(info, result)) {
            return;
        }
        if (!refundCheck(orderSimple, info, result)) {
            return;
        }
        if (!payCheck(orderSimple, info, result)) {
            return;
        }
        if (!achievementProductDetailCheck(info.getOrderId(), info.getProductId(), result)) {
            return;
        }
        MqOrderPaymentInfoModel model = getInitModel(orderSimple, info, manualOrder);
        if (PayTypeEnum.ALL.getCode().equals(orderSimple.getPayType())) {
            //非分期
            nonInstallmentHandler(model, result);
        } else {
            //分期
            installmentHandler(model, orderSimple, result);
        }
    }

    private boolean achievementPolicyCheck(OrderSimpleProductResponse info, ManualOrderResponse result) {
        AchievementPolicyModel policy = achievementPolicyRepository.lambdaQuery()
                .eq(AchievementPolicyModel::getProductId, info.getProductId())
                .eq(AchievementPolicyModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                .one();

        if (Objects.isNull(policy)) {
            result.setMessage(WebCodeMessageEnum.MANUAL_ORDER_POLICY_NOT_FOUND.getMsg());
            result.setIsSuccess(EXECUTE_FAIL);
            return false;
        }
        if (!Integer.valueOf(2).equals(policy.getRevenueNode())) {
            result.setMessage(WebCodeMessageEnum.MANUAL_ORDER_REVENUE_NODE_INVALID.getMsg());
            result.setIsSuccess(EXECUTE_FAIL);
            return false;
        }
        return true;
    }

    private void installmentHandler(MqOrderPaymentInfoModel model,OrderSimpleInfoResponse orderSimple,ManualOrderResponse result){
        model.setInstallmentStatus(NumberConstants.INTEGER_VALUE_2);
        List<InstallmentDetailResponse> installs = orderSimple.getInstallmentDetailResponseList();
        int failNum=0;
        for (int i = 1; i <= installs.size(); i++) {
            MqOrderPaymentInfoModel save = new MqOrderPaymentInfoModel();
            BeanUtils.copyProperties(model,save);
            save.setInstallmentNum(i);
            if (mqOrderPaymentInfoHasCheck(save)){
                failNum++;
                continue;
            }
            mqOrderPaymentRepository.save(save);
        }
        if (failNum==installs.size()){
            result.setMessage(WebCodeMessageEnum.MANUAL_ORDER_TASK_EXISTS.getMsg());
            result.setIsSuccess(EXECUTE_FAIL);
        }
    }

    private void nonInstallmentHandler(MqOrderPaymentInfoModel model,ManualOrderResponse result){
        model.setInstallmentStatus(NumberConstants.INTEGER_VALUE_1);
        model.setInstallmentNum(NumberConstants.INTEGER_VALUE_0);
        if (!mqOrderPaymentInfoCheck(model,result)){
            return;
        }
        mqOrderPaymentRepository.save(model);
    }

    private boolean isSuccessPayStatus(Integer payStatus) {
        return PayStatusEnum.FINISH_PAY.getCode().equals(payStatus)
                || PayStatusEnum.PARTIAL_REFUND.getCode().equals(payStatus)
                || PayStatusEnum.REFUNDED.getCode().equals(payStatus)
                || PayStatusEnum.PARTIAL_PAY.getCode().equals(payStatus);
    }

    private boolean refundCheck(OrderSimpleInfoResponse orderSimple, OrderSimpleProductResponse info, ManualOrderResponse result){
        // 检查退款消息表
        Long refundCount = mqOrderRefundInfoRepository.lambdaQuery()
                .eq(MqOrderRefundInfoModel::getOrderId, orderSimple.getOrderId())
                .eq(MqOrderRefundInfoModel::getTaskStatus, TaskStatusEnum.YES.getCode())
                .eq(MqOrderRefundInfoModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                .eq(MqOrderRefundInfoModel::getTaskType, TaskTypeEnum.ADD.getMsg())
                .in(MqOrderRefundInfoModel::getSaleType, OrderSaleTypeEnum.TRANSFER.getType(), OrderSaleTypeEnum.REFUND.getType())
                .count();
        if (refundCount > 0) {
            result.setMessage(WebCodeMessageEnum.MANUAL_ORDER_HAS_REFUND.getMsg());
            result.setIsSuccess(EXECUTE_FAIL);
            return false;
        }
        
        // 检查商品明细表是否有退款状态
        Long productRefundCount = achievementProductDetailRepository.lambdaQuery()
                .eq(AchievementProductDetailModel::getOrderProductId, info.getOrderProductCode())
                .eq(AchievementProductDetailModel::getStatus, AchStatus.REFUND.getType())
                .eq(AchievementProductDetailModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                .count();
        if (productRefundCount > 0) {
            result.setMessage(WebCodeMessageEnum.MANUAL_ORDER_PRODUCT_HAS_REFUND.getMsg());
            result.setIsSuccess(EXECUTE_FAIL);
            return false;
        }
        
        return true;
    }

    private boolean payCheck(OrderSimpleInfoResponse orderSimple,OrderSimpleProductResponse info,ManualOrderResponse result){
        if (PayTypeEnum.ALL.getCode().equals(orderSimple.getPayType())){
            //非分期
            if (!isSuccessPayStatus(orderSimple.getPayStatus())){
                result.setMessage(WebCodeMessageEnum.MANUAL_ORDER_PAY_STATUS_INVALID.getMsg());
                result.setIsSuccess(EXECUTE_FAIL);
                return false;
            }
        }else {
          //分期
            if (Objects.isNull(info.getPayableAmount())){
                return true;
            }
            if (info.getPayableAmount().compareTo(info.getPaidAmount())!=0){
                result.setMessage(WebCodeMessageEnum.MANUAL_ORDER_INSTALLMENT_NOT_FULLY_PAID.getMsg());
                result.setIsSuccess(EXECUTE_FAIL);
                return false;
            }
        }
        return true;
    }

    private boolean mqOrderPaymentInfoCheck(MqOrderPaymentInfoModel model,ManualOrderResponse result){
        if (mqOrderPaymentInfoHasCheck(model)){
            result.setMessage(WebCodeMessageEnum.MANUAL_ORDER_TASK_EXISTS.getMsg());
            result.setIsSuccess(EXECUTE_FAIL);
            return false;
        }
        return true;
    }

    private boolean mqOrderPaymentInfoHasCheck(MqOrderPaymentInfoModel model){
        List<MqOrderPaymentInfoModel> list = mqOrderPaymentRepository.list(new LambdaQueryWrapper<MqOrderPaymentInfoModel>()
                .eq(MqOrderPaymentInfoModel::getOrderId, model.getOrderId())
                .eq(MqOrderPaymentInfoModel::getOrderProductId, model.getOrderProductId())
                .eq(MqOrderPaymentInfoModel::getInstallmentNum, model.getInstallmentNum())
                .eq(MqOrderPaymentInfoModel::getDeleteFlag, NumberConstants.INTEGER_VALUE_0)
                .eq(MqOrderPaymentInfoModel::getProductId, model.getProductId())
                .eq(MqOrderPaymentInfoModel::getCalculateType, model.getCalculateType())
                .in(MqOrderPaymentInfoModel::getTaskStatus, TaskStatusEnum.NO.getCode(), TaskStatusEnum.YES.getCode())
        );
        return !CollectionUtils.isEmpty(list);
    }

    private MqOrderPaymentInfoModel getInitModel(OrderSimpleInfoResponse orderSimple,OrderSimpleProductResponse info,ManualOrderDto manualOrder){
        MqOrderPaymentInfoModel model = new MqOrderPaymentInfoModel();
        model.setOrderId(info.getOrderId());
        model.setProductId(info.getProductId());
        model.setCustomerId(orderSimple.getCustomerId());
        model.setTaskStatus(TaskStatusEnum.NO.getCode().toString());
        model.setTaskId(IdUtil.getSnowflake().nextId());
        model.setTaskType(TaskTypeEnum.ADD.getMsg());
        model.setAchievementSource(AchievementSourceEnum.KUAJINFG.getCode());
        model.setCreateTime(DateUtil.parse(manualOrder.getOrderTime()));
        model.setCalculateType(CalculateTypeEnum.SERVEINPROGRESS.getCode());
        model.setOrderProductId(info.getOrderProductCode());
        return model;
    }


    private OrderSimpleInfoResponse rpcData(ManualOrderDto manualOrder){
        return innerService.getOrderSimpleInfoByOrderNo(manualOrder.getOrderNo());
    }

    private ManualOrderResponse initResult(ManualOrderDto manualOrder){
        return new ManualOrderResponse(manualOrder.getOrderNo(), manualOrder.getOrderProductCode(),manualOrder.getOrderTime(),EXECUTE_SUCCESS);
    }

    private boolean achievementProductDetailCheck(Long orderId, Long productId, ManualOrderResponse result){
        if (Objects.isNull(productId)||Objects.isNull(orderId)){
            result.setMessage("订单ID或产品ID为空");
            result.setIsSuccess(EXECUTE_FAIL);
            return false;
        }
        List<AchievementProductDetailModel> list = achievementProductDetailRepository.list(
                new LambdaQueryWrapper<AchievementProductDetailModel>()
                        .eq(AchievementProductDetailModel::getOrderId, orderId)
                        .eq(AchievementProductDetailModel::getProductId, productId)
                        .eq(BaseModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode()));

        if (ObjectUtil.isEmpty(list)){
            result.setMessage(WebCodeMessageEnum.MANUAL_ORDER_PRODUCT_DETAIL_NOT_FOUND.getMsg());
            result.setIsSuccess(EXECUTE_FAIL);
            return false;
        }
        return true;
    }

    @Override
    public List<ManualOrderExcelResponse> manualOrderExcel(MultipartFile file) {
        try {
            if (!excelCheck(file)){
                throw new BusinessException("文件不合规");
            }
            return parse(file);
        }catch (IOException e){
            throw new BusinessException("解析异常");
        }
    }

    public  List<ManualOrderExcelResponse> parse(MultipartFile file) throws IOException {
        List<ManualOrderExcelResponse> dataList = new ArrayList<>();

        EasyExcel.read(file.getInputStream(), ManualOrderExcelResponse.class, new ReadListener<ManualOrderExcelResponse>() {
                    // 每读取一批数据会调用此方法（默认每100条）
                    @Override
                    public void invoke(ManualOrderExcelResponse data, AnalysisContext context) {
                        dataList.add(data);
                    }

                    // 全部解析完成会调用此方法
                    @Override
                    public void doAfterAllAnalysed(AnalysisContext context) {
                    }
                })
                .sheet(0)  // 读取第一个Sheet（对应Sheet1）
                .headRowNumber(1) // 从第1行开始读取数据（跳过标题）
                .doRead();

        return dataList;
    }

    public boolean excelCheck(MultipartFile file){
        if (file == null || file.isEmpty()) {
            return false;
        }

        // 校验文件名扩展名
        String fileName = file.getOriginalFilename();
        if (fileName == null || fileName.lastIndexOf(".") == -1) {
            return false;
        }

        String extension = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
        boolean validExtension = false;
        for (String allowedExt : EXCEL_EXTENSIONS) {
            if (allowedExt.equalsIgnoreCase(extension)) {
                validExtension = true;
                break;
            }
        }

        // 校验MIME类型
        String contentType = file.getContentType();
        boolean validContentType = false;
        for (String mime : EXCEL_MIME_TYPES) {
            if (mime.equalsIgnoreCase(contentType)) {
                validContentType = true;
                break;
            }
        }

        return validExtension && validContentType;
    }

    @Override
    public void fixMissingOrderProductId() {
        try {
            log.info("开始修复MqOrderPaymentInfo表中缺失的orderProductId");
            
            // 从Redis获取上次处理的页码，如果没有则从0开始
            String lastPageStr = stringRedisTemplate.opsForValue().get(REDIS_KEY_FIX_ORDER_PRODUCT_ID);
            long startPage = StringUtils.hasText(lastPageStr) ? Long.parseLong(lastPageStr) : 0L;
            
            log.info("从第{}页开始处理", startPage + 1);
            
            long currentPage = startPage;
            int totalFixedCount = 0;
            boolean hasMoreData = true;
            
            while (hasMoreData) {
                try {
                    // 构建分页查询条件
                    Page<MqOrderPaymentInfoModel> page = new Page<>(currentPage + 1, PAGE_SIZE);
                    LambdaQueryWrapper<MqOrderPaymentInfoModel> queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(MqOrderPaymentInfoModel::getAchievementSource, AchievementSourceEnum.KUAJINFG.getCode()) // 跨境
                               .eq(MqOrderPaymentInfoModel::getCalculateType, CalculateTypeEnum.SERVEINPROGRESS.getCode()) // 服务中
                               .eq(MqOrderPaymentInfoModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode()) // 未删除
                               .and(wrapper -> wrapper.isNull(MqOrderPaymentInfoModel::getOrderProductId)
                                                     .or()
                                                     .eq(MqOrderPaymentInfoModel::getOrderProductId, ""))
                               .orderByAsc(MqOrderPaymentInfoModel::getId); // 按ID排序
                    
                    IPage<MqOrderPaymentInfoModel> pageResult = mqOrderPaymentRepository.page(page, queryWrapper);
                    List<MqOrderPaymentInfoModel> paymentInfoList = pageResult.getRecords();
                    
                    if (CollectionUtils.isEmpty(paymentInfoList)) {
                        hasMoreData = false;
                        log.info("第{}页没有数据，处理完成", currentPage + 1);
                        break;
                    }
                    
                    log.info("处理第{}页，共{}条数据", currentPage + 1, paymentInfoList.size());
                    
                    // 批量处理当前页的数据
                    int pageFixedCount = batchFixOrderProductId(paymentInfoList);
                    totalFixedCount += pageFixedCount;
                    
                    // 更新Redis进度
                    stringRedisTemplate.opsForValue().set(REDIS_KEY_FIX_ORDER_PRODUCT_ID, 
                                                         String.valueOf(currentPage + 1), 
                                                         REDIS_EXPIRE_DAYS, TimeUnit.DAYS);
                    
                    currentPage++;
                    
                    // 检查是否还有更多数据
                    hasMoreData = pageResult.getSize() == PAGE_SIZE && pageResult.getCurrent() < pageResult.getPages();
                    
                    log.info("第{}页处理完成，修复{}条数据", currentPage, pageFixedCount);
                    
                } catch (Exception e) {
                    log.error("处理第{}页时发生错误: {}", currentPage + 1, e.getMessage(), e);
                    // 遇到错误时仍然更新进度，避免重复处理同一页
                    stringRedisTemplate.opsForValue().set(REDIS_KEY_FIX_ORDER_PRODUCT_ID, 
                                                         String.valueOf(currentPage + 1), 
                                                         REDIS_EXPIRE_DAYS, TimeUnit.DAYS);
                    currentPage++;
                }
            }
            
            // 处理完成后清除Redis进度
            stringRedisTemplate.delete(REDIS_KEY_FIX_ORDER_PRODUCT_ID);
            log.info("修复完成，总共修复{}条数据", totalFixedCount);
            
        } catch (Exception e) {
            log.error("修复MqOrderPaymentInfo表orderProductId失败，错误: {}", e.getMessage(), e);
        }
    }
    
    private int batchFixOrderProductId(List<MqOrderPaymentInfoModel> paymentInfoList) {
        Map<String, String> orderProductIdMap = new HashMap<>();
        List<MqOrderPaymentInfoModel> needUpdateList = new ArrayList<>();
        
        // 批量查询对应的orderProductId
        for (MqOrderPaymentInfoModel paymentInfo : paymentInfoList) {
            String mapKey = paymentInfo.getOrderId() + "_" + paymentInfo.getProductId();
            if (!orderProductIdMap.containsKey(mapKey)) {
                LambdaQueryWrapper<AchievementProductDetailModel> detailQueryWrapper = new LambdaQueryWrapper<>();
                detailQueryWrapper.eq(AchievementProductDetailModel::getOrderId, paymentInfo.getOrderId())
                                 .eq(AchievementProductDetailModel::getProductId, paymentInfo.getProductId())
                                 .isNotNull(AchievementProductDetailModel::getOrderProductId)
                                 .ne(AchievementProductDetailModel::getOrderProductId, "")
                                 .last("LIMIT 1");
                
                AchievementProductDetailModel detailModel = achievementProductDetailRepository.getOne(detailQueryWrapper);
                if (detailModel != null && StringUtils.hasText(detailModel.getOrderProductId())) {
                    orderProductIdMap.put(mapKey, detailModel.getOrderProductId());
                }
            }
            
            String orderProductId = orderProductIdMap.get(mapKey);
            if (StringUtils.hasText(orderProductId)) {
                paymentInfo.setOrderProductId(orderProductId);
                needUpdateList.add(paymentInfo);
            }
        }
        
        // 批量更新
        if (!CollectionUtils.isEmpty(needUpdateList)) {
            boolean updateResult = mqOrderPaymentRepository.updateBatchById(needUpdateList);
            if (updateResult) {
                log.debug("批量更新成功，更新{}条数据", needUpdateList.size());
                return needUpdateList.size();
            } else {
                log.warn("批量更新失败");
                return 0;
            }
        }
        
        return 0;
    }

    @Override
    public String getFixOrderProductIdProgress() {
        String progress = stringRedisTemplate.opsForValue().get(REDIS_KEY_FIX_ORDER_PRODUCT_ID);
        return StringUtils.hasText(progress) ? "当前处理到第" + progress + "页" : "当前没有正在进行的修复任务";
    }

    @Override
    public boolean clearFixOrderProductIdProgress() {
        try {
            Boolean result = stringRedisTemplate.delete(REDIS_KEY_FIX_ORDER_PRODUCT_ID);
            log.info("清除修复orderProductId进度Redis键，结果: {}", result);
            return Boolean.TRUE.equals(result);
        } catch (Exception e) {
            log.error("清除修复orderProductId进度Redis值失败，错误: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 构建包装响应对象
     */
    private ManualOrderWrapperResponse buildWrapperResponse(List<ManualOrderResponse> dataList, 
                                                          List<ManualOrderResponse> exceptionList, 
                                                          String failFileCode) {
        ManualOrderWrapperResponse wrapper = new ManualOrderWrapperResponse();
        wrapper.setDataList(dataList);
        wrapper.setExceptionList(exceptionList);
        wrapper.setTotalCount(dataList.size());
        wrapper.setFailCount(exceptionList.size());
        wrapper.setSuccessCount(dataList.size() - exceptionList.size());
        wrapper.setFailFileCode(failFileCode);
        return wrapper;
    }

    /**
     * 生成失败文件编码
     */
    private String generateFailFileCode() {
        String userId = getCurrentUserId();
        String randomStr = IdUtil.fastSimpleUUID().substring(0, 8);
        return userId + "_" + System.currentTimeMillis() + "_" + randomStr;
    }

    /**
     * 获取当前用户ID
     */
    private String getCurrentUserId() {
        try {
            if (UserContext.getCurrentUserInfo() != null && UserContext.getCurrentUserInfo().getUserId() != null) {
                return UserContext.getCurrentUserInfo().getUserId().toString();
            }
        } catch (Exception e) {
            log.warn("获取用户ID失败", e);
        }
        return "unknown";
    }

    /**
     * 存储失败数据到Redis
     */
    private void storeFailDataToRedis(String failFileCode, List<ManualOrderResponse> dataList) {
        try {
            String redisKey = REDIS_KEY_FAIL_DATA_PREFIX + failFileCode;
            String jsonData = JSONUtil.toJsonStr(dataList);
            stringRedisTemplate.opsForValue().set(redisKey, jsonData, REDIS_FAIL_DATA_EXPIRE_HOURS, TimeUnit.HOURS);
            log.info("失败数据已存储到Redis，key: {}, 数据条数: {}", redisKey, dataList.size());
        } catch (Exception e) {
            log.error("存储失败数据到Redis异常", e);
        }
    }

    @Override
    public void exportFailDataExcel(String failFileCode, HttpServletResponse response) {
        try {
            // 从Redis获取数据
            String redisKey = REDIS_KEY_FAIL_DATA_PREFIX + failFileCode;
            String jsonData = stringRedisTemplate.opsForValue().get(redisKey);
            
            if (!StringUtils.hasText(jsonData)) {
                throw new BusinessException("导出文件已过期或不存在");
            }
            
            List<ManualOrderResponse> dataList = JSONUtil.toList(jsonData, ManualOrderResponse.class);
            if (CollectionUtils.isEmpty(dataList)) {
                throw new BusinessException("没有数据可导出");
            }
            
            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = "manual_order_data_" + failFileCode + ".xlsx";
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName);
            
            // 写入Excel
            EasyExcel.write(response.getOutputStream(), ManualOrderResponse.class)
                    .sheet("手动订单处理结果")
                    .doWrite(dataList);
                    
            log.info("导出Excel成功，文件编码: {}, 数据条数: {}", failFileCode, dataList.size());
            
        } catch (Exception e) {
            log.error("导出Excel失败，文件编码: {}", failFileCode, e);
            throw new BusinessException("导出Excel失败: " + e.getMessage());
        }
    }
}