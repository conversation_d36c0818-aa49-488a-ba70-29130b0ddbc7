package com.xmd.achievement.dao.dto;

import java.util.Date;

import lombok.Data;

@Data
public class CustomerSaasGroupDto {
    
    /** 商务月 - 成为SAAS产品新客户时所属商务月 */
    private String month;
    
    /** 客户ID - 成为 SAAS 新客的客户 ID */
    private String customerId;
    
    /** 客户名称 - 存在客户改名，1 个客户ID对应两个客户名称，展示两个客户名称 */
    private String customerName;
    
    /** 商务ID - 成为 SAAS 产品新客时，对应的商务 ID */
    private String businessId;
    
    /** 商务名称 - 成为 SAAS 产品新客时，对应的商务名称 */
    private String businessName;
    
    /** 成为SAAS新客户时间 - 成为 SAAS 新客户的时间，精确到时分秒 */
    private Date newSaasCustomerTime;
    
    /** 成为SAAS新客户订单编号 - 成为SAAS 新客对应的订单编号 */
    private String orderNo;
    
    public CustomerSaasGroupDto() {
    }
    
    public CustomerSaasGroupDto(String month, String customerId, String customerName, 
                               String businessId, String businessName, 
                               Date newSaasCustomerTime, String orderNo) {
        this.month = month;
        this.customerId = customerId;
        this.customerName = customerName;
        this.businessId = businessId;
        this.businessName = businessName;
        this.newSaasCustomerTime = newSaasCustomerTime;
        this.orderNo = orderNo;
    }
}