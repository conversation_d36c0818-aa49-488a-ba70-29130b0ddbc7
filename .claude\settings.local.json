{"permissions": {"allow": ["Read(/d:\\dev\\bsp-achievement\\achievement-service\\src\\main\\java\\com\\xmd\\achievement\\service\\impl/**)", "Read(/d:\\dev\\bsp-achievement\\achievement-service\\src\\main\\java\\com\\xmd\\achievement\\service\\impl/**)", "Read(/d:\\dev\\bsp-achievement\\achievement-service\\src\\main\\java\\com\\xmd\\achievement\\service\\impl/**)", "Read(/d:\\dev\\bsp-achievement\\achievement-service\\src\\main\\java\\com\\xmd\\achievement\\service\\impl/**)", "Read(/d:\\dev\\bsp-achievement\\achievement-service\\src\\main\\java\\com\\xmd\\achievement\\service\\impl/**)", "Read(/d:\\dev\\bsp-achievement\\achievement-service\\src\\main\\java\\com\\xmd\\achievement\\service\\impl/**)", "Read(/D:\\dev\\bsp-achievement\\achievement-service\\src\\main\\java\\com\\xmd\\achievement\\service\\impl/**)", "Read(/D:\\dev\\bsp-achievement\\achievement-service\\src\\main\\java\\com\\xmd\\achievement\\service\\impl/**)", "Read(/D:\\dev\\bsp-achievement\\achievement-service\\src\\main\\java\\com\\xmd\\achievement\\service\\impl/**)", "Read(/D:\\dev\\bsp-achievement\\achievement-service\\src\\main\\java\\com\\xmd\\achievement\\service\\impl/**)", "Read(/D:\\dev\\bsp-achievement\\achievement-service\\src\\main\\java\\com\\xmd\\achievement\\service\\entity\\request/**)", "Read(/D:\\dev\\bsp-achievement/**)", "Read(/D:\\dev\\bsp-achievement/**)", "Read(/D:\\dev\\bsp-achievement\\achievement-service/**)", "Read(/D:\\dev\\bsp-achievement\\achievement-service/**)", "Read(/D:\\dev\\bsp-achievement\\achievement-service\\src\\main\\java\\com\\xmd\\achievement/**)", "Read(/D:\\dev\\bsp-achievement\\achievement-service\\src\\main\\java\\com\\xmd\\achievement/**)", "Read(/D:\\dev\\bsp-achievement\\achievement-dao/**)", "Read(/D:\\dev\\bsp-achievement/**)", "Read(/D:\\dev\\bsp-achievement/**)", "Read(/D:\\dev\\bsp-achievement\\achievement-service\\src\\main\\resources/**)", "Read(/D:\\dev\\bsp-achievement/**)", "Read(/D:\\dev\\bsp-achievement/**)", "Read(/D:\\dev\\bsp-achievement\\achievement-service/**)", "Read(/D:\\dev\\bsp-achievement\\achievement-service\\src\\main\\java\\com\\xmd\\achievement/**)", "Read(/D:\\dev\\bsp-achievement/**)", "Read(/D:\\dev\\bsp-achievement/**)", "Read(/D:\\dev\\bsp-achievement\\achievement-service\\src\\main\\resources/**)", "Read(/D:\\dev\\bsp-achievement\\achievement-service\\src\\main\\assembly\\bin/**)", "Read(/D:\\dev\\bsp-achievement\\achievement-dao\\src\\main\\java\\com\\xmd\\achievement\\dao\\entity/**)", "Read(/D:\\dev\\bsp-achievement\\achievement-service\\src\\main\\java\\com\\xmd\\achievement\\async\\job\\handler/**)", "Read(/D:\\dev\\bsp-achievement\\achievement-service\\src\\main\\java\\com\\xmd\\achievement\\async\\job\\handler/**)", "Read(/D:\\dev\\bsp-achievement\\achievement-service\\src\\main\\java\\com\\xmd\\achievement\\async\\job\\handler/**)", "Read(/D:\\dev\\bsp-achievement\\achievement-service\\src\\main\\java\\com\\xmd\\achievement\\service\\impl/**)", "Read(/D:\\dev\\bsp-achievement\\achievement-service\\src\\main\\java\\com\\xmd\\achievement\\handler\\achievement/**)", "Read(/D:\\dev\\bsp-achievement\\achievement-dao\\src\\main\\java\\com\\xmd\\achievement\\dao\\entity/**)", "Read(/D:\\dev\\bsp-achievement\\achievement-support\\src\\main\\java\\com\\xmd\\achievement\\support\\constant\\enums/**)", "Read(/D:\\dev\\bsp-achievement\\achievement-service\\src\\main\\java\\com\\xmd\\achievement\\service\\impl/**)", "Read(/D:\\dev\\bsp-achievement\\achievement-dao\\src\\main\\java\\com\\xmd\\achievement\\dao\\repository/**)", "Read(/D:\\dev\\bsp-achievement\\achievement-dao\\src\\main\\java\\com\\xmd\\achievement\\dao\\repository\\impl/**)", "Read(/D:\\dev\\bsp-achievement\\achievement-service\\src\\main\\java\\com\\xmd\\achievement\\handler\\achievement/**)", "Read(/D:\\dev\\bsp-achievement\\achievement-service\\src\\main\\java\\com\\xmd\\achievement\\service\\impl/**)", "Read(/D:\\dev\\bsp-achievement\\achievement-service\\src\\main\\java\\com\\xmd\\achievement\\web\\controller/**)", "Read(/D:\\dev\\bsp-achievement\\achievement-service\\src\\main\\java\\com\\xmd\\achievement\\service\\entity\\response/**)", "Read(/D:\\dev\\bsp-achievement\\achievement-service\\src\\main\\java\\com\\xmd\\achievement\\service/**)", "Read(/D:\\dev\\bsp-achievement\\achievement-service\\src\\main\\java\\com\\xmd\\achievement/**)", "Read(/D:\\dev\\bsp-achievement\\achievement-service\\src\\main\\java\\com\\xmd\\achievement\\web\\util/**)", "Read(/D:\\dev\\bsp-achievement\\achievement-service\\src\\main\\java\\com\\xmd\\achievement\\service\\entity\\response/**)", "Bash(mvn clean compile -q)", "Bash(mvn clean compile -pl achievement-service -q)", "Read(/D:\\dev\\bsp-achievement\\achievement-service\\src\\main\\java\\com\\xmd\\achievement\\service/**)", "Read(/D:\\dev\\bsp-achievement\\achievement-service\\src\\main\\java\\com\\xmd\\achievement\\service\\impl/**)"], "deny": [], "ask": []}}