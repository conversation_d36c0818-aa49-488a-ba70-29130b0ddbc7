package com.xmd.achievement.web.util;

import org.apache.poi.ss.usermodel.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * excel工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
public class ExcelUtil {

    /**
     * 判断excel的列是否为空
     * @return
     */
    public static boolean isRowEmpty(Row row) {
        if (row == null) {
            return true;
        }
        for (int c = row.getFirstCellNum(); c < row.getLastCellNum(); c++) {
            Cell cell = row.getCell(c);
            if (cell != null && cell.getCellType() != CellType.BLANK) {
                return false;
            }
        }
        return true;
    }

    /**
     * 处理 Excel 单元格数据
     *
     * @param cell cell
     * @return {@link String }
     */
    public static String getStringCellValue(Cell cell) {
        if (cell == null) {
            return "";
        }
        DataFormatter dataFormatter = new DataFormatter();
        return dataFormatter.formatCellValue(cell).trim();
    }

    public static String getStringCellValueOrNull(Cell cell) {
        if (cell == null) {
            return null;
        }
        DataFormatter dataFormatter = new DataFormatter();
        return dataFormatter.formatCellValue(cell).trim();
    }

    public static Integer getIntegerCellValue(Cell cell) {
        if (cell == null) {
            return null;
        }
        if (cell.getCellType() == CellType.NUMERIC) {
            // 强制转换为 Integer
            return (int) cell.getNumericCellValue();
        } else if (cell.getCellType() == CellType.STRING) {
            try {
                return Integer.parseInt(cell.getStringCellValue().trim());
            } catch (NumberFormatException e) {
                // 处理非法格式
                return null;
            }
        }
        return null;
    }

    public static Long getLongCellValue(Cell cell) {
        if (cell == null) {
            return null;
        }
        if (cell.getCellType() == CellType.NUMERIC) {
            // 强制转换为 Integer
            return (long) cell.getNumericCellValue();
        } else if (cell.getCellType() == CellType.STRING) {
            try {
                return Long.parseLong(cell.getStringCellValue().trim());
            } catch (NumberFormatException e) {
                // 处理非法格式
                return null;
            }
        }
        return null;
    }

    public static Double getDoubleCellValue(Cell cell) {
        if (cell == null) {
            return null;
        }
        if (cell.getCellType() == CellType.NUMERIC) {
            return cell.getNumericCellValue();
        } else if (cell.getCellType() == CellType.STRING) {
            try {
                return Double.parseDouble(cell.getStringCellValue().trim());
            } catch (NumberFormatException e) {
                // 处理非法格式
                return null;
            }
        }
        return null;
    }

    public static BigDecimal getBigDecimalFromCell(Cell cell) {
        if (cell == null) {
            return null;
        }

        switch (cell.getCellType()) {
            case NUMERIC:
                // 直接用字符串构造，避免 double 精度问题
                return new BigDecimal(String.valueOf(cell.getNumericCellValue()));
            case STRING:
                String str = cell.getStringCellValue().trim();
                if (str.isEmpty() || "null".equalsIgnoreCase(str)) {
                    return BigDecimal.ZERO;
                }
                try {
                    return new BigDecimal(str);
                } catch (NumberFormatException e) {
                    throw new IllegalArgumentException("Invalid number format: " + str, e);
                }
            case FORMULA:
                // 处理公式，先判断结果类型
                switch (cell.getCachedFormulaResultType()) {
                    case NUMERIC:
                        return new BigDecimal(String.valueOf(cell.getNumericCellValue()));
                    case STRING:
                        return new BigDecimal(cell.getStringCellValue());
                    default:
                        return BigDecimal.ZERO;
                }
            default:
                return BigDecimal.ZERO;
        }
    }

    public static Date getDateCellValue(Cell cell) {
        if (cell == null) {
            return null;
        }
        if (cell.getCellType() == CellType.NUMERIC) {
            if (DateUtil.isCellDateFormatted(cell)) {
                return cell.getDateCellValue();
            } else {
                // 处理非日期格式的数字
                return null;
            }
        } else if (cell.getCellType() == CellType.STRING) {
            String dateStr = cell.getStringCellValue().trim();
            try {
                // 尝试解析字符串为日期
                return DateUtil.parseYYYYMMDDDate(dateStr);
            } catch (Exception e) {
                // 处理非法格式
                return null;
            }
        }
        return null;
    }
}
