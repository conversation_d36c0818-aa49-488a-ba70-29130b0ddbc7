package com.xmd.achievement.service.entity.dto;

import java.util.Date;

import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.format.DateTimeFormat;
import lombok.Data;

@Data
public class CustomerSaasGroupExcelDto {
    
    @ExcelProperty(value = "商务月", index = 0)
    private String month;
    
    @ExcelProperty(value = "客户ID", index = 1)
    private String customerId;
    
    @ExcelProperty(value = "客户名称", index = 2)
    private String customerName;
    
    @ExcelProperty(value = "商务ID", index = 3)
    private String businessId;
    
    @ExcelProperty(value = "商务名称", index = 4)
    private String businessName;
    
    @ExcelProperty(value = "成为SAAS新客户时间", index = 5)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date newSaasCustomerTime;
    
    @ExcelProperty(value = "成为SAAS新客户订单编号", index = 6)
    private String orderNo;
}