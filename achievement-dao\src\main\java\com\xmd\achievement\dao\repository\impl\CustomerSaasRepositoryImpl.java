package com.xmd.achievement.dao.repository.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xmd.achievement.dao.dto.CustomerSaasGroupDto;
import com.xmd.achievement.dao.dto.CustomerSaasGroupQueryDto;
import com.xmd.achievement.dao.dto.SearchCustomerSaasDto;
import com.xmd.achievement.dao.entity.CustomerSaasModel;
import com.xmd.achievement.dao.mapper.CustomerSaasMapper;
import com.xmd.achievement.dao.repository.ICustomerSaasRepository;

@Service
public class CustomerSaasRepositoryImpl extends ServiceImpl<CustomerSaasMapper, CustomerSaasModel> implements ICustomerSaasRepository {
    @Autowired
    private CustomerSaasMapper customerSaasMapper;

    @Override
    public Date getMaxChurnDateByCustomerId(String customerId) {
        return customerSaasMapper.selectMaxChurnDateByCustomerId(customerId);
    }

    @Override
    public Date getMaxChurnDateByCustomerId(String customerId, Date date,Long orderId) {
       return customerSaasMapper.selectMaxChurnDateByCustomerIdWithDate(customerId,date,orderId);
    }  
    

    @Override
    public int countOrderBetween(String customerId, Date start, Date end, Integer orderSource) {
        return customerSaasMapper.countOrderBetween(customerId, start, end, orderSource);
    }

    @Override
    public List<SearchCustomerSaasDto> searchCustomerSaasCount(List<String> businessIds,
            String startDate, String endDate) {
        return customerSaasMapper.searchCustomerSaasCount(businessIds, startDate, endDate);
    }

    @Override
    public IPage<CustomerSaasGroupDto> searchCustomerSaasGroupedPage(Page<CustomerSaasGroupDto> page, CustomerSaasGroupQueryDto request) {
        return customerSaasMapper.searchCustomerSaasGroupedPage(page, request);
    }
}
