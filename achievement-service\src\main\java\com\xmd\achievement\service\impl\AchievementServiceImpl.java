package com.xmd.achievement.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xmd.achievement.async.event.ProductAchievementUpdatedEvent;
import com.xmd.achievement.dao.entity.*;
import com.xmd.achievement.dao.repository.*;
import com.xmd.achievement.dao.repository.impl.AchievementCategoryDetailRepositoryImpl;
import com.xmd.achievement.handler.achievement.AchievementHandler;
import com.xmd.achievement.handler.achievement.AchievementRefundHandler;
import com.xmd.achievement.handler.wrapper.AchievementProductWrapper;
import com.xmd.achievement.rpc.InnerService;
import com.xmd.achievement.rpc.entity.dto.*;
import com.xmd.achievement.service.*;
import com.xmd.achievement.service.entity.request.*;
import com.xmd.achievement.service.entity.response.*;
import com.xmd.achievement.support.constant.NumberConstants;
import com.xmd.achievement.support.constant.enums.*;
import com.xmd.achievement.support.constant.exceptions.BusinessException;
import com.xmd.achievement.util.enums.CalculateTypeEnum;
import com.xmd.achievement.util.enums.DataChangeTypeEnum;
import com.xmd.achievement.util.enums.SaleTypeEnum;
import com.xmd.achievement.web.annotate.lock.annotation.Lock;
import com.xmd.achievement.web.entity.base.PageResponse;
import com.xmd.achievement.web.entity.request.RecalculateAchievementRequest;
import com.xmd.achievement.web.entity.response.WebCodeMessageEnum;
import com.xmd.achievement.web.entity.response.WebResult;
import com.xmd.achievement.web.util.DateUtils;
import com.xmd.achievement.web.util.EasyExcelUtil;
import com.xmd.achievement.web.util.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

import static com.xmd.achievement.util.enums.CalculateTypeEnum.PAYMENT;
import static com.xmd.achievement.util.enums.CalculateTypeEnum.SERVEINPROGRESS;

/**
 * 描述
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class AchievementServiceImpl implements IAchievementService {
    @Resource
    IAchievementProductDetailRepository achievementProductDetailRepository;
    @Resource
    IAchievementCategoryDetailRepository achievementCategoryDetailRepository;
    @Resource
    IAchievementSpecDetailRepository achievementSpecDetailRepository;
    @Resource
    IBusinessAchievementRepository businessAchievementRepository;
    @Resource
    private MqServeFinishTimeInfoService mqServeFinishTimeInfoService;
    @Resource
    IThirdAchievementRepository tirdAchievementRepository;
    @Resource
    AchievementProductWrapper achievementProductWrapper;
    @Resource
    private AchievementCategoryDetailRepositoryImpl achievementCategoryDetailRepositoryImpl;
    @Resource
    private IBusinessMonthService businessMonthService;
    @Autowired
    private InnerService innerService;
    @Resource
    IMqOrderPaymentInfoRepository mqOrderPaymentRepository;
    @Resource
    private INewOldCustomerRecordRepository newOldCustomerRecordRepository;
    @Autowired
    IMqOrderPaymentInfoRepository mqOrderPaymentInfoRepository;
    @Autowired
    private AchievementHandler achievementHandler;
    @Autowired
    private IMqServeFinishTimeInfoRepository mqServeFinishTimeInfoRepository;
    @Resource
    private OperateLogService operateLogService;    
    @Resource
    private IFreezeMonthErrorLogService freezeMonthErrorLogService;
    @Resource
    private ICustomerSaasService customerSaasService;
    @Resource
    private ApplicationEventPublisher eventPublisher;
    @Resource
    private AchievementRefundHandler achievementRefundHandler;
    @Resource
    private IMqOrderRefundInfoRepository mqOrderRefundInfoRepository;


    private void permitHandler(LambdaQueryWrapper<AchievementProductDetailModel> queryWrapper,UserLoginInfoDTO userInfo){
        DataPermitEnum dataPermit = DataPermitEnum.getDataPermitEnumByCode(userInfo.getUserDataPermissionsDTO().getDataPermissionsType());
        switch (dataPermit){
            case SELF:
                queryWrapper.eq(AchievementProductDetailModel::getBusinessId, userInfo.getUserId());
                break;
            case SELF_AND_COMPANY:
                queryWrapper.eq(AchievementProductDetailModel::getBusinessId, userInfo.getUserId());
                if(Objects.nonNull(userInfo.getOrgPathInfoDTO().getCompanyId())){
                    queryWrapper.eq(AchievementProductDetailModel::getCompanyId, userInfo.getOrgPathInfoDTO().getCompanyId());
                }
                break;
            case DEPT:
                if (Objects.nonNull(userInfo.getOrgPathInfoDTO().getDeptId())) {
                    queryWrapper.eq(AchievementProductDetailModel::getDeptId, userInfo.getOrgPathInfoDTO().getDeptId());
                }else{
                    queryWrapper.eq(AchievementProductDetailModel::getBusinessId, userInfo.getUserId());
                }
                break;
            case CAREER:
                if (Objects.nonNull(userInfo.getOrgPathInfoDTO().getCareerId())) {
                    queryWrapper.eq(AchievementProductDetailModel::getDivisionId, userInfo.getOrgPathInfoDTO().getCareerId());
                }else{
                    queryWrapper.eq(AchievementProductDetailModel::getBusinessId, userInfo.getUserId());
                }
                break;
            case COMPANY:
                if (Objects.nonNull(userInfo.getOrgPathInfoDTO().getCompanyId())) {
                    queryWrapper.eq(AchievementProductDetailModel::getCompanyId, userInfo.getOrgPathInfoDTO().getCompanyId());
                }else{
                    queryWrapper.eq(AchievementProductDetailModel::getBusinessId, userInfo.getUserId());
                }
        }
    }

    private void BusinessPermitHandler(LambdaQueryWrapper<BusinessAchievementModel> queryWrapper,UserLoginInfoDTO userInfo){
        DataPermitEnum dataPermit = DataPermitEnum.getDataPermitEnumByCode(userInfo.getUserDataPermissionsDTO().getDataPermissionsType());
        switch (dataPermit){
            case SELF:
                queryWrapper.eq(BusinessAchievementModel::getEmployeeId, userInfo.getUserId());
                break;
            case SELF_AND_COMPANY:
                queryWrapper.eq(BusinessAchievementModel::getEmployeeId, userInfo.getUserId());
                if(Objects.nonNull(userInfo.getOrgPathInfoDTO().getCompanyId())){
                    queryWrapper.eq(BusinessAchievementModel::getCompanyId, userInfo.getOrgPathInfoDTO().getCompanyId());
                }
                break;
            case DEPT:
                if (Objects.nonNull(userInfo.getOrgPathInfoDTO().getDeptId())) {
                    queryWrapper.eq(BusinessAchievementModel::getDeptId, userInfo.getOrgPathInfoDTO().getDeptId());
                }else{
                    queryWrapper.eq(BusinessAchievementModel::getEmployeeId, userInfo.getUserId());
                }
                break;
            case CAREER:
                if (Objects.nonNull(userInfo.getOrgPathInfoDTO().getCareerId())) {
                    queryWrapper.eq(BusinessAchievementModel::getDivisionId, userInfo.getOrgPathInfoDTO().getCareerId());
                }else{
                    queryWrapper.eq(BusinessAchievementModel::getEmployeeId, userInfo.getUserId());
                }
                break;
            case COMPANY:
                if (Objects.nonNull(userInfo.getOrgPathInfoDTO().getCompanyId())) {
                    queryWrapper.eq(BusinessAchievementModel::getCompanyId, userInfo.getOrgPathInfoDTO().getCompanyId());
                }else{
                    queryWrapper.eq(BusinessAchievementModel::getEmployeeId, userInfo.getUserId());
                }
        }
    }

    private LambdaQueryWrapper<AchievementProductDetailModel> searchProductDetail(AchievementProductPageRequest search, UserLoginInfoDTO userInfo) {
        LambdaQueryWrapper<AchievementProductDetailModel> queryWrapper = new LambdaQueryWrapper<>();
        permitHandler(queryWrapper,userInfo);
        queryWrapper.eq(AchievementProductDetailModel::getDeleteFlag, YesOrNotEnum.NO.getCode())
                .eq(AchievementProductDetailModel::getDisplayed, NumberConstants.INTEGER_VALUE_0);
        Integer achievementSource = search.getAchievementSource();
        if (null == achievementSource) {
            queryWrapper.in(AchievementProductDetailModel::getAchievementSource, NumberConstants.INTEGER_VALUE_1, NumberConstants.INTEGER_VALUE_2);
        } else if (!Objects.equals(achievementSource, NumberConstants.INTEGER_VALUE_0)) {
            queryWrapper.eq(AchievementProductDetailModel::getAchievementSource, achievementSource);
        }

        if (ObjectUtils.isNotEmpty(search)) {
            String orderProductId = search.getOrderProductId();
            String orderNo = search.getOrderNo();
            String customerName = search.getCustomerName();
            String businessId = search.getBusinessId();
            String businessName = search.getBusinessName();
            String contractNo = search.getContractNo();
            String saleType = search.getSaleType();
            String orgId = search.getOrgId();
            Date startTime = search.getStartTime();
            if (ObjectUtil.isNotEmpty(startTime)) {
                startTime = DateUtils.getStartOfDay(startTime);
            }
            Date endTime = search.getEndTime();
            if (ObjectUtil.isNotEmpty(endTime)) {
                endTime = DateUtils.getEndOfDay(endTime);
            }
            queryWrapper.eq(StringUtils.isNotBlank(orderProductId), AchievementProductDetailModel::getOrderProductId, orderProductId);
            queryWrapper.eq(StringUtils.isNotBlank(saleType), AchievementProductDetailModel::getSaleType, saleType);
            queryWrapper.eq(StringUtils.isNotBlank(orderNo), AchievementProductDetailModel::getOrderNo, orderNo);
            queryWrapper.like(StringUtils.isNotBlank(customerName), AchievementProductDetailModel::getCustomerName, customerName);
            queryWrapper.eq(StringUtils.isNotBlank(businessId), AchievementProductDetailModel::getBusinessId, businessId);
            queryWrapper.like(StringUtils.isNotBlank(businessName), AchievementProductDetailModel::getBusinessRepresentative, businessName);
            queryWrapper.eq(StringUtils.isNotBlank(contractNo), AchievementProductDetailModel::getContractNo, contractNo);
            queryWrapper.between(ObjectUtils.isNotEmpty(startTime) && ObjectUtils.isNotEmpty(endTime), AchievementProductDetailModel::getStatisticsTime, startTime, endTime);
            queryWrapper.eq(ObjectUtils.isNotEmpty(search.getAchievementId()), AchievementProductDetailModel::getAchievementId, search.getAchievementId());
            queryWrapper.eq(Objects.nonNull(search.getIsAbnormal()), AchievementProductDetailModel::getIsAbnormal, search.getIsAbnormal());
            queryWrapper.and(StringUtils.isNotBlank(orgId),
                    wrapper -> wrapper
                            .eq(AchievementProductDetailModel::getCompanyId, orgId)
                            .or()
                            .eq(AchievementProductDetailModel::getDeptId, orgId)
                            .or()
                            .eq(AchievementProductDetailModel::getDivisionId, orgId)
            );
            
            // 添加商务月范围查询条件
            if (StringUtils.isNotBlank(search.getStartMonth())) {
                queryWrapper.ge(AchievementProductDetailModel::getBusinessMonth, search.getStartMonth());
            }
            if (StringUtils.isNotBlank(search.getEndMonth())) {
                queryWrapper.le(AchievementProductDetailModel::getBusinessMonth, search.getEndMonth());
            }

        }

        queryWrapper.orderByDesc(AchievementProductDetailModel::getAchievementId);
        return queryWrapper;
    }

    @Override
    public PageResponse<AchievementProductDetailResponse> achievementProductList(AchievementProductPageRequest pageRequest) {
        UserLoginInfoDTO userInfo = UserContext.getCurrentUserInfo();
        UserDataPermissionsDTO userDataPermissionsDTO = userInfo.getUserDataPermissionsDTO();
        if (Objects.isNull(userDataPermissionsDTO.getDataPermissionsType())||DataPermitEnum.NO_PERMIT.getCode().equals(userDataPermissionsDTO.getDataPermissionsType())) {
            return new PageResponse<>(0, pageRequest.getPageIndex(), pageRequest.getPageSize());
        }
        Page<AchievementProductDetailModel> pageOf = new Page<>(pageRequest.getPageIndex(), pageRequest.getPageSize());
        LambdaQueryWrapper<AchievementProductDetailModel> queryWrapper = searchProductDetail(pageRequest, userInfo);
        queryWrapper.select(AchievementProductDetailModel::getAchievementId);
        Page<AchievementProductDetailModel> page = achievementProductDetailRepository.page(pageOf, queryWrapper);
        PageResponse<AchievementProductDetailResponse> response = new PageResponse<>(page.getTotal(), page.getCurrent(), page.getSize());
        if (page.getRecords().isEmpty()) {
            response.setList(Collections.emptyList());
            return response;
        }
        List<Long> achievementIds = page.getRecords().stream().map(AchievementProductDetailModel::getAchievementId).collect(Collectors.toList());
        LambdaQueryWrapper<AchievementProductDetailModel> achievementQueryWrapper = new LambdaQueryWrapper<AchievementProductDetailModel>()
                .in(AchievementProductDetailModel::getAchievementId, achievementIds)
                .eq(AchievementProductDetailModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode());

        // 添加商务月范围查询条件，确保第二次查询也应用相同的过滤条件
        if (StringUtils.isNotBlank(pageRequest.getStartMonth())) {
            achievementQueryWrapper.ge(AchievementProductDetailModel::getBusinessMonth, pageRequest.getStartMonth());
        }
        if (StringUtils.isNotBlank(pageRequest.getEndMonth())) {
            achievementQueryWrapper.le(AchievementProductDetailModel::getBusinessMonth, pageRequest.getEndMonth());
        }

        List<AchievementProductDetailModel> achievements = achievementProductDetailRepository.list(achievementQueryWrapper);
        response.setList(achievements.stream().map(model -> {
            AchievementProductDetailResponse achievementProductDetailResponse = new AchievementProductDetailResponse();
            BeanUtils.copyProperties(model, achievementProductDetailResponse);
            achievementProductDetailResponse.setMainSplitPerson(MainSubEnum.getDescriptionByType(model.getMainSplitPerson()));
            achievementProductDetailResponse.setCustomerType(CustomerType.getDescriptionByType(model.getCustomerType()));
            achievementProductDetailResponse.setOrderSource(ObjectUtils.isEmpty(OrderSourceEnum.getOrderSourceByType(model.getOrderSource())) ? "" : OrderSourceEnum.getOrderSourceByType(model.getOrderSource()).getVisitorCode());
            achievementProductDetailResponse.setSaleType(OrderSaleTypeEnum.getOrderSaleTypeByType(model.getSaleType()).getVisitorCode());
            achievementProductDetailResponse.setStatus(AchStatus.getDescriptionByType(model.getStatus()));
            return achievementProductDetailResponse;
        }).collect(Collectors.toList()));

        return response;
    }


    @Override
    public AchievementDetailResponse getAchievementProductDetail(Long achievementId) {

        AchievementDetailResponse response = new AchievementDetailResponse();

        LambdaQueryWrapper<AchievementProductDetailModel> queryWrapper = new LambdaQueryWrapper<AchievementProductDetailModel>()
                .eq(AchievementProductDetailModel::getAchievementId, achievementId);
        AchievementProductDetailModel one = achievementProductDetailRepository.getOne(queryWrapper);
        BeanUtils.copyProperties(one, response);
        response.setMainSplitPerson(MainSubEnum.getDescriptionByType(one.getMainSplitPerson()));
        response.setCustomerType(CustomerType.getDescriptionByType(one.getCustomerType()));
        response.setOrderSource(OrderSourceEnum.getOrderSourceByType(one.getOrderSource()).getVisitorCode());
        response.setSaleType(OrderSaleTypeEnum.getOrderSaleTypeByType(one.getSaleType()).getVisitorCode());
        response.setStatus(AchStatus.getDescriptionByType(one.getStatus()));
        return response;
    }

    @Override
    public AchievementProductDetailModel getAchievementProductDetailModel(Long achievementId) {

        return achievementProductDetailRepository.getOne(new LambdaQueryWrapper<AchievementProductDetailModel>().eq(AchievementProductDetailModel::getAchievementId, achievementId));
    }

    @Override
    public List<AchievementCategoryResponse> getAchievementCategoryDetail(Long achievementId) {

        List<AchievementCategoryDetailModel> list = achievementCategoryDetailRepository.list(new LambdaQueryWrapper<AchievementCategoryDetailModel>().eq(AchievementCategoryDetailModel::getAchievementId, achievementId));

        return list.stream().map(model -> {
            AchievementCategoryResponse achievementCategoryResponse = new AchievementCategoryResponse();
            BeanUtils.copyProperties(model, achievementCategoryResponse);
            return achievementCategoryResponse;
        }).collect(Collectors.toList());
    }

    @Override
    public List<AchievementSpecDetailResponse> getAchievementSpecDetail(Long achievementCategoryId,String orderProductId) {

        List<AchievementSpecDetailModel> list = achievementSpecDetailRepository.list(new LambdaQueryWrapper<AchievementSpecDetailModel>()
                .eq(AchievementSpecDetailModel::getAchievementCategoryId, achievementCategoryId));

        Boolean flag = false;
        for (AchievementSpecDetailModel model : list) {
            if(StringUtils.isNotBlank(model.getOrderProductId()) && model.getOrderProductId().equals(orderProductId)){
                flag = true;
                break;
            }
        }
        if(flag){
            list = list.stream().filter(model -> model.getOrderProductId().equals(orderProductId)).collect(Collectors.toList());
        }

        return list.stream().map(model -> {
            AchievementSpecDetailResponse achievementSpecDetailResponse = new AchievementSpecDetailResponse();
            BeanUtils.copyProperties(model, achievementSpecDetailResponse);
            achievementSpecDetailResponse.setItemType(ItemTypeEnum.getDescriptionByType(model.getItemType()));
            return achievementSpecDetailResponse;
        }).collect(Collectors.toList());
    }

    @Override
    public PageResponse<BusinessAchievementResponse> businessAchievementList(BusinessSearchRequest pageRequest) {
        UserLoginInfoDTO userInfo = UserContext.getCurrentUserInfo();
        UserDataPermissionsDTO userDataPermissionsDTO = UserContext.getCurrentUserInfo().getUserDataPermissionsDTO();
        if (Objects.isNull(userDataPermissionsDTO.getDataPermissionsType()) ||DataPermitEnum.NO_PERMIT.getCode().equals(userDataPermissionsDTO.getDataPermissionsType())) {
            return new PageResponse<>(0, pageRequest.getPageIndex(), pageRequest.getPageSize());
        }
        LambdaQueryWrapper<BusinessAchievementModel> queryWrapper = new LambdaQueryWrapper<>();
        BusinessPermitHandler(queryWrapper,userInfo);
        if (ObjectUtils.isNotEmpty(pageRequest)) {
            String employeeName = pageRequest.getEmployeeName();
            String employeeId = pageRequest.getEmployeeId();
            String positionCodes = pageRequest.getPositionCodes();
            if (StringUtils.isNotBlank(positionCodes)) {
                queryWrapper.in(StringUtils.isNotBlank(positionCodes), BusinessAchievementModel::getPositionCode, (Object[]) positionCodes.split(","));
            }
            String senioritySegmentId = pageRequest.getSeniorityId();
            String orgId = pageRequest.getOrgId();
            String achievementSegmentId = pageRequest.getAchievementSegmentId();
            String startDate = pageRequest.getStartDate();
            String endDate = pageRequest.getEndDate();
            queryWrapper.eq(StringUtils.isNotBlank(employeeId), BusinessAchievementModel::getEmployeeId, employeeId);
            queryWrapper.like(StringUtils.isNotBlank(employeeName), BusinessAchievementModel::getEmployeeName, employeeName);
            queryWrapper.eq(StringUtils.isNotBlank(senioritySegmentId), BusinessAchievementModel::getCompanyId, senioritySegmentId);
            queryWrapper.eq(StringUtils.isNotBlank(achievementSegmentId), BusinessAchievementModel::getAchievementSegmentId, achievementSegmentId);
            queryWrapper.and(StringUtils.isNotBlank(orgId),
                    wrapper -> wrapper
                            .eq(BusinessAchievementModel::getCompanyId, orgId)
                            .or()
                            .eq(BusinessAchievementModel::getDeptId, orgId)
                            .or()
                            .eq(BusinessAchievementModel::getDivisionId, orgId)
            );
            queryWrapper.between(StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate), BusinessAchievementModel::getBusinessMonth, startDate, endDate);
        }
        queryWrapper.eq(BusinessAchievementModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode());
        queryWrapper.orderByAsc(BusinessAchievementModel::getEmployeeId);
        Page<BusinessAchievementModel> pageOf = new Page<>(pageRequest.getPageIndex(), pageRequest.getPageSize());
        Page<BusinessAchievementModel> page = businessAchievementRepository.page(pageOf, queryWrapper);
        List<BusinessAchievementModel> businessAchievementModels = page.getRecords();

        PageResponse<BusinessAchievementResponse> response = new PageResponse<>(page.getTotal(), page.getCurrent(), page.getSize());
        response.setList(businessAchievementModels.stream().map(businessAchievementModel -> {
            BusinessAchievementResponse businessAchievementResponse = new BusinessAchievementResponse();
            BeanUtils.copyProperties(businessAchievementModel, businessAchievementResponse);
            if (businessAchievementModel.getConfirmed() != null) {
                businessAchievementResponse.setConfirmed(ConfirmedFlagEnum.getDescriptionByType(businessAchievementModel.getConfirmed()));
            }
            return businessAchievementResponse;
        }).collect(Collectors.toList()));
        return response;
    }

    @Override
    public void exportBusinessAchievement(BusinessSearch search, HttpServletResponse response) {
        LambdaQueryWrapper<BusinessAchievementModel> queryWrapper = new LambdaQueryWrapper<>();
        UserLoginInfoDTO userInfo = UserContext.getCurrentUserInfo();
        UserDataPermissionsDTO userDataPermissionsDTO = UserContext.getCurrentUserInfo().getUserDataPermissionsDTO();
        if (Objects.isNull(userDataPermissionsDTO.getDataPermissionsType()) ||DataPermitEnum.NO_PERMIT.getCode().equals(userDataPermissionsDTO.getDataPermissionsType())) {
            return;
        }
        BusinessPermitHandler(queryWrapper,userInfo);
        if (ObjectUtils.isNotEmpty(search)) {
            String employeeName = search.getEmployeeName();
            String employeeId = search.getEmployeeId();
            String positionCode = search.getPositionCode();
            String senioritySegmentId = search.getSeniorityId();
            String orgId = search.getOrgId();
            String achievementSegmentId = search.getAchievementSegmentId();
            String startDate = search.getStartDate();
            String endDate = search.getEndDate();
            queryWrapper.eq(StringUtils.isNotBlank(employeeId), BusinessAchievementModel::getEmployeeId, employeeId);
            queryWrapper.like(StringUtils.isNotBlank(employeeName), BusinessAchievementModel::getEmployeeName, employeeName);
            queryWrapper.eq(StringUtils.isNotBlank(positionCode), BusinessAchievementModel::getPosition, positionCode);
            queryWrapper.eq(StringUtils.isNotBlank(senioritySegmentId), BusinessAchievementModel::getCompanyId, senioritySegmentId);
            queryWrapper.eq(StringUtils.isNotBlank(achievementSegmentId), BusinessAchievementModel::getAchievementSegmentId, achievementSegmentId);
            queryWrapper.between(StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate), BusinessAchievementModel::getBusinessMonth, startDate, endDate);
            queryWrapper.and(StringUtils.isNotBlank(orgId),
                    wrapper -> wrapper
                            .eq(BusinessAchievementModel::getCompanyId, orgId)
                            .or()
                            .eq(BusinessAchievementModel::getDeptId, orgId)
                            .or()
                            .eq(BusinessAchievementModel::getDivisionId, orgId)
            );
        }
        queryWrapper.eq(BusinessAchievementModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode());
        queryWrapper.orderByAsc(BusinessAchievementModel::getEmployeeId);

        List<BusinessAchievementModel> list = businessAchievementRepository.list(queryWrapper);
        List<BusinessAchievementExcel> collect = list.stream().map(businessAchievementModel -> {
            BusinessAchievementExcel excel = new BusinessAchievementExcel();
            BeanUtils.copyProperties(businessAchievementModel, excel);
            if (businessAchievementModel.getConfirmed() != null) {
                excel.setConfirmed(ConfirmedFlagEnum.getDescriptionByType(businessAchievementModel.getConfirmed()));
            }
            return excel;
        }).collect(Collectors.toList());

        try {
            EasyExcelUtil.download(response, collect, "商务统计导出", Boolean.TRUE);
        } catch (IOException e) {
            log.error("导出产品业绩失败", e);
        }
    }

    @Override
    @Lock("'handelTask'+#orderId+#productId+#serveNo")
    public void updateServeFinishTime(Long orderId, Long productId, String serveNo, Date serveFinishTime) {
        //幂等校验
        if (checkTaskStatus(orderId, productId, serveNo)) {
            return;
        }
        LambdaQueryWrapper<AchievementProductDetailModel> query = new LambdaQueryWrapper<AchievementProductDetailModel>()
                .eq(AchievementProductDetailModel::getOrderId, orderId)
                .eq(AchievementProductDetailModel::getProductId, productId);
        List<AchievementProductDetailModel> list = achievementProductDetailRepository.list(query);
        if (ObjectUtil.isEmpty(list)) {
            log.info("更新服务完成时间失败{}", WebCodeMessageEnum.DATA_NOT_EXIST.getMsg());
            mqServeFinishTimeInfoService.executeFailed(orderId, productId, WebCodeMessageEnum.DATA_NOT_EXIST.getMsg());
            return;
        }
        list.forEach(model -> model.setServeFinishTime(serveFinishTime));
        achievementProductDetailRepository.saveOrUpdateBatch(list);
        mqServeFinishTimeInfoService.executeSuccess(orderId, productId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handelThirdAchievement(ThirdAchievementModel model, MqOrderPaymentInfoModel mqModel) {
        try {
            // 业务逻辑处理
            thirdAchievementHandel(model, mqModel);

            // 修改任务状态成功
            tirdAchievementRepository.lambdaUpdate()
                    .eq(ThirdAchievementModel::getTaskId, model.getTaskId())
                    .set(ThirdAchievementModel::getTaskStatus, TaskStatusEnum.YES.getCode())
                    .update();

        } catch (Exception ex) {
            log.error("三方业绩流水数据处理失败taskId:{},失败原因:", model.getTaskId(), ex);
            // 修改任务状态成功
            tirdAchievementRepository.lambdaUpdate()
                    .eq(ThirdAchievementModel::getTaskId, model.getTaskId())
                    .set(ThirdAchievementModel::getFailReason, ex.getMessage())
                    .set(ThirdAchievementModel::getTaskStatus, TaskStatusEnum.YES.getCode())
                    .update();

            mqOrderPaymentRepository.lambdaUpdate()
                    .eq(MqOrderPaymentInfoModel::getOrderId, mqModel.getOrderId())
                    .set(MqOrderPaymentInfoModel::getTaskStatus, TaskStatusEnum.YES.getCode())
                    .update();
        }

    }

    @Override
    public boolean selectAchievementInfo(Long orderId, String orderProductId) {
        List<AchievementProductDetailModel> list = achievementProductDetailRepository.list(
                new LambdaQueryWrapper<AchievementProductDetailModel>()
                        .eq(AchievementProductDetailModel::getOrderId, orderId)
                        .eq(AchievementProductDetailModel::getOrderProductId, orderProductId)
                        .eq(AchievementProductDetailModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode()));
        if (ObjectUtil.isEmpty(list)) {
            List<MqOrderPaymentInfoModel> existList =  mqOrderPaymentRepository.selectByOrderIdAndCalculateType(orderId,NumberConstants.INTEGER_VALUE_1);
            //如果没有生成支付完成 但是支付完成已经是完成状态 那么生产完成也不需要生成
            if(!CollectionUtils.isEmpty(existList)){
                //把生成完成mq状态改为完成
                mqOrderPaymentRepository.updateByOrderId(orderId,NumberConstants.INTEGER_VALUE_2);
                return false;
            }
            log.error(WebCodeMessageEnum.DATA_NOT_EXIST.getMsg() + "orderId:" + orderId + "orderProductId:" + orderProductId);
            return false;
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Lock("'updateAchievement_' + #updateRequest.getId()")
    public void updateAchievementAndSpec(AchievementUpdateRequest updateRequest) {
        Long id = updateRequest.getId();
        AchievementProductDetailModel existingProduct = achievementProductDetailRepository.lambdaQuery()
                .eq(AchievementProductDetailModel::getId, id)
                .eq(AchievementProductDetailModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                .one();
        Assert.notNull(existingProduct, () -> new BusinessException("该业绩流水不存在或已删除，请稍后重试"));
        // 检验当前商务月是否冻结
        if (Objects.nonNull(existingProduct.getBusinessMonthId())
                && businessMonthService.isMonthFrozen(existingProduct.getBusinessMonthId())) {
            freezeMonthErrorLogService.add(existingProduct.getBusinessMonthId(), existingProduct);
            throw new BusinessException(WebCodeMessageEnum.BUSINESS_MONTH_FROZEN_ERROR.getMsg());
        }
        AchievementProductDetailModel currentProduct = new AchievementProductDetailModel();
        currentProduct.setId(id);
        currentProduct.setAchievementId(existingProduct.getAchievementId());
        currentProduct.setOrderProductId(existingProduct.getOrderProductId());
        currentProduct.setCustomerType(updateRequest.getCustomerType());
        currentProduct.setMainSplitPerson(updateRequest.getMainSplitPerson());
        currentProduct.setBusinessId(StrUtil.trimToNull(updateRequest.getBusinessId()));
        currentProduct.setBusinessRepresentative(StrUtil.trimToNull(updateRequest.getBusinessRepresentative()));
        currentProduct.setDataChangeType(DataChangeTypeEnum.ARTIFICIAL_UPDATE.getChangeType());

        // 更新商品业绩对应的商务月
        Date statisticsTime = updateRequest.getStatisticsTime();
        if (ObjectUtil.isNotNull(statisticsTime)) {
            // 检验目标商务月是否冻结
            BusinessMonthModel monthInfo = businessMonthService.getMonthInfo(statisticsTime);
            if (businessMonthService.isMonthFrozen(monthInfo.getMonthId())) {
                freezeMonthErrorLogService.add(monthInfo.getMonthId(), existingProduct);
                throw new BusinessException(WebCodeMessageEnum.BUSINESS_MONTH_FROZEN_ERROR.getMsg());
            }
            currentProduct.setStatisticsTime(statisticsTime);
            Assert.notNull(monthInfo, () -> new BusinessException(StrUtil.format("根据业绩生成时间未找到商务月,业绩生成时间:{}",
                    DateUtil.format(statisticsTime, DatePattern.NORM_DATETIME_PATTERN))));
            currentProduct.setBusinessMonth(monthInfo.getMonth());
            currentProduct.setBusinessMonthId(monthInfo.getMonthId());
        }

        //修改saas新客中对应的商务信息
        if (Objects.equals(NumberConstants.INTEGER_VALUE_1, currentProduct.getMainSplitPerson())) {
            customerSaasService.updateHandler(existingProduct.getOrderId(),existingProduct.getProductId(),existingProduct.getOrderProductId(),currentProduct.getBusinessId(),currentProduct.getBusinessRepresentative());
        }


        // 更新规格业绩，并同步聚合更新分类业绩，聚合商品业绩数据，生成规格业绩变更日志
        List<OperateLogModel> operateLogs = new ArrayList<>();
        List<AchievementSpecUpdateRequest> specUpdateRequests = updateRequest.getSpecList();
        if (CollUtil.isNotEmpty(specUpdateRequests)) {
            this.updateSpecAndSyncCategoryWithLog(existingProduct, currentProduct, specUpdateRequests, operateLogs);
        }

        if (productAchievementChanged(existingProduct, currentProduct)) {
            if (dataChanged(existingProduct.getBusinessId(), currentProduct.getBusinessId())) {
                // 商务人员变更要同步修改所属机构信息
                UserInfoDetailResp userInfoDetailResp = innerService.getUserInfoDetail(currentProduct.getBusinessId());
                OrgPathInfoDTO orgPathInfoDTO = Optional.ofNullable(userInfoDetailResp).map(UserInfoDetailResp::getOrgPathInfoDTO)
                        .orElseThrow(() -> new BusinessException("该商务人员所属机构信息为空，businessId：" + currentProduct.getBusinessId()));
                currentProduct.setDivisionId(orgPathInfoDTO.getCareerId());
                currentProduct.setDivision(orgPathInfoDTO.getCareerName());
                currentProduct.setDeptId(orgPathInfoDTO.getDeptId());
                currentProduct.setDepartment(orgPathInfoDTO.getDeptName());
                currentProduct.setCompanyId(orgPathInfoDTO.getCompanyId());
                currentProduct.setCompany(orgPathInfoDTO.getCompanyName());
                currentProduct.setRegionId(orgPathInfoDTO.getAreaId());
            }
            OperateLogModel operateLog = operateLogService.createOperateLog(existingProduct, currentProduct, OperateTypeEnum.UPDATE,
                    SourceSystemEnum.ACHIEVEMENT, "商品业绩流水修改，商品流水Id：" + existingProduct.getAchievementId());
            operateLogs.add(operateLog);
        }

        if (CollUtil.isEmpty(operateLogs)) {
            log.info("更改商品业绩，业绩流水修改有请求但没有修改，请求参数：{}", JSON.toJSONString(updateRequest));
            return;
        }

        // 更新备注信息
        String history = existingProduct.getRemarkHistory();
        history = StrUtil.isBlank(history)
                ? existingProduct.getLatestRemark()
                : StrUtil.format("{}|{}", existingProduct.getLatestRemark(), history);
        currentProduct.setRemarkHistory(history);
        currentProduct.setLatestRemark(updateRequest.getRemark());

        log.info("更改商品业绩，商品业绩Id：{}，业绩数据：{}", currentProduct.getAchievementId(), currentProduct);
        achievementProductDetailRepository.updateById(currentProduct);

        operateLogService.saveBatch(operateLogs);

        eventPublisher.publishEvent(new ProductAchievementUpdatedEvent(this, existingProduct, currentProduct));
    }

    /**
     * 更新规格业绩，并同步聚合更新分类业绩，聚合商品业绩数据，生成规格业绩变更日志
     *
     * @param existingProduct    现有产品
     * @param currentProduct     当前产品
     * @param specUpdateRequests 规范更新请求
     * @param operateLogs        操作日志
     * <AUTHOR>
     * @since 1.0
     */
    private void updateSpecAndSyncCategoryWithLog(AchievementProductDetailModel existingProduct, AchievementProductDetailModel currentProduct,
            List<AchievementSpecUpdateRequest> specUpdateRequests, List<OperateLogModel> operateLogs) {
        List<AchievementSpecDetailModel> existingSpecs = achievementSpecDetailRepository.listByAchievementId(existingProduct.getAchievementId());
        List<AchievementSpecDetailModel> specUpdates = specUpdateRequests.stream()
                .map(specUpdateRequest -> {
                    AchievementSpecDetailModel existingSpec = existingSpecs.stream()
                            .filter(t -> ObjectUtil.equal(t.getId(), specUpdateRequest.getId()))
                            .findAny().orElseThrow(() -> new BusinessException("商品规格不存在，Id:" + specUpdateRequest.getId()));
                    if (!specAchievementChanged(existingSpec, specUpdateRequest)) {
                        return null;
                    }
                    AchievementSpecDetailModel currentSpec = buildSpecModelFromUpdate(specUpdateRequest);
                    // 为了记录日志
                    currentSpec.setAchievementSpecId(existingSpec.getAchievementSpecId());
                    currentSpec.setOrderProductId(existingSpec.getOrderProductId());
                    currentSpec.setSpecId(existingSpec.getSpecId());
                    currentSpec.setSpecName(existingSpec.getSpecName());

                    // 预生成变更日志对象
                    OperateLogModel operateLog = operateLogService.createOperateLog(existingSpec, currentSpec,
                            OperateTypeEnum.UPDATE, SourceSystemEnum.ACHIEVEMENT,
                            "商品规格业绩流水修改，规格流水Id：" + existingSpec.getAchievementSpecId() + "，商品流水Id：" + existingProduct.getAchievementId());
                    operateLogs.add(operateLog);
                    return currentSpec;
                }).filter(ObjectUtil::isNotNull).collect(Collectors.toList());

        if (CollUtil.isEmpty(specUpdates)) {
            return;
        }
        log.info("更改商品业绩，商品业绩Id：{}，规格业绩数据：{}", currentProduct.getAchievementId(), specUpdates);
        achievementSpecDetailRepository.updateBatchById(specUpdates);

        // 查询最新的规格业绩数据聚合商品业绩
        List<AchievementSpecDetailModel> specs = achievementSpecDetailRepository.listByAchievementId(existingProduct.getAchievementId());
        currentProduct.setNetCash(specs.stream().map(AchievementSpecDetailModel::getNetCash).reduce(BigDecimal.ZERO, BigDecimal::add));
        currentProduct.setAgentCommissionAchievement(specs.stream().map(AchievementSpecDetailModel::getAgentCommAchv).reduce(BigDecimal.ZERO, BigDecimal::add));
        currentProduct.setAgentActualCommission(specs.stream().map(AchievementSpecDetailModel::getAgentActCommAchv).reduce(BigDecimal.ZERO, BigDecimal::add));
        currentProduct.setAgentDeferredCommission(specs.stream().map(AchievementSpecDetailModel::getAgentDefCommAchv).reduce(BigDecimal.ZERO, BigDecimal::add));
        currentProduct.setDeptCommission(specs.stream().map(AchievementSpecDetailModel::getDeptCommAchv).reduce(BigDecimal.ZERO, BigDecimal::add));
        currentProduct.setDivCommission(specs.stream().map(AchievementSpecDetailModel::getBuCommAchv).reduce(BigDecimal.ZERO, BigDecimal::add));
        currentProduct.setBranchCommission(specs.stream().map(AchievementSpecDetailModel::getBranchCommAchv).reduce(BigDecimal.ZERO, BigDecimal::add));

        // 聚合规格分类业绩
        Map<Long, List<AchievementSpecDetailModel>> specsByCategory = specs.stream()
                .collect(Collectors.groupingBy(AchievementSpecDetailModel::getAchievementCategoryId));
        List<AchievementCategoryDetailModel> categoryUpdates = specsByCategory.entrySet().stream()
                .map(entry -> {
                    List<AchievementSpecDetailModel> categorySpecs = entry.getValue();
                    AchievementCategoryDetailModel categoryDetailModel = new AchievementCategoryDetailModel();
                    // 这里用achievementCategoryId返回了分类业绩流水表的Id
                    categoryDetailModel.setId(entry.getKey());
                    categoryDetailModel.setDataChangeType(DataChangeTypeEnum.ARTIFICIAL_UPDATE.getChangeType());
                    categoryDetailModel.setNetCash(categorySpecs.stream().map(AchievementSpecDetailModel::getNetCash).reduce(BigDecimal.ZERO, BigDecimal::add));
                    categoryDetailModel.setAgentCommAchv(categorySpecs.stream().map(AchievementSpecDetailModel::getAgentCommAchv).reduce(BigDecimal.ZERO, BigDecimal::add));
                    categoryDetailModel.setAgentActCommAchv(categorySpecs.stream().map(AchievementSpecDetailModel::getAgentActCommAchv).reduce(BigDecimal.ZERO, BigDecimal::add));
                    categoryDetailModel.setAgentDefCommAchv(categorySpecs.stream().map(AchievementSpecDetailModel::getAgentDefCommAchv).reduce(BigDecimal.ZERO, BigDecimal::add));
                    categoryDetailModel.setDeptCommAchv(categorySpecs.stream().map(AchievementSpecDetailModel::getDeptCommAchv).reduce(BigDecimal.ZERO, BigDecimal::add));
                    categoryDetailModel.setBuCommAchv(categorySpecs.stream().map(AchievementSpecDetailModel::getBuCommAchv).reduce(BigDecimal.ZERO, BigDecimal::add));
                    categoryDetailModel.setBranchCommAchv(categorySpecs.stream().map(AchievementSpecDetailModel::getBranchCommAchv).reduce(BigDecimal.ZERO, BigDecimal::add));
                    return categoryDetailModel;
                }).collect(Collectors.toList());
        log.info("更改商品业绩，商品业绩Id：{}，规格分类业绩数据：{}", currentProduct.getAchievementId(), categoryUpdates);
        achievementCategoryDetailRepository.updateBatchById(categoryUpdates);
    }

    private boolean dataChanged(Object existing, Object current) {
        return ObjectUtil.isNotEmpty(current) && ObjectUtil.notEqual(existing, current);
    }

    private boolean specAchievementChanged(AchievementSpecDetailModel existingSpec, AchievementSpecUpdateRequest currentSpec) {
        return dataChanged(existingSpec.getNetCash(), currentSpec.getNetCash())
                || dataChanged(existingSpec.getAgentCommAchv(), currentSpec.getAgentCommAchv())
                || dataChanged(existingSpec.getAgentActCommAchv(), currentSpec.getAgentActCommAchv())
                || dataChanged(existingSpec.getAgentDefCommAchv(), currentSpec.getAgentDefCommAchv())
                || dataChanged(existingSpec.getDeptCommAchv(), currentSpec.getDeptCommAchv())
                || dataChanged(existingSpec.getBuCommAchv(), currentSpec.getBuCommAchv())
                || dataChanged(existingSpec.getBranchCommAchv(), currentSpec.getBranchCommAchv());
    }

    private boolean productAchievementChanged(AchievementProductDetailModel existingProduct, AchievementProductDetailModel currentProduct) {
        return dataChanged(existingProduct.getStatisticsTime(), currentProduct.getStatisticsTime())
                || dataChanged(existingProduct.getBusinessMonthId(), currentProduct.getBusinessMonthId())
                || dataChanged(existingProduct.getBusinessMonth(), currentProduct.getBusinessMonth())
                || dataChanged(existingProduct.getCustomerType(), currentProduct.getCustomerType())
                || dataChanged(existingProduct.getMainSplitPerson(), currentProduct.getMainSplitPerson())
                || dataChanged(existingProduct.getBusinessId(), currentProduct.getBusinessId())
                || dataChanged(existingProduct.getBusinessRepresentative(), currentProduct.getBusinessRepresentative());
    }

    @Override
    public List<OrderProductResponse> importOrderProduct(MultipartFile file) throws Exception {
        List<OrderProductResponse> list = new ArrayList<>();
        Workbook workbook = WorkbookFactory.create(file.getInputStream());
        // 读取第一个 sheet
        Sheet sheet = workbook.getSheetAt(0);
        Iterator<Row> rowIterator = sheet.iterator();
        // 跳过标题行
        rowIterator.next();

        while (rowIterator.hasNext()) {
            Row row = rowIterator.next();
            if (isRowEmpty(row)) {
                continue;
            }
            OrderProductResponse response = new OrderProductResponse();
            response.setOrderNo(getStringCellValue(row.getCell(0)));
            response.setOrderProductId(getStringCellValue(row.getCell(1)));
            list.add(response);
        }
        workbook.close();
        return list;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void recalculateOrderProductAchievement(RecalculateOrderProductRequest request) {
        Map<String, List<OrderProductResponse>> groupedByOrderNo = request.getOrderProducts().stream().collect(Collectors.groupingBy(OrderProductResponse::getOrderNo));
        // 构建新的 Map<orderId, List<OrderProduct>>
        Map<Long, List<String>> orderIdGroup = new HashMap<>();
        Map<Long, List<Long>> serverFinish = new HashMap<>();
        List<Long> orderIds = Lists.newArrayList();
        for (Map.Entry<String, List<OrderProductResponse>> entry : groupedByOrderNo.entrySet()) {
            String orderNo = entry.getKey();
            List<String> orderProductIds = entry.getValue().stream().map(OrderProductResponse::getOrderProductId).collect(Collectors.toList());
            OrderSimpleInfoResponse orderSimpleInfoResponse = innerService.getOrderSimpleInfoByOrderNo(orderNo);
            if (ObjectUtil.isEmpty(orderSimpleInfoResponse)) {
                log.info("根据订单编号{}查询订单系统返回结果为空", orderNo);
                continue;
            }
            // 放入新 Map 中
            Long orderId = orderSimpleInfoResponse.getOrderId();
            orderIdGroup.put(orderId, orderProductIds);
            orderIds.add(orderId);
        }
        // 根据orderId 查询 mq
        for (Long orderId : orderIds) {
            List<MqOrderPaymentInfoModel> mqOrderPaymentInfoModels = mqOrderPaymentInfoRepository.selectListByOrderId(orderId);
            List<String> orderProductIds = orderIdGroup.get(orderId);
            /**
             * 分期的订单每一笔的订单明细编号一样只是分期号不一样，如果是分期的，则删除全部的分期订单，如果是重复加购的，则删除指定的那笔、
             */
            // 1.先删除对应的商品业绩
            List<AchievementProductDetailModel> productDetailModels = achievementProductDetailRepository.selectByOrderIdAndOrderProductId(orderId, orderProductIds);
            
            Long monthId=productDetailModels.get(0).getBusinessMonthId();
            if(!CollectionUtils.isEmpty(orderIds)&&businessMonthService.isMonthFrozen(monthId)){
                freezeMonthErrorLogService.add(monthId, productDetailModels);
                throw new BusinessException(WebCodeMessageEnum.BUSINESS_MONTH_FROZEN_ERROR.getMsg());
            }

            List<Long> achievementProductIds = productDetailModels.stream().map(AchievementProductDetailModel::getId).collect(Collectors.toList());
            List<Long> productIds = productDetailModels.stream().map(AchievementProductDetailModel::getProductId).collect(Collectors.toList());
            serverFinish.put(orderId, productIds);
            achievementProductDetailRepository.logicBatchDelete(achievementProductIds);
            List<Long> achievementIds = productDetailModels.stream().map(AchievementProductDetailModel::getAchievementId).collect(Collectors.toList());
            // 2.先删除对应的规格分类业绩
            achievementCategoryDetailRepositoryImpl.logicDeleteByAchievementId(achievementIds);
            // 3.先删除对应的规格业绩
            achievementSpecDetailRepository.logicDeleteByOrderIdAndOrderProductId(orderId, orderProductIds);

            //支付完成 重新新增
            List<MqOrderPaymentInfoModel> paymentModels = mqOrderPaymentInfoModels.stream().filter(v -> v.getCalculateType().equals(PAYMENT.getCode())).collect(Collectors.toList());
            for (MqOrderPaymentInfoModel model : paymentModels) {
                achievementHandler.handlerPaidAchievement(model, true, orderProductIds);
            }
            // 生产完成 重新新增
            List<MqOrderPaymentInfoModel> serveModels = mqOrderPaymentInfoModels.stream().filter(v -> v.getCalculateType().equals(SERVEINPROGRESS.getCode())).collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(serveModels)) {
                for (MqOrderPaymentInfoModel serveModel : serveModels) {
                    achievementHandler.newHandlerServiceAchievement(serveModel, false, orderProductIds);
                }
            }
            // 判断支付完成或者生产完成
//            boolean flag = mqOrderPaymentInfoModels.stream().anyMatch(v -> ObjectUtil.equals(v.getCalculateType(), SERVEINPROGRESS.getCustomerType()));
//            if (flag) {
//
//            } else {
//
//            }
        }
        // 根据orderId+productId 查服务完成时间
        List<MqServeFinishTimeInfoModel> mqServeFinishTimeInfoModels = Lists.newArrayList();
        for (Map.Entry<Long, List<Long>> entry : serverFinish.entrySet()) {
            List<MqServeFinishTimeInfoModel> list = mqServeFinishTimeInfoRepository.selectByOrderIdAndProductId(entry.getKey(), entry.getValue());
            mqServeFinishTimeInfoModels.addAll(list);
        }
        try {
            if (ObjectUtil.isNotEmpty(mqServeFinishTimeInfoModels)) {
                for (MqServeFinishTimeInfoModel mqServeFinishTimeInfoModel : mqServeFinishTimeInfoModels) {
                    Long orderId = mqServeFinishTimeInfoModel.getOrderId();
                    Long productId = mqServeFinishTimeInfoModel.getProductId();
                    Date serveFinishTime = mqServeFinishTimeInfoModel.getServeFinishTime();
                    LambdaQueryWrapper<AchievementProductDetailModel> query = new LambdaQueryWrapper<AchievementProductDetailModel>()
                            .eq(AchievementProductDetailModel::getOrderId, orderId)
                            .eq(AchievementProductDetailModel::getProductId, productId);
                    List<AchievementProductDetailModel> list = achievementProductDetailRepository.list(query);
                    if (ObjectUtil.isEmpty(list)) {
                        throw new BusinessException(WebCodeMessageEnum.DATA_NOT_EXIST.getMsg());
                    }
                    list.forEach(model -> model.setServeFinishTime(serveFinishTime));
                    achievementProductDetailRepository.saveOrUpdateBatch(list);
                }
            }
        } catch (Exception e) {
            log.error("更新服务完成时间失败", e);
        }

    }

    @Override
    public String[] recalculateAchievementByExcel(MultipartFile file) {
        List<RecalculateAchievementRequest> inputList = Lists.newArrayList();
        try {
            inputList = parseRecalculateAchievementExcel(file);
        } catch (Exception e) {
            log.error("解析Excel表格异常", e);
            throw new IllegalArgumentException("解析Excel表格异常");
        }
        //校验导入excel
        checkoutExcelDto(inputList);
        return inputList.stream().distinct().map(RecalculateAchievementRequest::getOrderNo).toArray(String[]::new);
    }

    private List<RecalculateAchievementRequest> parseRecalculateAchievementExcel(MultipartFile file) throws Exception {
        List<RecalculateAchievementRequest> list = new ArrayList<>();
        Workbook workbook = WorkbookFactory.create(file.getInputStream());
        // 读取第一个 sheet
        Sheet sheet = workbook.getSheetAt(0);
        Iterator<Row> rowIterator = sheet.iterator();
        // 跳过标题行
        rowIterator.next();

        while (rowIterator.hasNext()) {
            Row row = rowIterator.next();
            RecalculateAchievementRequest request = new RecalculateAchievementRequest();
            if(StringUtils.isNotBlank(getStringCellValue(row.getCell(0)))){
                request.setOrderNo(getStringCellValue(row.getCell(0)));
                list.add(request);
            }
        }
        workbook.close();
        return list;
    }

    @Transactional
    @Lock("'RecalculateAchievement'+#orderNoStr")
    @Override
    public void recalculateAchievement(String[] orderNos, String orderNoStr) {
        List<String> orderNoList = Lists.newArrayList(orderNos).stream().distinct().collect(Collectors.toList());
        String invalidNos = orderNoList.stream().filter(no -> StringUtils.isBlank(no)).collect(Collectors.joining(","));
        if (StringUtils.isNotBlank(invalidNos)) {
            throw new BusinessException("订单编号存在空字符，请核实！");
        }
        List<AchievementProductDetailModel> achievementProductDetailModels = achievementProductDetailRepository.list(new LambdaQueryWrapper<AchievementProductDetailModel>().in(AchievementProductDetailModel::getOrderNo, orderNoList).eq(BaseModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode()));
        if (CollUtil.isEmpty(achievementProductDetailModels)) {
            //throw new IllegalArgumentException("订单：" + orderNoList.stream().collect(Collectors.joining(",")) + "业绩未生成!");
            throw new IllegalArgumentException("订单或业绩数据不满足重算条件！:" + orderNoList.stream().collect(Collectors.joining(",")));
        }
        Map<String, List<AchievementProductDetailModel>> orderProductAchievementMap = achievementProductDetailModels.stream().collect(Collectors.groupingBy(AchievementProductDetailModel::getOrderNo));
        if (orderProductAchievementMap.keySet().size() != orderNoList.size()) {
            String invalidOrderNos = orderNoList.stream().filter(t -> !orderProductAchievementMap.keySet().contains(t)).collect(Collectors.joining(","));
            //throw new IllegalArgumentException("订单：" + invalidOrderNos + "业绩未生成!");
            throw new IllegalArgumentException("订单或业绩数据不满足重算条件！:" + invalidOrderNos);
        }
        //移除冻结商务月的数据
        achievementProductDetailModels.removeIf(model -> {
            if (businessMonthService.isMonthFrozen(model.getBusinessMonthId())) {
                freezeMonthErrorLogService.add(model.getBusinessMonthId(), model);
                return true; // 满足条件移除
            }
            return false;
        });

        List<Long> orderIdList = achievementProductDetailModels.stream().map(AchievementProductDetailModel::getOrderId).distinct().collect(Collectors.toList());
        //1、基础业绩数据
        List<AchievementSpecDetailModel> achievementSpecDetailModels = achievementSpecDetailRepository.list(new LambdaQueryWrapper<AchievementSpecDetailModel>().in(AchievementSpecDetailModel::getOrderId, orderIdList).eq(BaseModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode()));
        List<AchievementCategoryDetailModel> achievementCategoryDetailModels = achievementCategoryDetailRepository.list(Wrappers.lambdaQuery(AchievementCategoryDetailModel.class).in(AchievementCategoryDetailModel::getOrderId, orderIdList).eq(BaseModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode()));
        List<NewOldCustomerRecordModel> delNocrmList = new ArrayList<>();
        List<BusinessAchievementModel> delAchBsList = new ArrayList<>();

        List<Long> delAchPdmlList = achievementProductDetailModels.stream().map(AchievementProductDetailModel::getId).collect(Collectors.toList());
        List<Long> delAchSdmlList = achievementSpecDetailModels.stream().map(AchievementSpecDetailModel::getId).collect(Collectors.toList());
        List<Long> delAchCdmlList = achievementCategoryDetailModels.stream().map(AchievementCategoryDetailModel::getId).collect(Collectors.toList());

        if(CollUtil.isNotEmpty(delAchPdmlList)){
            LambdaUpdateWrapper<AchievementProductDetailModel> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.in(AchievementProductDetailModel::getId, delAchPdmlList);
            updateWrapper.set(AchievementProductDetailModel::getUpdateTime, new Date());
            updateWrapper.set(AchievementProductDetailModel::getDeleteFlag, DeleteFlagEnum.DELETE.getCode());
            achievementProductDetailRepository.update(null, updateWrapper);
        }
        if(CollUtil.isNotEmpty(delAchSdmlList)){
            LambdaUpdateWrapper<AchievementSpecDetailModel> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.in(AchievementSpecDetailModel::getId, delAchSdmlList);
            updateWrapper.set(AchievementSpecDetailModel::getUpdateTime, new Date());
            updateWrapper.set(AchievementSpecDetailModel::getDeleteFlag, DeleteFlagEnum.DELETE.getCode());
            achievementSpecDetailRepository.update(null, updateWrapper);
        }
        if(CollUtil.isNotEmpty(delAchCdmlList)){
            LambdaUpdateWrapper<AchievementCategoryDetailModel> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.in(AchievementCategoryDetailModel::getId, delAchCdmlList);
            updateWrapper.set(AchievementCategoryDetailModel::getUpdateTime, new Date());
            updateWrapper.set(AchievementCategoryDetailModel::getDeleteFlag, DeleteFlagEnum.DELETE.getCode());
            achievementCategoryDetailRepository.update(null, updateWrapper);
        }

        for (List<AchievementProductDetailModel> achList : orderProductAchievementMap.values()) {
            //2、删除新老客户记录 (原因是当支付完成计算业绩时，会计算新老客户写入new_old_customer_record，这里需要把写入的数据找出来进行删除)
            this.deleteNewOldCustomerRecord(achList, delNocrmList);
            //3、扣减商务业绩business_achievement
            //新的商务月业绩统计走 addBusinessNew 方法， 这里的扣减逻辑注释掉
            //ps:待学周syncBusinessAchievementStatisticsJob上线就可注释
//            List<MqOrderPaymentInfoModel> mqOrderPaymentInfoModels = orderPaymentInfoRepository.list(new LambdaQueryWrapper<MqOrderPaymentInfoModel>()
//                    .eq(MqOrderPaymentInfoModel::getOrderId, achList.get(0).getOrderId())
//                    .eq(MqOrderPaymentInfoModel::getTaskStatus, TaskStatusEnum.YES.getCode()));
            //deductionBsAchievement(mqOrderPaymentInfoModels, achievementProductDetailModels);
        }
        newOldCustomerRecordRepository.removeBatchByIds(delNocrmList);

        List<MqOrderPaymentInfoModel> mqOrderPaymentInfoModels = mqOrderPaymentRepository.list(new LambdaQueryWrapper<MqOrderPaymentInfoModel>()
                .in(MqOrderPaymentInfoModel::getOrderId, orderIdList)
                .eq(MqOrderPaymentInfoModel::getTaskStatus, TaskStatusEnum.YES.getCode())
                .eq(MqOrderPaymentInfoModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode()));
        //4、重置业绩生成task
        List<Long> updateMqOrderPaymentInfoModelList = mqOrderPaymentInfoModels.stream().map(MqOrderPaymentInfoModel::getId).collect(Collectors.toList());
        if(CollUtil.isNotEmpty(updateMqOrderPaymentInfoModelList)){
            LambdaUpdateWrapper<MqOrderPaymentInfoModel> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.in(MqOrderPaymentInfoModel::getId, updateMqOrderPaymentInfoModelList);
            updateWrapper.set(MqOrderPaymentInfoModel::getUpdateTime, new Date());
            updateWrapper.set(MqOrderPaymentInfoModel::getTaskStatus, String.valueOf(TaskStatusEnum.NO.getCode()));
            updateWrapper.set(MqOrderPaymentInfoModel::getFailCount, NumberConstants.INTEGER_VALUE_0);
            mqOrderPaymentRepository.update(null, updateWrapper);
        }
    }

    @Override
    public List<OpenAchievementResponse> openAchievementList(OpenAchievementRequest request) {
        List<Long> businessMonthIds = request.getBusinessMonthId();
        if (ObjectUtil.isEmpty(businessMonthIds)) {
            throw new BusinessException("商务月ID不能为空");
        }
        List<AchievementProductDetailModel> models = achievementProductDetailRepository.selectAchievement(request.getCompanyId(),
                request.getDeptId(), businessMonthIds, request.getBusinessId());
        return BeanUtil.copyToList(models, OpenAchievementResponse.class);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public WebResult<Boolean> deleteAchievement(DeleteAchievementProductRequest request) {

        List<Long> achivevementIdList = request.getAchivevementIdList();

        if(CollectionUtils.isEmpty(achivevementIdList)){
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_NOT_NULL);
        }
        //查询业绩
        List<AchievementProductDetailModel> achList = achievementProductDetailRepository.selectAchievementProductByAchievementIds(achivevementIdList,null);

        //分组
        Map<String, List<AchievementProductDetailModel>> byMonthMap = achList.stream().collect(Collectors.groupingBy(AchievementProductDetailModel::getBusinessMonth));
        for (Map.Entry<String, List<AchievementProductDetailModel>> monthMap : byMonthMap.entrySet()) {
            //检查商务月是否冻结
            boolean checkMonthFlag = businessMonthService.isMonthFrozen(monthMap.getKey());
            if(checkMonthFlag){
                log.warn(WebCodeMessageEnum.BUSINESS_MONTH_FROZEN_ERROR.getMsg());
                return WebResult.error(WebCodeMessageEnum.BUSINESS_MONTH_FROZEN_ERROR);
            }
        }

        List<AchievementCategoryDetailModel> categoryAchievements = achievementCategoryDetailRepository.lambdaQuery()
                .in(AchievementCategoryDetailModel::getAchievementId, achivevementIdList)
                .eq(AchievementCategoryDetailModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                .list();
        List<Long> achievementCategoryIds = categoryAchievements.stream()
                .map(AchievementCategoryDetailModel::getAchievementCategoryId).collect(Collectors.toList());

        //删除商品业绩
        achievementProductDetailRepository.deleteByAchievementId(achivevementIdList);
        //删除分类业绩
        achievementCategoryDetailRepository.logicDeleteByAchievementId(achivevementIdList);
        // 删除规格业绩
        if(!CollectionUtils.isEmpty(achievementCategoryIds)){
            achievementSpecDetailRepository.lambdaUpdate()
                    .in(AchievementSpecDetailModel::getAchievementCategoryId, achievementCategoryIds)
                    .eq(AchievementSpecDetailModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                    .set(AchievementSpecDetailModel::getDeleteFlag, DeleteFlagEnum.DELETE.getCode())
                    .update();
        }

        for (AchievementProductDetailModel achievementProductDetailModel : achList) {
            eventPublisher.publishEvent(new ProductAchievementUpdatedEvent(this, achievementProductDetailModel, null));
        }
        return WebResult.success();
    }

    private void deleteNewOldCustomerRecord(List<AchievementProductDetailModel> achList, List<NewOldCustomerRecordModel> delNocrmList) {
        //按支付时间分组绩效
        Map<Date, List<AchievementProductDetailModel>> payTimeAchMap = achList.stream().collect(Collectors.groupingBy(AchievementProductDetailModel::getCreateTime));
        List<AchievementProductDetailModel> dels = new ArrayList<>();
        for (List<AchievementProductDetailModel> value : payTimeAchMap.values()) {
            //业绩有修改就删除， 没有就不动（删了，job还是会生成）
            Optional<AchievementProductDetailModel> lastestUpdateModel = value.stream()
                    .filter(md -> md.getMainSplitPerson().equals(MainSubEnum.MAIN.getType())
                            && !md.getCustomerType().equals(CustomerType.NON_EXISTENT.getType())
                            && StringUtils.isNotBlank(md.getLatestRemark()))
                    .sorted(Comparator.comparing(AchievementProductDetailModel::getUpdateTime, Comparator.reverseOrder()))
                    .findFirst();
            if (lastestUpdateModel.isPresent()) {
                dels.add(lastestUpdateModel.get());
            }
        }
        if (CollUtil.isNotEmpty(dels)) {
            Map<String, List<NewOldCustomerRecordModel>> duplicateMap = Maps.newHashMap();
            for (AchievementProductDetailModel del : dels) {
                BusinessMonthModel monthInfo = businessMonthService.getMonthInfo(del.getStatisticsTime());
                if (null == monthInfo) {
                    continue;
                }
                StringBuffer duplicateKey = new StringBuffer("");
                duplicateKey.append(monthInfo.getMonthId()).append("_").append(del.getBusinessId()).append("_");
                LambdaQueryWrapper<NewOldCustomerRecordModel> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(NewOldCustomerRecordModel::getBusinessMonthId, monthInfo.getMonthId())
                        .eq(NewOldCustomerRecordModel::getEmployeeId, del.getBusinessId());
                if (CustomerType.NEW.getType().equals(del.getCustomerType())) {
                    duplicateKey.append(CustomerType.NEW.getType()).append("_");
                    duplicateKey.append(del.getCustomerId());
                    queryWrapper.eq(NewOldCustomerRecordModel::getNewCustomerId, del.getCustomerId());
                } else {
                    duplicateKey.append(CustomerType.OLD.getType()).append("_");
                    duplicateKey.append(del.getCustomerId());
                    queryWrapper.eq(NewOldCustomerRecordModel::getOldCustomerId, del.getCustomerId());
                }
                if (null == duplicateMap.get(duplicateKey.toString())) {
                    ArrayList<NewOldCustomerRecordModel> duplicateList = Lists.newArrayList();
                    List<NewOldCustomerRecordModel> list = newOldCustomerRecordRepository.list(queryWrapper);
                    if (CollUtil.isNotEmpty(list)) {
                        duplicateList.add(list.get(0));
                        duplicateMap.put(duplicateKey.toString(), duplicateList);
                        delNocrmList.add(list.get(0));
                    }
                } else {
                    //重复了，寻找另一个
                    List<NewOldCustomerRecordModel> duplicateList = duplicateMap.get(duplicateKey.toString());
                    Optional<NewOldCustomerRecordModel> another = newOldCustomerRecordRepository.list(queryWrapper).stream().filter(t ->
                            duplicateList.stream().noneMatch(t2 -> t2.getId().equals(t.getId()))
                    ).findFirst();
                    if (another.isPresent()) {
                        if (another.isPresent()) {
                            duplicateList.add(another.get());
                            duplicateMap.put(duplicateKey.toString(), duplicateList);
                            delNocrmList.add(another.get());
                        }
                    }
                }
            }
        }
    }

    private void checkoutExcelDto(List<RecalculateAchievementRequest> inputList) {
        //非空校验
        if(CollUtil.isEmpty(inputList)){
            throw new IllegalArgumentException("模板导入为空！");
        }
        //最多一百条限制
        if(inputList.size()>100){
            throw new IllegalArgumentException("模板导入上限一百条！");
        }
        List<String> orderNoList = inputList.stream().map(RecalculateAchievementRequest::getOrderNo).distinct().collect(Collectors.toList());
        List<AchievementProductDetailModel> achievementProductDetailModels = achievementProductDetailRepository.list(new LambdaQueryWrapper<AchievementProductDetailModel>().in(AchievementProductDetailModel::getOrderNo, orderNoList).eq(BaseModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode()));
        if(CollUtil.isEmpty(achievementProductDetailModels)){
            throw new IllegalArgumentException("订单或业绩数据不满足重算条件！:"+orderNoList.stream().collect(Collectors.joining(",")));
        }
        Map<String, List<AchievementProductDetailModel>> orderProductAchievementMap = achievementProductDetailModels.stream().collect(Collectors.groupingBy(AchievementProductDetailModel::getOrderNo));
        if(orderProductAchievementMap.keySet().size() != orderNoList.size()){
            List<String> invalidOrderNos = orderNoList.stream().filter(t -> !orderProductAchievementMap.keySet().contains(t)).collect(Collectors.toList());
            throw new IllegalArgumentException("订单或业绩数据不满足重算条件！:"+invalidOrderNos.stream().collect(Collectors.joining(",")));
        }
    }

    /** 处理 Excel 单元格数据 */
    private String getStringCellValue(Cell cell) {
        if (cell == null) {
            return "";
        }
        cell.setCellType(CellType.STRING);
        return cell.getStringCellValue().trim();
    }

    private boolean checkTaskStatus(Long orderId, Long productId, String serveNo) {
        return mqServeFinishTimeInfoService.checkTaskStatus(orderId, productId, serveNo);
    }

    private void thirdAchievementHandel(ThirdAchievementModel model, MqOrderPaymentInfoModel mqModel) throws ParseException {
        
        if (TaskTypeEnum.ADD.getMsg().equals(model.getTaskType())) {
            AchievementProductDetailModel saveModel = achievementProductWrapper.buildFromThirdAchievement(model, mqModel);
            achievementProductDetailRepository.save(saveModel);
            operateLogService.save(null, saveModel, OperateTypeEnum.CREATE, SourceSystemEnum.THIRDACHIEVEMENT, "中企业绩流水新增，三方业绩流水Id：" + saveModel.getThirdAchievementId());
            if (!ObjectUtil.equals(model.getDisplayed(), NumberConstants.INTEGER_VALUE_1)) {
                // 商务人员根据商品业绩统计
                // businessAchHandler.achStatistics(Collections.singletonList(saveModel), ProcessType.PAYMENT.getCode(), BusinessAchievementUpdateTypeEnum.NORMAL.getUpdateType());
            }
            return;
        }
        AchievementProductDetailModel dbModel = achievementProductDetailRepository.lambdaQuery()
                .eq(AchievementProductDetailModel::getAchievementSource, AchievementSourceEnum.ZHONGXIAO.getCode())
                .eq(AchievementProductDetailModel::getThirdAchievementId, model.getThirdId())
                .eq(AchievementProductDetailModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                .orderByDesc(AchievementProductDetailModel::getUpdateTime)
                .last("limit 1").one();
        if (TaskTypeEnum.UPDATE.getMsg().equals(model.getTaskType())) {
            AchievementProductDetailModel currentModel = achievementProductWrapper.buildFromThirdAchievement(model, mqModel);
            if (ObjectUtil.isNull(dbModel)) {
                operateLogService.save(dbModel, currentModel, OperateTypeEnum.CREATE, SourceSystemEnum.THIRDACHIEVEMENT, "中企业绩流水修改，三方业绩流水Id：" + currentModel.getThirdAchievementId());
            } else {
                log.info("中小三方业绩流水数据修改，原数据：{}，修改数据：{}", dbModel, currentModel);
                achievementProductDetailRepository.lambdaUpdate()

                        .eq(AchievementProductDetailModel::getId, dbModel.getId())
                        .set(AchievementProductDetailModel::getDeleteFlag, DeleteFlagEnum.DELETE.getCode())
                        .update();
                operateLogService.save(dbModel, currentModel, OperateTypeEnum.UPDATE, SourceSystemEnum.THIRDACHIEVEMENT, "中企业绩流水修改，三方业绩流水Id：" + currentModel.getThirdAchievementId());
            }
            achievementProductDetailRepository.save(currentModel);
            if (!ObjectUtil.equals(model.getDisplayed(), NumberConstants.INTEGER_VALUE_1)) {
                // 商务人员根据商品业绩统计，要先减去之前的业绩，再加上最新的业绩
                // businessAchHandler.achStatistics(Collections.singletonList(dbModel), ProcessType.PAYMENT.getCode(), BusinessAchievementUpdateTypeEnum.DELETE.getUpdateType());
                // businessAchHandler.achStatistics(Collections.singletonList(currentModel), ProcessType.PAYMENT.getCode(), BusinessAchievementUpdateTypeEnum.NORMAL.getUpdateType());
            }
            return;
        }
        if (TaskTypeEnum.DELETE.getMsg().equals(model.getTaskType())) {
            if (ObjectUtil.isNull(dbModel)) {
                log.info("中小三方业绩流水数据逻辑删除，原数据已删除，参数：{}", model);
                return;
            }
            log.info("中小三方业绩流水数据逻辑删除，原数据：{}", dbModel);
            achievementProductDetailRepository.lambdaUpdate()
                    .eq(AchievementProductDetailModel::getId, dbModel.getId())
                    .set(AchievementProductDetailModel::getDeleteFlag, DeleteFlagEnum.DELETE.getCode())
                    .update();
            operateLogService.save(dbModel, null, OperateTypeEnum.DELETE, SourceSystemEnum.THIRDACHIEVEMENT, "中企业绩流水逻辑删除，三方业绩流水Id：" + dbModel.getThirdAchievementId());
            if (!ObjectUtil.equals(model.getDisplayed(), NumberConstants.INTEGER_VALUE_1)) {
                // 商务人员根据商品业绩统计，减去之前的业绩
                // businessAchHandler.achStatistics(Collections.singletonList(dbModel), ProcessType.PAYMENT.getCode(), BusinessAchievementUpdateTypeEnum.DELETE.getUpdateType());
            }
        }
    }

    /**
     * 从更新参数构建规格更新对象
     *
     * @param specUpdateRequest 规格更新请求参数
     * @return {@link AchievementSpecDetailModel }
     * <AUTHOR>
     * @since 1.0
     */
    private AchievementSpecDetailModel buildSpecModelFromUpdate(AchievementSpecUpdateRequest specUpdateRequest) {
        AchievementSpecDetailModel specModel = new AchievementSpecDetailModel();
        specModel.setId(specUpdateRequest.getId());
        if (ObjectUtil.isNotNull(specUpdateRequest.getNetCash())) {
            specModel.setNetCash(specUpdateRequest.getNetCash());
        }
        if (ObjectUtil.isNotNull(specUpdateRequest.getAgentCommAchv())) {
            specModel.setAgentCommAchv(specUpdateRequest.getAgentCommAchv());
        }
        if (ObjectUtil.isNotNull(specUpdateRequest.getAgentActCommAchv())) {
            specModel.setAgentActCommAchv(specUpdateRequest.getAgentActCommAchv());
        }
        if (ObjectUtil.isNotNull(specUpdateRequest.getAgentDefCommAchv())) {
            specModel.setAgentDefCommAchv(specUpdateRequest.getAgentDefCommAchv());
        }
        if (ObjectUtil.isNotNull(specUpdateRequest.getDeptCommAchv())) {
            specModel.setDeptCommAchv(specUpdateRequest.getDeptCommAchv());
        }
        if (ObjectUtil.isNotNull(specUpdateRequest.getBuCommAchv())) {
            specModel.setBuCommAchv(specUpdateRequest.getBuCommAchv());
        }
        if (ObjectUtil.isNotNull(specUpdateRequest.getBranchCommAchv())) {
            specModel.setBranchCommAchv(specUpdateRequest.getBranchCommAchv());
        }
        specModel.setDataChangeType(DataChangeTypeEnum.ARTIFICIAL_UPDATE.getChangeType());
        return specModel;
    }

    private boolean isRowEmpty(Row row) {
        if (row == null) return true;
        for (int c = row.getFirstCellNum(); c < row.getLastCellNum(); c++) {
            Cell cell = row.getCell(c);
            if (cell != null && cell.getCellType() != CellType.BLANK) {
                return false;
            }
        }
        return true;
    }

    @Override
    @Lock("'recalculateRefund'+#achievementId")
    @Transactional(rollbackFor = Exception.class)
    public Boolean recalculateRefund(Long achievementId) {
        log.info("开始重算退款，业绩ID: {}", achievementId);

        // 1. 用achievementId查achievement_product_detail表获取到唯一的数据model
        LambdaQueryWrapper<AchievementProductDetailModel> queryWrapper = new LambdaQueryWrapper<>();
       
        queryWrapper.eq(AchievementProductDetailModel::getAchievementId, achievementId)
            .eq(AchievementProductDetailModel::getIsAbnormal, AdnormalEnum.ABNORMAL.getCode());


        AchievementProductDetailModel productDetail = achievementProductDetailRepository.getOne(queryWrapper);
        if (productDetail == null) {
            log.warn("未找到业绩记录，业绩ID: {}", achievementId);
            return false;
        }

        // 2. 调用新方法处理退款重算
        processRefundRecalculation(productDetail.getOrderNo());

        productDetail.setIsAbnormal(AdnormalEnum.NORMAL.getCode());
        achievementProductDetailRepository.updateById(productDetail);
        return true;
    }

    /**
     * 处理退款重算
     * @param orderNo 订单号
     * @return 处理结果
     */
    private void processRefundRecalculation(String orderNo) {
        log.info("处理退款重算，订单号: {}", orderNo);

        // 检测是否有已执行的退款
        LambdaQueryWrapper<MqOrderRefundInfoModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MqOrderRefundInfoModel::getOrderNo, orderNo)
                .eq(MqOrderRefundInfoModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                .eq(MqOrderRefundInfoModel::getTaskStatus, TaskStatusEnum.YES.getCode())
                .last("limit 1");
        MqOrderRefundInfoModel refundInfo = mqOrderRefundInfoRepository.getOne(queryWrapper);

        if (refundInfo == null) {
            return ;
        }
        refundInfo.setTaskStatus(TaskStatusEnum.NO.getCode());
        //删除所有退款记录
        LambdaUpdateWrapper<AchievementProductDetailModel> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(AchievementProductDetailModel::getOrderNo, orderNo)
                .eq(AchievementProductDetailModel::getStatus, AchStatus.REFUND.getType())
                .eq(AchievementProductDetailModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                .set(AchievementProductDetailModel::getDeleteFlag, DeleteFlagEnum.DELETE.getCode());
        achievementProductDetailRepository.update(updateWrapper);

        log.info("已删除原有退款记录，订单号: {}", orderNo);

        achievementRefundHandler.processRefundTask(refundInfo);        

        //检测转款
        if (!refundInfo.getSaleType().equals(SaleTypeEnum.TRANSFER.getCode())||
            Objects.isNull(refundInfo.getAftersaleOrderId())) {
            return;
        }
       
        LambdaQueryWrapper<MqOrderPaymentInfoModel> paymentInfo = new LambdaQueryWrapper<>();
        paymentInfo.eq(MqOrderPaymentInfoModel::getOrderId, refundInfo.getTransferInOrderId())
                .eq(MqOrderPaymentInfoModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                .eq(MqOrderPaymentInfoModel::getCalculateType,CalculateTypeEnum.PAYMENT.getCode())
                .eq(MqOrderPaymentInfoModel::getTaskStatus,  TaskStatusEnum.YES.getCode())
                .last("limit 1");
        MqOrderPaymentInfoModel paymentInfoModel = mqOrderPaymentInfoRepository.getOne(paymentInfo);

        if (Objects.isNull(paymentInfoModel)) {
            return;
        }
        
        //删除转入订单业绩
        LambdaUpdateWrapper<AchievementProductDetailModel> transferUpdateWrapper = new LambdaUpdateWrapper<>();
        transferUpdateWrapper.eq(AchievementProductDetailModel::getOrderId, paymentInfoModel.getOrderId())
                .eq(AchievementProductDetailModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                .set(AchievementProductDetailModel::getDeleteFlag, DeleteFlagEnum.DELETE.getCode());
        achievementProductDetailRepository.update(transferUpdateWrapper);

        //重新生成转入订单业绩
        paymentInfoModel.setTaskStatus(String.valueOf(TaskStatusEnum.NO.getCode()));
        achievementHandler.processAchievement(paymentInfoModel);
        //递归检测转入订单是否有退款记录
        processRefundRecalculation(refundInfo.getTransferInOrderNo());
        
    }
}
