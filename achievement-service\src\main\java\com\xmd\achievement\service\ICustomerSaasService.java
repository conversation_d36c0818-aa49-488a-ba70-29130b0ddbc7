package com.xmd.achievement.service;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import com.xmd.achievement.dao.dto.CustomerSaasGroupDto;
import com.xmd.achievement.dao.entity.CustomerSaasModel;
import com.xmd.achievement.dao.entity.MqOrderPaymentInfoModel;
import com.xmd.achievement.dao.entity.MqOrderRefundInfoModel;
import com.xmd.achievement.service.entity.request.CustomerSaasGroupQueryRequest;
import com.xmd.achievement.service.entity.response.CustomerChurnResponse;
import com.xmd.achievement.web.entity.base.PageResponse;

public interface ICustomerSaasService {
    public void customerSaasJob();
    
    public void recalculateHistory();

    public void handler(MqOrderPaymentInfoModel model);

    public void  refundHandler(Long orderId,Long productId,String orderProductCode);

    public void refundHandler(MqOrderRefundInfoModel refundTask);

    public void deleteHandler(Long orderId, Long productId,String orderProductCode);

    public void updateHandler(Long orderId, Long productId,String orderProductCode, String businessId,String businessRepresentative);

    public List<CustomerSaasModel> searchCustomerSaas(List<String> businessIds, String startDate, String endDate);

    public int searchCount(String businessId, Long monthId);

    public void recalculateAllSaasStatus();

    /**
     * 获取客户流失日期列表（带缓存）
     * @param customerId 客户ID
     * @return 客户流失日期列表
     */
    public List<String> getCustomerLossDateListWithCache(String customerId);

    /**
     * 获取客户流失信息（带缓存）
     * @param customerId 客户ID
     * @return 客户流失信息
     */
    public CustomerChurnResponse getCustomerChurnInfoWithCache(String customerId);

    /**
     * 获取SaaS相关Redis值
     * @return 包含两个key的值的Map
     */
    public Map<String, String> getSaasRedisValues();

    /**
     * 清除客户SaaS Redis值
     * @return 是否清除成功
     */
    public boolean clearCustomerSaasRedisValue();

    /**
     * 清除SaaS状态重算 Redis值
     * @return 是否清除成功
     */
    public boolean clearSaasStatusRecalcRedisValue();

    boolean clearRetryRedisValue();

    /**
     * 根据商务月、客户ID、商务ID分组查询客户SaaS信息，支持名称模糊查询（分页）
     * @param request 查询请求参数
     * @return 分页查询结果
     */
    PageResponse<CustomerSaasGroupDto> searchCustomerSaasWithConditionsPage(CustomerSaasGroupQueryRequest request);

    /**
     * 导出SAAS新客户分组信息到Excel
     * @param request 查询请求参数
     * @param response HTTP响应对象
     */
    void exportCustomerSaasWithConditions(CustomerSaasGroupQueryRequest request, HttpServletResponse response);
}
