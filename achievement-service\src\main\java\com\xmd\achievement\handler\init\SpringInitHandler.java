
package com.xmd.achievement.handler.init;

import javax.annotation.Resource;

import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

@Component
public class SpringInitHandler {

	@Resource
	private InitAsyncService initAsyncService;

	/**
	 * Spring初始化完成后执行的方法
	 */
	@EventListener(ApplicationReadyEvent.class)
	public void onApplicationReady(ApplicationReadyEvent event) {
		// 异步执行重试逻辑，避免阻塞 Spring 启动主线程
		initAsyncService.retryRecalculateHistory();
	}

}
