package com.xmd.achievement.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.xmd.achievement.dao.entity.*;
import com.xmd.achievement.dao.repository.*;
import com.xmd.achievement.handler.calculateCustomer.CalculateCustomerContext;
import com.xmd.achievement.rpc.InnerService;
import com.xmd.achievement.rpc.entity.dto.*;
import com.xmd.achievement.service.*;
import com.xmd.achievement.service.entity.dto.*;
import com.xmd.achievement.service.entity.request.ExcelProductRequest;
import com.xmd.achievement.service.entity.response.AchievementProductDetailExcel;
import com.xmd.achievement.service.entity.response.AchievementProductSpecDetailExcel;
import com.xmd.achievement.support.constant.NumberConstants;
import com.xmd.achievement.support.constant.enums.*;
import com.xmd.achievement.support.constant.exceptions.BusinessException;
import com.xmd.achievement.util.enums.*;
import com.xmd.achievement.web.config.ProductAchievementConfig;
import com.xmd.achievement.web.entity.response.WebCodeMessageEnum;
import com.xmd.achievement.web.util.DateUtils;
import com.xmd.achievement.web.util.EasyExcelUtil;
import com.xmd.achievement.web.util.ExcelUtil;
import com.xmd.achievement.web.util.UserContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 业绩的导入导出服务
 *
 * <AUTHOR>
 * @since 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AchievementFileServiceImpl implements AchievementFileService {

    private final InnerService innerService;
    private final IBusinessMonthService businessMonthService;
    private final IAchievementProductDetailService achievementProductService;
    private final IAchievementCategoryDetailService achievementCategoryService;
    private final IAchievementSpecDetailService achievementSpecService;
    private final CalculateCustomerContext calculateCustomerContext;
    private final IPolicySpecDetailRepository policySpecDetailRepository;
    private final IAchievementProductDetailRepository achievementProductDetailRepository;
    private final IAchievementCategoryDetailRepository achievementCategoryDetailRepository;
    private final IAchievementSpecDetailRepository achievementSpecDetailRepository;
    private final IMqOrderPaymentInfoRepository mqOrderPaymentInfoRepository;
    private final IThirdAchievementRepository thirdAchievementRepository;
    private final ISaasTabService saasTabService;
    private final IFreezeMonthErrorLogService freezeMonthErrorLogService;
    private final ICustomerSaasService customerSaasService;
    private final TransactionTemplate transactionTemplate;

    //注入自己
    @Resource
    private AchievementFileService self;
    @Resource
    private ProductAchievementConfig productAchievementConfig;

    /**
     * 校验商品业绩流水导入文件
     *
     * @param file 文件
     */
    @Override
    public void verifyFile(MultipartFile file) throws Exception {
        // 解析为 Java 对象列表 校验逻辑后续补充
        parseExcel(file);
    }

    /**
     * 校验规格业绩流水导入文件
     *
     * @param file 文件
     */
    @Override
    public void verifySpecFile(MultipartFile file) throws Exception {
        parseSpecExcel(file);
    }

    /**
     * 校验规格业绩流水退转款导入文件
     *
     * @param file 文件
     */
    @Override
    public void verifySpecRefundFile(MultipartFile file) throws Exception {
        parseSpecRefundExcel(file);
    }


    /**
     * 导入商品业绩流水
     *
     * @param file 文件
     */
    @Override
    public void excelFlowing(MultipartFile file) throws Exception {
        List<AchievementUploadDto> uploadList = parseExcel(file);
        if (CollUtil.isEmpty(uploadList)) {
            return;
        }
        self.handleUploadFlowing(uploadList, false);
    }

    /**
     * 导入规格业绩流水
     *
     * @param file 文件
     */
    @Override
    public void excelSpecFlowing(MultipartFile file) throws Exception {
        List<AchievementUploadDto> uploadList = parseSpecExcel(file);
        if (CollUtil.isEmpty(uploadList)) {
            return;
        }
        self.handleUploadFlowing(uploadList, true);
    }

    /**
     * 导入规格业绩流水退转款
     *
     * @param file 文件
     */
    @Override
    public void excelSpecRefundFlowing(MultipartFile file) throws Exception {
        List<AchievementUploadDto> uploadList = parseSpecRefundExcel(file);
        if (CollUtil.isEmpty(uploadList)) {
            return;
        }
        self.handleUploadFlowing(uploadList, true);
    }

    /**
     * 导出商品业绩
     *
     * @param search   查询条件
     * @param response 响应
     */
    @Override
    public void exportProductAchievement(ExcelProductRequest search, HttpServletResponse response) {
        UserLoginInfoDTO userInfo = UserContext.getCurrentUserInfo();
        UserDataPermissionsDTO userDataPermissionsDTO = UserContext.getCurrentUserInfo().getUserDataPermissionsDTO();
        if (Objects.isNull(userDataPermissionsDTO.getDataPermissionsType()) || DataPermitEnum.NO_PERMIT.getCode().equals(userDataPermissionsDTO.getDataPermissionsType())) {
            return;
        }
        LambdaQueryWrapper<AchievementProductDetailModel> queryWrapper = searchProductExcel(search, userInfo);
        List<AchievementProductDetailModel> list = achievementProductDetailRepository.list(queryWrapper);
        List<AchievementProductDetailExcel> collect = list.stream().map(model -> {
            AchievementProductDetailExcel excel = new AchievementProductDetailExcel();
            BeanUtils.copyProperties(model, excel);
            excel.setAchievementId(model.getAchievementId() + "");
            if (model.getCompanyId() != null) {
                excel.setCompanyId(model.getCompanyId() + "");
            }
            if (model.getDeptId() != null) {
                excel.setDeptId(model.getDeptId() + "");
            }
            if (model.getDivisionId() != null) {
                excel.setDivisionId(model.getDivisionId() + "");
            }
            excel.setOrderProductId(model.getOrderProductId());
            excel.setMainSplitPerson(MainSubEnum.getDescriptionByType(model.getMainSplitPerson()));
            excel.setCustomerType(CustomerType.getDescriptionByType(model.getCustomerType()));
            excel.setSaleType(OrderSaleTypeEnum.getOrderSaleTypeByType(model.getSaleType()).getVisitorCode());
            excel.setStatus(AchStatus.getDescriptionByType(model.getStatus()));

            Integer dataChangeType = model.getDataChangeType();
            if (DataChangeTypeEnum.NORMAL.getChangeType().equals(dataChangeType)) {
                excel.setDataChangeType(DataChangeTypeEnum.NORMAL.getDescription());
            }
            if (DataChangeTypeEnum.ARTIFICIAL_NEW.getChangeType().equals(dataChangeType)) {
                excel.setDataChangeType(DataChangeTypeEnum.ARTIFICIAL_NEW.getDescription());
            }
            if (DataChangeTypeEnum.ARTIFICIAL_UPDATE.getChangeType().equals(dataChangeType)) {
                excel.setDataChangeType(DataChangeTypeEnum.ARTIFICIAL_UPDATE.getDescription());
            }
            excel.setPaymentTime(model.getStatisticsTime());
            excel.setIsSaas(SaasEnum.getByCode(model.getIsSaas()) == null ? "未知" : SaasEnum.getByCode(model.getIsSaas()).getMsg());
            return excel;
        }).collect(Collectors.toList());
        try {
            EasyExcelUtil.download(response, collect, "业绩数据导出", Boolean.TRUE);
        } catch (IOException e) {
            log.error("导出产品业绩失败", e);
        }

    }

    /**
     * 导出商品规格业绩
     *
     * @param search
     * @param response
     */
    @Override
    public void exportProductSpecAchievement(ExcelProductRequest search, HttpServletResponse response) {
        UserLoginInfoDTO userInfo = UserContext.getCurrentUserInfo();
        UserDataPermissionsDTO userDataPermissionsDTO = UserContext.getCurrentUserInfo().getUserDataPermissionsDTO();
        if (Objects.isNull(userDataPermissionsDTO.getDataPermissionsType()) || DataPermitEnum.NO_PERMIT.getCode().equals(userDataPermissionsDTO.getDataPermissionsType())) {
            return;
        }
        if (NumberConstants.INTEGER_VALUE_2.equals(search.getAchievementSource())) {
            return;
        }
        if (NumberConstants.INTEGER_VALUE_0.equals(search.getAchievementSource())) {
            search.setAchievementSource(NumberConstants.INTEGER_VALUE_1);
        }
        // 根据条件查询商品业绩数据
        LambdaQueryWrapper<AchievementProductDetailModel> queryWrapper = searchProductExcel(search, userInfo);
        List<AchievementProductDetailModel> achievementProductDetailModelList = achievementProductDetailRepository.list(queryWrapper);
        if (CollectionUtils.isEmpty(achievementProductDetailModelList)) {
            return;
        }
        // 业绩流水id
        List<Long> achievementIdList = achievementProductDetailModelList.stream()
                .map(AchievementProductDetailModel::getAchievementId).collect(Collectors.toList());
        // 通过业绩流水id查询业绩规格分类明细
        List<AchievementCategoryDetailModel> achievementCategoryDetailModelList = achievementCategoryDetailRepository.selectAchievementCategoryByAchievementIds(achievementIdList);
        if (CollectionUtils.isEmpty(achievementCategoryDetailModelList)) {
            return;
        }
        // 根据业绩流水ID分组
        Map<Long, List<AchievementCategoryDetailModel>> achievementCategoryIdAndEntityMap = achievementCategoryDetailModelList.stream()
                .collect(Collectors.groupingBy(AchievementCategoryDetailModel::getAchievementId));

        // 根据业绩规格分类id查询业绩规格数据
        List<Long> achievementCategoryIdList = achievementCategoryDetailModelList.stream()
                .map(AchievementCategoryDetailModel::getAchievementCategoryId).collect(Collectors.toList());
        List<AchievementSpecDetailModel> achievementSpecDetailModelList = achievementSpecDetailRepository.selectAchievementSpecListByCategoryIds(achievementCategoryIdList);
        if (CollectionUtils.isEmpty(achievementSpecDetailModelList)) {
            return;
        }
        // 根据业绩规格分类id分组
        Map<Long, List<AchievementSpecDetailModel>> achievementSpecDetailAndEntityMap = achievementSpecDetailModelList.stream()
                .collect(Collectors.groupingBy(AchievementSpecDetailModel::getAchievementCategoryId));

        List<AchievementProductSpecDetailExcel> excelList = new ArrayList<>();

        for (AchievementProductDetailModel achievementProductDetailModel : achievementProductDetailModelList) {

            Long achievementId = achievementProductDetailModel.getAchievementId();
            List<AchievementCategoryDetailModel> achievementCategoryDetailModels = achievementCategoryIdAndEntityMap.get(achievementId);
            if (CollectionUtils.isEmpty(achievementCategoryDetailModels)) {
                log.info("exportProductSpecAchievement 业绩规格分类明细数据为空，achievementId:{}", achievementId);
                continue;
            }

            for (AchievementCategoryDetailModel achievementCategoryDetailModel : achievementCategoryDetailModels) {
                Long achievementCategoryId = achievementCategoryDetailModel.getAchievementCategoryId();

                List<AchievementSpecDetailModel> achievementSpecDetailModels = achievementSpecDetailAndEntityMap.get(achievementCategoryId);
                if (CollectionUtils.isEmpty(achievementSpecDetailModels)) {
                    log.info("exportProductSpecAchievement 业绩规格数据为空，achievementCategoryId:{}", achievementCategoryId);
                    continue;
                }
                // 规格分类名称
                String categoryName = achievementCategoryDetailModel.getCategoryName();
                Long categoryId = achievementCategoryDetailModel.getCategoryId();

                for (AchievementSpecDetailModel achievementSpecDetailModel : achievementSpecDetailModels) {
                    // 规格名称
                    String specName = achievementSpecDetailModel.getSpecName();
                    Long specId = achievementSpecDetailModel.getSpecId();

                    AchievementProductSpecDetailExcel excel = new AchievementProductSpecDetailExcel();
                    BeanUtils.copyProperties(achievementProductDetailModel, excel);

                    excel.setStandardPrice(achievementSpecDetailModel.getStandardPrice());
                    excel.setPayableAmount(achievementSpecDetailModel.getPayableAmount());
                    excel.setPaidAmount(achievementSpecDetailModel.getPaidAmount());
                    excel.setFirstYearRevenue(achievementSpecDetailModel.getFirstYearIncome());
                    excel.setFirstYearQuote(achievementSpecDetailModel.getFirstYearQuote());
                    excel.setRenewalQuote(achievementSpecDetailModel.getRenewalQuote());
                    excel.setRenewalRevenue(achievementSpecDetailModel.getRenewalIncome());
                    excel.setNetCash(achievementSpecDetailModel.getNetCash());
                    excel.setAgentCommissionAchievement(achievementSpecDetailModel.getAgentCommAchv());
                    excel.setAgentActualCommission(achievementSpecDetailModel.getAgentActCommAchv());
                    excel.setAgentDeferredCommission(achievementSpecDetailModel.getAgentDefCommAchv());
                    excel.setDeptCommission(achievementSpecDetailModel.getDeptCommAchv());
                    excel.setDivCommission(achievementSpecDetailModel.getBuCommAchv());
                    excel.setBranchCommission(achievementSpecDetailModel.getBranchCommAchv());
                    excel.setIsSaas(SaasEnum.getByCode(achievementSpecDetailModel.getIsSaas()) == null ? "未知" : SaasEnum.getByCode(achievementSpecDetailModel.getIsSaas()).getMsg());
                    excel.setAchievementId(achievementProductDetailModel.getAchievementId() + "");
                    if (achievementProductDetailModel.getCompanyId() != null) {
                        excel.setCompanyId(achievementProductDetailModel.getCompanyId() + "");
                    }
                    if (achievementProductDetailModel.getDeptId() != null) {
                        excel.setDeptId(achievementProductDetailModel.getDeptId() + "");
                    }
                    if (achievementProductDetailModel.getDivisionId() != null) {
                        excel.setDivisionId(achievementProductDetailModel.getDivisionId() + "");
                    }
                    excel.setOrderProductId(achievementProductDetailModel.getOrderProductId());
                    excel.setMainSplitPerson(MainSubEnum.getDescriptionByType(achievementProductDetailModel.getMainSplitPerson()));
                    excel.setCustomerType(CustomerType.getDescriptionByType(achievementProductDetailModel.getCustomerType()));
                    excel.setSaleType(OrderSaleTypeEnum.getOrderSaleTypeByType(achievementProductDetailModel.getSaleType()).getVisitorCode());
                    excel.setStatus(AchStatus.getDescriptionByType(achievementProductDetailModel.getStatus()));

                    Integer dataChangeType = achievementSpecDetailModel.getDataChangeType();
                    if (DataChangeTypeEnum.NORMAL.getChangeType().equals(dataChangeType)) {
                        excel.setDataChangeType(DataChangeTypeEnum.NORMAL.getDescription());
                    }
                    if (DataChangeTypeEnum.ARTIFICIAL_NEW.getChangeType().equals(dataChangeType)) {
                        excel.setDataChangeType(DataChangeTypeEnum.ARTIFICIAL_NEW.getDescription());
                    }
                    if (DataChangeTypeEnum.ARTIFICIAL_UPDATE.getChangeType().equals(dataChangeType)) {
                        excel.setDataChangeType(DataChangeTypeEnum.ARTIFICIAL_UPDATE.getDescription());
                    }

                    excel.setCategoryId(Convert.toStr(categoryId));
                    excel.setCategoryName(categoryName);
                    excel.setSpecId(Convert.toStr(specId));
                    excel.setSpecName(specName);
                    excel.setPaymentTime(achievementProductDetailModel.getStatisticsTime());
                    excelList.add(excel);
                }
            }
        }

        try {
            EasyExcelUtil.download(response, excelList, "业绩规格数据导出", Boolean.TRUE);
        } catch (IOException e) {
            log.error("导出业绩规格数据失败", e);
        }
    }

    /**
     * 解析导入的商品业绩文档
     *
     * @return List<AchievementUploadDto>
     * @throws Exception 异常
     */
    private List<AchievementUploadDto> parseExcel(MultipartFile file) throws Exception {
        List<AchievementUploadDto> list = new ArrayList<>();
        Workbook workbook = WorkbookFactory.create(file.getInputStream());
        // 读取第一个 sheet
        Sheet sheet = workbook.getSheetAt(0);
        Iterator<Row> rowIterator = sheet.iterator();
        // 跳过标题行
        rowIterator.next();

        while (rowIterator.hasNext()) {
            Row row = rowIterator.next();
            if (isRowEmpty(row)) {
                continue;
            }
            AchievementUploadDto dto = new AchievementUploadDto();
            dto.setOrderProductCode(ExcelUtil.getStringCellValue(row.getCell(0)));
            dto.setSaleType(ExcelUtil.getIntegerCellValue(row.getCell(1)));
            dto.setOrderNo(ExcelUtil.getStringCellValue(row.getCell(2)));
            dto.setCustomerType(ExcelUtil.getIntegerCellValue(row.getCell(3)));
            dto.setBusinessId(ExcelUtil.getStringCellValue(row.getCell(4)));
            dto.setMainSplitPerson(ExcelUtil.getIntegerCellValue(row.getCell(5)));
            dto.setStatisticsTime(ExcelUtil.getDateCellValue(row.getCell(6)));
            // 这部分数据是计算业绩的
            dto.setNetCash(ExcelUtil.getDoubleCellValue(row.getCell(7)));
            dto.setSaleHiredMoney(ExcelUtil.getDoubleCellValue(row.getCell(8)));
            dto.setRelaySaleHiredMoney(ExcelUtil.getDoubleCellValue(row.getCell(9)));
            dto.setDelaySaleHiredMoney(ExcelUtil.getDoubleCellValue(row.getCell(10)));
            dto.setDeptCommission(ExcelUtil.getDoubleCellValue(row.getCell(11)));
            dto.setDivCommission(ExcelUtil.getDoubleCellValue(row.getCell(12)));
            dto.setBranchCommission(ExcelUtil.getDoubleCellValue(row.getCell(13)));
            dto.setRemark(ExcelUtil.getStringCellValue(row.getCell(14)));

            boolean hasNull = Stream.of(dto.getOrderProductCode(), dto.getSaleType(), dto.getOrderNo(), dto.getBusinessId(),
                            dto.getMainSplitPerson(), dto.getStatisticsTime(), dto.getNetCash(), dto.getSaleHiredMoney(),
                            dto.getRelaySaleHiredMoney(), dto.getDelaySaleHiredMoney(), dto.getDeptCommission(),
                            dto.getDivCommission(), dto.getBranchCommission(), dto.getRemark())
                    .anyMatch(ObjectUtils::isEmpty);
            if (hasNull) {
                throw new IllegalArgumentException("存在必填字段为空，请检查文件!");
            }
            list.add(dto);
        }
        workbook.close();
        verifyMainSplitPerson(list);
        return list;
    }

    /**
     * 解析导入的规格业绩文档
     *
     * @param file 文件
     * @return {@link List }<{@link AchievementUploadDto }>
     * @throws Exception 异常
     */
    private List<AchievementUploadDto> parseSpecExcel(MultipartFile file) throws Exception {
        List<AchievementUploadDto> list = new ArrayList<>();
        Workbook workbook = WorkbookFactory.create(file.getInputStream());
        Sheet sheet = workbook.getSheetAt(0);
        Iterator<Row> rowIterator = sheet.iterator();
        // 跳过标题行
        rowIterator.next();

        while (rowIterator.hasNext()) {
            Row row = rowIterator.next();
            if (isRowEmpty(row)) {
                continue;
            }
            AchievementUploadDto dto = new AchievementUploadDto();
            dto.setOrderProductCode(ExcelUtil.getStringCellValue(row.getCell(0)));
            dto.setSpecId(ExcelUtil.getLongCellValue(row.getCell(1)));
            dto.setSpecCategoryId(ExcelUtil.getLongCellValue(row.getCell(2)));
            dto.setSaleType(ExcelUtil.getIntegerCellValue(row.getCell(3)));
            dto.setOrderNo(ExcelUtil.getStringCellValue(row.getCell(4)));
            dto.setCustomerType(ExcelUtil.getIntegerCellValue(row.getCell(5)));
            dto.setBusinessId(ExcelUtil.getStringCellValue(row.getCell(6)));
            dto.setMainSplitPerson(ExcelUtil.getIntegerCellValue(row.getCell(7)));
            dto.setStatisticsTime(ExcelUtil.getDateCellValue(row.getCell(8)));
            // 这部分数据是计算业绩的
            dto.setNetCash(ExcelUtil.getDoubleCellValue(row.getCell(9)));
            dto.setSaleHiredMoney(ExcelUtil.getDoubleCellValue(row.getCell(10)));
            dto.setRelaySaleHiredMoney(ExcelUtil.getDoubleCellValue(row.getCell(11)));
            dto.setDelaySaleHiredMoney(ExcelUtil.getDoubleCellValue(row.getCell(12)));
            dto.setDeptCommission(ExcelUtil.getDoubleCellValue(row.getCell(13)));
            dto.setDivCommission(ExcelUtil.getDoubleCellValue(row.getCell(14)));
            dto.setBranchCommission(ExcelUtil.getDoubleCellValue(row.getCell(15)));
            dto.setRemark(ExcelUtil.getStringCellValue(row.getCell(16)));

            boolean hasNull = Stream.of(dto.getOrderProductCode(), dto.getSpecId(), dto.getSpecCategoryId(), dto.getSaleType(),
                            dto.getOrderNo(), dto.getBusinessId(), dto.getMainSplitPerson(), dto.getStatisticsTime(),
                            dto.getNetCash(), dto.getSaleHiredMoney(), dto.getRelaySaleHiredMoney(), dto.getDelaySaleHiredMoney(),
                            dto.getDeptCommission(), dto.getDivCommission(), dto.getBranchCommission(), dto.getRemark())
                    .anyMatch(ObjectUtils::isEmpty);
            if (hasNull) {
                throw new IllegalArgumentException("存在必填字段为空，请检查文件!");
            }
            list.add(dto);
        }
        workbook.close();
        verifyMainSplitPerson(list);
        return list;
    }

    /**
     * 解析导入的规格退转款业绩文档
     *
     * @param file 文件
     * @return {@link List }<{@link AchievementUploadDto }>
     * @throws Exception 异常
     */
    private List<AchievementUploadDto> parseSpecRefundExcel(MultipartFile file) throws Exception {
        List<AchievementUploadDto> list = new ArrayList<>();
        Workbook workbook = WorkbookFactory.create(file.getInputStream());
        Sheet sheet = workbook.getSheetAt(0);
        Iterator<Row> rowIterator = sheet.iterator();
        // 跳过标题行
        rowIterator.next();

        while (rowIterator.hasNext()) {
            Row row = rowIterator.next();
            if (isRowEmpty(row)) {
                continue;
            }
            AchievementUploadDto dto = new AchievementUploadDto();
            dto.setOrderProductCode(ExcelUtil.getStringCellValue(row.getCell(0)));
            dto.setSpecCategoryId(ExcelUtil.getLongCellValue(row.getCell(1)));
            dto.setSpecId(ExcelUtil.getLongCellValue(row.getCell(2)));
            dto.setOrderNo(ExcelUtil.getStringCellValue(row.getCell(3)));
            dto.setCustomerType(ExcelUtil.getIntegerCellValue(row.getCell(4)));
            dto.setStatisticsTime(ExcelUtil.getDateCellValue(row.getCell(5)));
            dto.setMainSplitPerson(ExcelUtil.getIntegerCellValue(row.getCell(6)));
            dto.setBusinessId(ExcelUtil.getStringCellValue(row.getCell(7)));
            dto.setSaleType(ExcelUtil.getIntegerCellValue(row.getCell(8)));

            dto.setStandardPrice(ExcelUtil.getDoubleCellValue(row.getCell(9)));
            dto.setPayableAmount(ExcelUtil.getDoubleCellValue(row.getCell(10)));
            dto.setPaidAmount(ExcelUtil.getDoubleCellValue(row.getCell(11)));
            dto.setFirstYearRevenue(ExcelUtil.getDoubleCellValue(row.getCell(12)));
            dto.setFirstYearQuote(ExcelUtil.getDoubleCellValue(row.getCell(13)));
            dto.setRenewalRevenue(ExcelUtil.getDoubleCellValue(row.getCell(14)));
            dto.setRenewalQuote(ExcelUtil.getDoubleCellValue(row.getCell(15)));

            // 这部分数据是计算业绩的
            dto.setNetCash(ExcelUtil.getDoubleCellValue(row.getCell(16)));
            dto.setSaleHiredMoney(ExcelUtil.getDoubleCellValue(row.getCell(17)));
            dto.setRelaySaleHiredMoney(ExcelUtil.getDoubleCellValue(row.getCell(18)));
            dto.setDelaySaleHiredMoney(ExcelUtil.getDoubleCellValue(row.getCell(19)));
            dto.setDeptCommission(ExcelUtil.getDoubleCellValue(row.getCell(20)));
            dto.setDivCommission(ExcelUtil.getDoubleCellValue(row.getCell(21)));
            dto.setBranchCommission(ExcelUtil.getDoubleCellValue(row.getCell(22)));
            dto.setStatus(ExcelUtil.getIntegerCellValue(row.getCell(23)));
            dto.setRemark(ExcelUtil.getStringCellValue(row.getCell(24)));

            boolean hasNull = Stream.of(dto.getOrderProductCode(), dto.getSpecCategoryId(), dto.getSpecId(), dto.getOrderNo(),
                            dto.getStatisticsTime(), dto.getMainSplitPerson(), dto.getBusinessId(), dto.getSaleType(),
                            dto.getNetCash(), dto.getSaleHiredMoney(), dto.getRelaySaleHiredMoney(), dto.getDelaySaleHiredMoney(),
                            dto.getDeptCommission(), dto.getDivCommission(), dto.getBranchCommission(), dto.getStatus(), dto.getRemark())
                    .anyMatch(ObjectUtils::isEmpty);
            if (hasNull) {
                throw new IllegalArgumentException("存在必填字段为空，请检查文件!");
            }
            list.add(dto);
        }
        workbook.close();
        verifyMainSplitPerson(list);
        return list;
    }

    private boolean isRowEmpty(Row row) {
        if (row == null) {
            return true;
        }
        for (int c = row.getFirstCellNum(); c < row.getLastCellNum(); c++) {
            Cell cell = row.getCell(c);
            if (cell != null && cell.getCellType() != CellType.BLANK) {
                return false;
            }
        }
        return true;
    }

    private void verifyMainSplitPerson(List<AchievementUploadDto> dto) {
        // 先按 orderNo 分组
        Map<String, List<AchievementUploadDto>> groupedByOrderNo = dto.stream()
                .collect(Collectors.groupingBy(AchievementUploadDto::getOrderNo));

        for (Map.Entry<String, List<AchievementUploadDto>> entry : groupedByOrderNo.entrySet()) {
            String orderNo = entry.getKey();
            List<AchievementUploadDto> productList = entry.getValue();

            // 统计 businessId -> type 映射
            Map<String, Set<Integer>> businessIdToTypes = new HashMap<>();

            for (AchievementUploadDto product : productList) {
                businessIdToTypes.computeIfAbsent(product.getBusinessId(), k -> new HashSet<>()).add(product.getMainSplitPerson());
            }

            // 校验：同一个 businessId 只能有一个 type
            boolean invalidBusinessId = businessIdToTypes.values().stream().anyMatch(types -> types.size() > 1);
            if (invalidBusinessId) {
                throw new IllegalArgumentException("❌ 订单编号=" + orderNo + " 的 商务代表ID 里存在同时有主辅分担人 1 和 2 的情况，数据异常");
            }

            // 获取唯一的 businessId -> type 映射
            Map<String, Integer> businessIdToType = productList.stream()
                    .collect(Collectors.toMap(AchievementUploadDto::getBusinessId, AchievementUploadDto::getMainSplitPerson, (a, b) -> a));

            // 业务校验逻辑
            if (businessIdToType.size() > 2) {
                throw new IllegalArgumentException("❌ 订单编号=" + orderNo + " 的 商务代表ID 超过 2 个，数据异常");
            }

            if (businessIdToType.size() == 2) {
                List<Integer> types = new ArrayList<>(businessIdToType.values());
                if (!(types.contains(1) && types.contains(2))) {
                    throw new IllegalArgumentException("❌ 订单编号=" + orderNo + " 的 商务代表ID 对应的 主辅分单人 不是 1 和 2，数据异常");
                }
            }
        }
    }

    /**
     * 处理上传的数据
     *
     * @param uploadList     上传列表
     * @param needHandleSpec 是否需要处理规格数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleUploadFlowing(List<AchievementUploadDto> uploadList, boolean needHandleSpec) {
        List<AchievementProductDetailModel> productList = new ArrayList<>();
        List<AchievementCategoryDetailModel> categoryList = new ArrayList<>();
        List<AchievementSpecDetailModel> specList = new ArrayList<>();
        // 按照订单编号进行分组，查询出订单相关的信息
        Map<String, List<AchievementUploadDto>> groupedByOrderNo = uploadList.stream()
                .collect(Collectors.groupingBy(AchievementUploadDto::getOrderNo));
        Map<String, BusinessMonthModel> monthCache = new HashMap<>();
        for (Map.Entry<String, List<AchievementUploadDto>> orderNoEntry : groupedByOrderNo.entrySet()) {
            String orderNo = orderNoEntry.getKey();
            // 先根据订单编号查出订单的信息
            OrderSimpleInfoResponse orderInfo = innerService.getOrderSimpleInfoByOrderNo(orderNo);
            if (null == orderInfo) {
                throw new BusinessException("订单编号: " + orderNo + "有误，请核实！");
            }

            // 订单中的商品相关信息
            List<Long> specIds = orderInfo.getSpecResponseList().stream()
                    .map(OrderSimpleProductSpecResponse::getProductSpecId).collect(Collectors.toList());
            List<ProductListForAchievementResponse> productListForAchievement = innerService.getProductListForAchievement(specIds, null);
            Map<Long, ProductListForAchievementResponse> productIdProductMap = productListForAchievement.stream()
                    .collect(Collectors.toMap(ProductListForAchievementResponse::getProductId, Function.identity(), (k1, k2) -> k1));

            Map<String, OrderSimpleProductResponse> productMap = orderInfo.getProductResponseList().stream()
                    .collect(Collectors.toMap(OrderSimpleProductResponse::getOrderProductCode, Function.identity(), (k1, k2) -> k1));

            // 根据订单查询合同相关信息
            Long orderId = orderInfo.getOrderId();
            QueryContractDetailResponse contractDetail = innerService.getContractDetail(orderId);
            String contractCode = Optional.ofNullable(contractDetail)
                    .map(QueryContractDetailResponse::getContractDto)
                    .map(ContractDto::getContractCode)
                    .orElseThrow(() -> new RuntimeException(String.format("订单id:%s ,contractDetail未找到合同信息", orderId)));

            // 根据订单查询客户相关信息
            String customerId = orderInfo.getCustomerId();
            QueryCustomerResponse customer = innerService.queryCustomerInfo(customerId);

            // 按照主辅分单人进行分组
            Map<String, List<AchievementUploadDto>> groupByBusinessId = orderNoEntry.getValue().stream()
                    .collect(Collectors.groupingBy(AchievementUploadDto::getBusinessId));
            for (Map.Entry<String, List<AchievementUploadDto>> businessIdEntry : groupByBusinessId.entrySet()) {
                String businessId = businessIdEntry.getKey();
                // 商务信息
                UserInfoDetailResp business = innerService.getUserInfoDetail(businessId);
                if (null == business) {
                    throw new BusinessException("商务id: " + businessId + "有误，请核实！");
                }

                // 按照订单明细编号进行分组，整合出商品信息，并整合规格信息
                Map<String, List<AchievementUploadDto>> groupByOrderProductCode = businessIdEntry.getValue().stream()
                        .collect(Collectors.groupingBy(AchievementUploadDto::getOrderProductCode));
                for (Map.Entry<String, List<AchievementUploadDto>> orderProductCodeEntry : groupByOrderProductCode.entrySet()) {
                    String orderProductCode = orderProductCodeEntry.getKey();
                    OrderSimpleProductResponse productResponse = productMap.get(orderProductCode);
                    if (null == productResponse) {
                        throw new BusinessException("订单编号: " + orderNo + "中订单明细编号: " + orderProductCode + "有误，请核实！");
                    }
                    ProductListForAchievementResponse productListForAchievementResponse = productIdProductMap.get(productResponse.getProductId());

                    List<AchievementUploadDto> excelValueList = orderProductCodeEntry.getValue();
                    AchievementUploadDto excelDto = excelValueList.get(0);

                    // 商务月
                    String monthStr = DateUtil.formatDateTime(excelDto.getStatisticsTime());
                    BusinessMonthModel monthInfo = monthCache.get(monthStr);
                    if (null == monthInfo) {
                        monthInfo = businessMonthService.getMonthInfo(excelDto.getStatisticsTime());
                        monthCache.put(monthStr, monthInfo);
                    }
                    if (null == monthInfo) {
                        throw new RuntimeException(String.format("根据业绩生成时间未找到商务月,业绩生成时间:%s", monthStr));
                    }

                    if (businessMonthService.isMonthFrozen(monthInfo)){
                        // 记录日志
                        freezeMonthErrorLogService.add(monthInfo.getMonthId(), excelDto);
                        throw new BusinessException(WebCodeMessageEnum.BUSINESS_MONTH_FROZEN_ERROR.getMsg());
                    }
                    
                    // 这部分数据来源于订单联动
                    AchievementProductDetailModel product = new AchievementProductDetailModel();
                    long achievementId = IdUtil.getSnowflakeNextId();
                    product.setAchievementId(achievementId);
                    product.setOrderId(orderInfo.getOrderId());
                    product.setOrderNo(orderInfo.getOrderNo());
                    product.setSignedTime(orderInfo.getCreateTime());
                    Date updateTime = orderInfo.getProductResponseList().get(0).getUpdateTime();
                    if(null == updateTime){
                        updateTime = new Date();
                    }
                    product.setPaymentTime(updateTime);
                    product.setCustomerName(orderInfo.getCustomerName());
                    product.setCustomerId(orderInfo.getCustomerId());
                    product.setOrderType(orderInfo.getOrderType());
                    product.setOrderProductId(orderProductCode);
                    product.setProductId(productResponse.getProductId());
                    product.setProductName(productResponse.getProductName());
                    product.setDeliveryMethod(productResponse.getDeliveryMethod());
                    product.setSiteFlag(productListForAchievementResponse.getSiteFlag());
                    product.setProductType(productListForAchievementResponse.getLevelThreeCategoryName());
                    product.setDataChangeType(DataChangeTypeEnum.ARTIFICIAL_NEW.getChangeType());
                    product.setOrderSource(orderInfo.getOrderSource());
                    product.setContractNo(contractCode);
                    if (saasTabService.checkIsSaas(product.getProductId())) {
                        product.setIsSaas(SaasEnum.YES.getCode());
                    } else {
                        product.setIsSaas(SaasEnum.NO.getCode());
                    }
                    // 下面这部分是从excel中导入的

                    if(AchStatus.REFUND.getType().equals(excelDto.getStatus())){
                        customerSaasService.refundHandler(orderInfo.getOrderId(),productResponse.getProductId(),orderProductCode);
                    }
                    
                    // 退转款专属
                    product.setStatus(ObjectUtil.defaultIfNull(excelDto.getStatus(), AchStatus.VALID.getType()));
                    BigDecimal firstYearQuote = excelValueList.stream()
                            .filter(t -> ObjectUtil.isNotEmpty(t.getFirstYearQuote()))
                            .map(t -> BigDecimal.valueOf(t.getFirstYearQuote()))
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    product.setFirstYearQuote(firstYearQuote);
                    BigDecimal firstYearRevenue = excelValueList.stream()
                            .filter(t -> ObjectUtil.isNotEmpty(t.getFirstYearRevenue()))
                            .map(t -> BigDecimal.valueOf(t.getFirstYearRevenue()))
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    product.setFirstYearRevenue(firstYearRevenue);
                    BigDecimal renewalQuote = excelValueList.stream()
                            .filter(t -> ObjectUtil.isNotEmpty(t.getRenewalQuote()))
                            .map(t -> BigDecimal.valueOf(t.getRenewalQuote()))
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    product.setRenewalQuote(renewalQuote);
                    BigDecimal renewalRevenue = excelValueList.stream()
                            .filter(t -> ObjectUtil.isNotEmpty(t.getRenewalRevenue()))
                            .map(t -> BigDecimal.valueOf(t.getRenewalRevenue()))
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    product.setRenewalRevenue(renewalRevenue);
                    BigDecimal standardPrice = excelValueList.stream()
                            .filter(t -> ObjectUtil.isNotEmpty(t.getStandardPrice()))
                            .map(t -> BigDecimal.valueOf(t.getStandardPrice()))
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    product.setStandardPrice(standardPrice);
                    BigDecimal payableAmount = excelValueList.stream()
                            .filter(t -> ObjectUtil.isNotEmpty(t.getPayableAmount()))
                            .map(t -> BigDecimal.valueOf(t.getPayableAmount()))
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    product.setPayableAmount(payableAmount);
                    BigDecimal paidAmount = excelValueList.stream()
                            .filter(t -> ObjectUtil.isNotEmpty(t.getPaidAmount()))
                            .map(t -> BigDecimal.valueOf(t.getPaidAmount()))
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    product.setPaidAmount(paidAmount);

                    product.setMainSplitPerson(excelDto.getMainSplitPerson());
                    product.setSaleType(excelDto.getSaleType());
                    product.setBusinessId(excelDto.getBusinessId());
                    BigDecimal netCash = excelValueList.stream()
                            .map(t -> BigDecimal.valueOf(t.getNetCash()))
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    product.setNetCash(netCash);
                    BigDecimal saleHiredMoney = excelValueList.stream()
                            .map(t -> BigDecimal.valueOf(t.getSaleHiredMoney()))
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    product.setAgentCommissionAchievement(saleHiredMoney);
                    BigDecimal relaySaleHiredMoney = excelValueList.stream()
                            .map(t -> BigDecimal.valueOf(t.getRelaySaleHiredMoney()))
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    product.setAgentActualCommission(relaySaleHiredMoney);
                    BigDecimal delaySaleHiredMoney = excelValueList.stream()
                            .map(t -> BigDecimal.valueOf(t.getDelaySaleHiredMoney()))
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    product.setAgentDeferredCommission(delaySaleHiredMoney);
                    BigDecimal deptCommission = excelValueList.stream()
                            .map(t -> BigDecimal.valueOf(t.getDeptCommission()))
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    product.setDeptCommission(deptCommission);
                    BigDecimal divCommission = excelValueList.stream()
                            .map(t -> BigDecimal.valueOf(t.getDivCommission()))
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    product.setDivCommission(divCommission);
                    BigDecimal branchCommission = excelValueList.stream()
                            .map(t -> BigDecimal.valueOf(t.getBranchCommission()))
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    product.setBranchCommission(branchCommission);
                    product.setStatisticsTime(excelDto.getStatisticsTime());
                    product.setLatestRemark(excelDto.getRemark());
                    product.setRemarkHistory(excelDto.getRemark());

                    if (ObjectUtil.isNull(excelDto.getCustomerType())) {
                        CustomerType customerTypeEnum = calculateCustomer(customerId, orderInfo);
                        if (null == customerTypeEnum) {
                            throw new RuntimeException(String.format("订单:%s计算新老客户出错", orderId));
                        }
                        Integer customerType = customerTypeEnum.getType();
                        product.setCustomerType(customerType);
                    } else {
                        product.setCustomerType(excelDto.getCustomerType());
                    }

                    // 这部分数据是根据客户联动
                    if (customer != null) {
                        product.setCustomerName(customer.getCustomerName());
                        product.setProvinceCode(customer.getProvinceCode());
                        product.setProvinceName(customer.getProvinceName());
                        product.setCityCode(customer.getCityCode());
                        product.setCityName(customer.getCityName());
                        product.setDistrictCode(customer.getDistrictCode());
                        product.setDistrictName(customer.getDistrictName());
                        product.setCustomerRegion(customer.getProvinceName() + customer.getCityName() + customer.getDistrictName());
                    }

                    // 商务信息
                    product.setBusinessRepresentative(business.getName());
                    OrgPathInfoDTO businessOrgInfo = business.getOrgPathInfoDTO();
                    product.setCompanyId(businessOrgInfo.getCompanyId());
                    product.setCompany(businessOrgInfo.getCompanyName());
                    product.setDivisionId(businessOrgInfo.getCareerId());
                    product.setDivision(businessOrgInfo.getCareerName());
                    product.setDeptId(businessOrgInfo.getDeptId());
                    product.setDepartment(businessOrgInfo.getDeptName());
                    product.setRegionId(businessOrgInfo.getAreaId());
                    product.setBusinessMonth(monthInfo.getMonth());
                    product.setBusinessMonthId(monthInfo.getMonthId());
                    productList.add(product);

                    if (needHandleSpec) {
                        // 整合规格分类和规格信息
                        generateCategoryAndSpec(product, excelValueList, categoryList, specList);
                    }
                }
            }
        }
        // 计算完成后批量入库
        achievementProductService.saveOrUpdateBatch(productList);
        if (needHandleSpec) {
            achievementCategoryService.saveOrUpdateBatch(categoryList);
            achievementSpecService.saveOrUpdateBatch(specList);
        }
        // 商务人员根据商品业绩统计
        //businessAchHandler.achStatistics(productList, ProcessType.PAYMENT.getCode(), BusinessAchievementUpdateTypeEnum.NORMAL.getUpdateType());
    }

    /**
     * 导入商品业绩，覆盖原数据，数据来源于和中企对比后的数据
     *
     * @param file 文件
     * <AUTHOR>
     * @since 1.0
     */
    @Override
    public void importZqAchievementFromThird(MultipartFile file) throws Exception {
        List<AchievementThirdUploadDto> uploadList = parseThirdExcel(file);
        if (CollUtil.isEmpty(uploadList)) {
            return;
        }
        List<AchievementProductDetailModel> productAchievements = createProductAchievementForZq(uploadList);
        List<MqOrderPaymentInfoModel> mqModels = createMqOrderPaymentInfo(uploadList);
        List<ThirdAchievementModel> thirdAchievements = createThirdAchievement(uploadList);
        List<String> thirdIds = thirdAchievements.stream().map(ThirdAchievementModel::getThirdId).collect(Collectors.toList());

        Date startTime = DateUtil.parseDateTime("2025-01-01 00:00:00");
        Date endTime = DateUtil.parseDateTime("2025-05-31 23:59:59");

        transactionTemplate.executeWithoutResult(transactionStatus -> {
            achievementProductDetailRepository.lambdaUpdate()
                    .set(AchievementProductDetailModel::getDeleteFlag, DeleteFlagEnum.DELETE.getCode())
                    .set(AchievementProductDetailModel::getLatestRemark, "刷数据，重新导入")
                    .eq(AchievementProductDetailModel::getAchievementSource, AchievementSourceEnum.ZHONGXIAO.getCode())
                    .between(AchievementProductDetailModel::getStatisticsTime, startTime, endTime)
                    .update();

            achievementProductDetailRepository.saveBatch(productAchievements, 1000);

            mqOrderPaymentInfoRepository.lambdaUpdate()
                    .set(MqOrderPaymentInfoModel::getDeleteFlag, DeleteFlagEnum.DELETE.getCode())
                    .set(MqOrderPaymentInfoModel::getFailReason, "刷数据，重新导入")
                    .eq(MqOrderPaymentInfoModel::getAchievementSource, AchievementSourceEnum.ZHONGXIAO.getCode())
                    .between(MqOrderPaymentInfoModel::getCreateTime, startTime, endTime)
                    .update();

            mqOrderPaymentInfoRepository.saveBatch(mqModels, 1000);

            thirdAchievementRepository.lambdaUpdate()
                    .set(ThirdAchievementModel::getDeleteFlag, DeleteFlagEnum.DELETE.getCode())
                    .set(ThirdAchievementModel::getFailReason, "刷数据，重新导入")
                    .in(ThirdAchievementModel::getThirdId, thirdIds)
                    .update();

            thirdAchievementRepository.saveBatch(thirdAchievements, 1000);
        });
    }

    /**
     * 导入中企商品业绩，覆盖原数据，数据来源于和中企对比后的数据
     *
     * @param file 文件
     * <AUTHOR>
     * @since 1.0
     */
    @Override
    public void importKjAchievementFromThird(MultipartFile file) throws Exception {
        List<AchievementThirdUploadDto> uploadList = parseThirdExcel(file);
        if (CollUtil.isEmpty(uploadList)) {
            return;
        }
        Map<String, List<AchievementProductDetailModel>> productAchievementMap = createProductAchievementForKj(uploadList);
        List<AchievementProductDetailModel> insertModels = productAchievementMap.get(TaskTypeEnum.ADD.getMsg());
        List<AchievementProductDetailModel> updateModels = productAchievementMap.get(TaskTypeEnum.UPDATE.getMsg());

        transactionTemplate.executeWithoutResult(transactionStatus -> {
            achievementProductDetailRepository.saveBatch(insertModels, 1000);

            for (AchievementProductDetailModel updateModel : updateModels) {
                LambdaUpdateWrapper<AchievementProductDetailModel> wrapper = Wrappers.lambdaUpdate(AchievementProductDetailModel.class)
                        .eq(AchievementProductDetailModel::getAchievementId, updateModel.getAchievementId());
                achievementProductDetailRepository.update(updateModel, wrapper);
            }
        });
    }

    private void generateCategoryAndSpec(AchievementProductDetailModel product, List<AchievementUploadDto> excelValueList,
            List<AchievementCategoryDetailModel> categoryDetailModels, List<AchievementSpecDetailModel> specDetailModels) {
        // 按照规格分类进行分组，整合出规格分类信息
        Map<Long, List<AchievementUploadDto>> groupByCategory = excelValueList.stream()
                .collect(Collectors.groupingBy(AchievementUploadDto::getSpecCategoryId));
        for (Map.Entry<Long, List<AchievementUploadDto>> entry : groupByCategory.entrySet()) {
            Long specCategoryId = entry.getKey();
            List<AchievementUploadDto> specUploadList = entry.getValue();
            AchievementCategoryDetailModel categoryDetailModel = new AchievementCategoryDetailModel();
            List<PolicySpecDetailModel> categoryPolicy = policySpecDetailRepository.getPolicySpecDetailByCategoryId(specCategoryId);
            if (CollUtil.isEmpty(categoryPolicy)) {
                throw new BusinessException("规格分类id: " + specCategoryId + "有误，请核实！");
            }
            Long achievementCategoryId = IdUtil.getSnowflakeNextId();
            categoryDetailModel.setAchievementCategoryId(achievementCategoryId);
            categoryDetailModel.setAchievementId(product.getAchievementId());
            categoryDetailModel.setCategoryId(specCategoryId);
            categoryDetailModel.setCategoryName(categoryPolicy.get(0).getSpecCategoryName());
            categoryDetailModel.setStatus(product.getStatus());
            // 退转款专属，加了非空判断
            BigDecimal firstYearQuote = specUploadList.stream()
                    .filter(t -> ObjectUtil.isNotEmpty(t.getFirstYearQuote()))
                    .map(t -> BigDecimal.valueOf(t.getFirstYearQuote()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            categoryDetailModel.setFirstYearQuote(firstYearQuote);
            BigDecimal firstYearRevenue = specUploadList.stream()
                    .filter(t -> ObjectUtil.isNotEmpty(t.getFirstYearRevenue()))
                    .map(t -> BigDecimal.valueOf(t.getFirstYearRevenue()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            categoryDetailModel.setFirstYearIncome(firstYearRevenue);
            BigDecimal renewalQuote = specUploadList.stream()
                    .filter(t -> ObjectUtil.isNotEmpty(t.getRenewalQuote()))
                    .map(t -> BigDecimal.valueOf(t.getRenewalQuote()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            categoryDetailModel.setRenewalQuote(renewalQuote);
            BigDecimal renewalRevenue = specUploadList.stream()
                    .filter(t -> ObjectUtil.isNotEmpty(t.getRenewalRevenue()))
                    .map(t -> BigDecimal.valueOf(t.getRenewalRevenue()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            categoryDetailModel.setRenewalIncome(renewalRevenue);
            BigDecimal standardPrice = specUploadList.stream()
                    .filter(t -> ObjectUtil.isNotEmpty(t.getStandardPrice()))
                    .map(t -> BigDecimal.valueOf(t.getStandardPrice()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            categoryDetailModel.setStandardPrice(standardPrice);
            BigDecimal payableAmount = specUploadList.stream()
                    .filter(t -> ObjectUtil.isNotEmpty(t.getPayableAmount()))
                    .map(t -> BigDecimal.valueOf(t.getPayableAmount()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            categoryDetailModel.setPayableAmount(payableAmount);
            BigDecimal paidAmount = specUploadList.stream()
                    .filter(t -> ObjectUtil.isNotEmpty(t.getPaidAmount()))
                    .map(t -> BigDecimal.valueOf(t.getPaidAmount()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            categoryDetailModel.setPaidAmount(paidAmount);

            BigDecimal netCash = specUploadList.stream()
                    .map(t -> BigDecimal.valueOf(t.getNetCash()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            categoryDetailModel.setNetCash(netCash);
            BigDecimal saleHiredMoney = specUploadList.stream()
                    .map(t -> BigDecimal.valueOf(t.getSaleHiredMoney()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            categoryDetailModel.setAgentCommAchv(saleHiredMoney);
            BigDecimal relaySaleHiredMoney = specUploadList.stream()
                    .map(t -> BigDecimal.valueOf(t.getRelaySaleHiredMoney()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            categoryDetailModel.setAgentActCommAchv(relaySaleHiredMoney);
            BigDecimal delaySaleHiredMoney = specUploadList.stream()
                    .map(t -> BigDecimal.valueOf(t.getDelaySaleHiredMoney()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            categoryDetailModel.setAgentDefCommAchv(delaySaleHiredMoney);
            BigDecimal deptCommission = specUploadList.stream()
                    .map(t -> BigDecimal.valueOf(t.getDeptCommission()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            categoryDetailModel.setDeptCommAchv(deptCommission);
            BigDecimal divCommission = specUploadList.stream()
                    .map(t -> BigDecimal.valueOf(t.getDivCommission()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            categoryDetailModel.setBuCommAchv(divCommission);
            BigDecimal branchCommission = specUploadList.stream()
                    .map(t -> BigDecimal.valueOf(t.getBranchCommission()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            categoryDetailModel.setBranchCommAchv(branchCommission);
            categoryDetailModel.setOrderId(product.getOrderId());
            categoryDetailModel.setProductId(product.getProductId());
            categoryDetailModel.setMainSplitPerson(specUploadList.get(0).getMainSplitPerson());
            categoryDetailModel.setDataChangeType(DataChangeTypeEnum.ARTIFICIAL_NEW.getChangeType());
            categoryDetailModels.add(categoryDetailModel);

            // 最后是规格信息
            generateSpecDetail(product, categoryDetailModel, specUploadList, specDetailModels);
        }
    }

    private void generateSpecDetail(AchievementProductDetailModel product, AchievementCategoryDetailModel category,
            List<AchievementUploadDto> excelValueList, List<AchievementSpecDetailModel> specDetailModels) {
        Long specCategoryId = category.getCategoryId();
        Long achievementCategoryId = category.getAchievementCategoryId();
        for (AchievementUploadDto uploadDto : excelValueList) {
            Long specId = uploadDto.getSpecId();
            if (!checkSpecRelation(specId, specCategoryId)) {
                throw new BusinessException("规格分类:" + specCategoryId + "和规格:" + specId + "关联关系不存在");
            }
            AchievementSpecDetailModel specDetailModel = new AchievementSpecDetailModel();
            PolicySpecDetailModel specPolicy = policySpecDetailRepository.getPolicySpecDetailBySpecId(specId);
            if (null == specPolicy) {
                throw new BusinessException("规格id: " + specId + "有误，请核实！");
            }
            specDetailModel.setAchievementSpecId(IdUtil.getSnowflakeNextId());
            specDetailModel.setAchievementCategoryId(achievementCategoryId);
            specDetailModel.setSpecId(specId);
            specDetailModel.setSpecName(specPolicy.getSpecName());
            specDetailModel.setItemType(ItemTypeEnum.NUMBER.getType());
            specDetailModel.setOrderSpecType(OrderSpecTypeEnum.NORMAL.getType());
            specDetailModel.setBillingPrice(BigDecimal.ZERO);
            specDetailModel.setDataChangeType(DataChangeTypeEnum.ARTIFICIAL_NEW.getChangeType());
            if (saasTabService.checkIsSaas(product.getProductId())) {
                specDetailModel.setIsSaas(SaasEnum.YES.getCode());
            } else {
                specDetailModel.setIsSaas(SaasEnum.NO.getCode());
            }
            // 退转款专属，加了非空判断
            specDetailModel.setFirstYearQuote(Optional.ofNullable(uploadDto.getFirstYearQuote()).map(BigDecimal::valueOf).orElse(BigDecimal.ZERO));
            specDetailModel.setFirstYearIncome(Optional.ofNullable(uploadDto.getFirstYearRevenue()).map(BigDecimal::valueOf).orElse(BigDecimal.ZERO));
            specDetailModel.setRenewalQuote(Optional.ofNullable(uploadDto.getRenewalQuote()).map(BigDecimal::valueOf).orElse(BigDecimal.ZERO));
            specDetailModel.setRenewalIncome(Optional.ofNullable(uploadDto.getRenewalRevenue()).map(BigDecimal::valueOf).orElse(BigDecimal.ZERO));
            specDetailModel.setStandardPrice(Optional.ofNullable(uploadDto.getStandardPrice()).map(BigDecimal::valueOf).orElse(BigDecimal.ZERO));
            specDetailModel.setPayableAmount(Optional.ofNullable(uploadDto.getPayableAmount()).map(BigDecimal::valueOf).orElse(BigDecimal.ZERO));
            specDetailModel.setPaidAmount(Optional.ofNullable(uploadDto.getPaidAmount()).map(BigDecimal::valueOf).orElse(BigDecimal.ZERO));

            specDetailModel.setNetCash(BigDecimal.valueOf(uploadDto.getNetCash()));
            specDetailModel.setAgentCommAchv(BigDecimal.valueOf(uploadDto.getSaleHiredMoney()));
            specDetailModel.setAgentActCommAchv(BigDecimal.valueOf(uploadDto.getRelaySaleHiredMoney()));
            specDetailModel.setAgentDefCommAchv(BigDecimal.valueOf(uploadDto.getDelaySaleHiredMoney()));
            specDetailModel.setDeptCommAchv(BigDecimal.valueOf(uploadDto.getDeptCommission()));
            specDetailModel.setBuCommAchv(BigDecimal.valueOf(uploadDto.getDivCommission()));
            specDetailModel.setBranchCommAchv(BigDecimal.valueOf(uploadDto.getBranchCommission()));
            specDetailModel.setOrderId(product.getOrderId());
            specDetailModel.setProductId(product.getProductId());
            specDetailModel.setProductCategoryId(specCategoryId);
            specDetailModel.setMainSplitPerson(product.getMainSplitPerson());
            specDetailModel.setOrderProductId(uploadDto.getOrderProductCode());
            specDetailModels.add(specDetailModel);
        }
    }

    private CustomerType calculateCustomer(String customerId, OrderSimpleInfoResponse orderInfo) {
        List<AchievementProductDetailModel> newProductList = new ArrayList<>();

        // 商品ID 映射 商品信息
        List<Long> specIds = orderInfo.getSpecResponseList().stream()
                .map(OrderSimpleProductSpecResponse::getProductSpecId).collect(Collectors.toList());


        List<ProductListForAchievementResponse> productListForAchievement = innerService.getProductListForAchievement(specIds, null);


        Map<Long, ProductListForAchievementResponse> productIdProductMap = productListForAchievement.stream()
                .collect(Collectors.toMap(ProductListForAchievementResponse::getProductId,
                        product -> product, (existing, replacement) -> existing));

        // 商品规格ID 映射 商品信息
        Map<Long, ProductListForAchievementResponse> sepcIdProductMap = productListForAchievement.stream()
                .collect(Collectors.toMap(ProductListForAchievementResponse::getSpecId, Function.identity()));

        // 订单规格ID 映射 规格信息
        Map<Long, OrderSimpleProductSpecItemResponse> orderProductSpecIdItemMap = orderInfo.getSpecItemResponseList().stream().filter(spec -> NumberConstants.INTEGER_VALUE_1.equals(spec.getBillingItemRule()))
                .collect(Collectors.toMap(OrderSimpleProductSpecItemResponse::getOrderProductSpecId, Function.identity()));
//        Map<Long, List<OrderSimpleProductSpecItemResponse>> byOrderProductSpecIdMap = orderInfo.getSpecItemResponseList().stream().collect(Collectors.groupingBy(OrderSimpleProductSpecItemResponse::getOrderProductSpecId));
//        for (Map.Entry<Long, List<OrderSimpleProductSpecItemResponse>> orderProductSpecIdMap : byOrderProductSpecIdMap.entrySet()) {
//            List<OrderSimpleProductSpecItemResponse> mainValueList = orderProductSpecIdMap.getValue().stream().filter(spec -> NumberConstants.INTEGER_VALUE_1.equals(spec.getBillingItemRule())).collect(Collectors.toList());
//            if(CollectionUtils.isEmpty(mainValueList)){
//                orderProductSpecIdItemMap.put(orderProductSpecIdMap.getKey(),orderProductSpecIdMap.getValue().get(0));
//            }
//        }

        Map<Long, OrderSimpleProductSpecItemResponse> productSpecItemMap = setOrderProductSpecMap(orderInfo);

        // 商品规格ID 映射  新老客户统计数量
        List<Long> productIds = orderInfo.getSpecResponseList().stream().map(OrderSimpleProductSpecResponse::getProductId)
                .collect(Collectors.toList());
        Map<Long, Integer> specQuantityMap = innerService.getSpecStatistics(orderInfo.getCustomerId(), productIds).stream()
                .collect(Collectors.toMap(SpecStatisticsResponse::getProductSpecId, SpecStatisticsResponse::getQuantity));

        //广告通商品id
        List<String> advertisementProductList = Arrays.asList(productAchievementConfig.getAdvertisement().split(","));
        List<Long> newOpenSpecList = Lists.newArrayList();
        // 1.生成-商品规格业绩基础数据
        List<AchievementSpecDetailDto> specDtoList = orderInfo.getSpecResponseList().stream()
                .map(specResponse -> {
                    List<OrderSimpleProductResponse> orderProductIdList = orderInfo.getProductResponseList().stream()
                            .filter(o -> o.getOrderProductId().equals(specResponse.getOrderProductId()))
                            .collect(Collectors.toList());
                    specResponse.setOrderProductId(orderProductIdList.get(0).getOrderProductCode());
                    boolean flag = advertisementProductList.contains(specResponse.getProductId() + "");
                    return AchievementSpecDetailDto.buildBaseDto(specResponse, sepcIdProductMap, orderProductSpecIdItemMap, specQuantityMap, orderInfo ,productSpecItemMap,orderInfo.getOrderSaleType(), flag, newOpenSpecList);
                }).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(specDtoList)) {
            return null;
        }


        // 商品分类ID 映射 规格List
        Map<Long, List<AchievementSpecDetailDto>> categoryIdSpecListMap = specDtoList.stream()
                .collect(Collectors.groupingBy(AchievementSpecDetailDto::getCategoryId));


        // 2.生成-商品分类业绩基础数据
        List<AchievementCategoryDetailDto> categoryDtoList = categoryIdSpecListMap.values().stream()
                .map(AchievementCategoryDetailDto::buildBaseDto).collect(Collectors.toList());


        // 商品ID 映射  商品分类List
        Map<Long, List<AchievementCategoryDetailDto>> productIdCategoryListMap = categoryDtoList.stream()
                .collect(Collectors.groupingBy(AchievementCategoryDetailDto::getProductId));


        // 3.生成-商品业绩基础信息
        List<AchievementProductDetailDto> productDtoList = orderInfo.getProductResponseList().stream()
                .map(product -> AchievementProductDetailDto.buildBaseDtoList(orderInfo, product,
                        productIdCategoryListMap.get(product.getProductId()), productIdProductMap.get(product.getProductId()), innerService, achievementProductDetailRepository, 0))
                .flatMap(List::stream).collect(Collectors.toList());

        // 商品业绩ID 商品分类ID 商品分类规格ID 进行组装
        for (AchievementProductDetailDto productDto : productDtoList) {
            AchievementProductDetailModel targetAchProductModel = new AchievementProductDetailModel();
            BeanUtils.copyProperties(productDto, targetAchProductModel);
            newProductList.add(targetAchProductModel);
        }
        return calculateCustomerContext.calculateCustomer(customerId, newProductList);
    }

    private boolean checkSpecRelation(Long specId, Long specCategoryId) {
        return policySpecDetailRepository.getPolicySpecDetail(specId, specCategoryId) != null;
    }

    private Map<Long, OrderSimpleProductSpecItemResponse> setOrderProductSpecMap(OrderSimpleInfoResponse orderInfo) {
        Map<Long, OrderSimpleProductSpecItemResponse> productSpecIdItemMap = new HashMap<>();
        if(!SaleTypeEnum.NEW_OPEN.getType().equals(orderInfo.getOrderSaleType())){

            for (OrderSimpleProductResponse orderSimpleProductResponse : orderInfo.getProductResponseList()) {
                ServiceDelivery serviceDetail = innerService.getServiceDetail(orderSimpleProductResponse.getServeId(), orderInfo.getOrderSaleType());
                if(null == serviceDetail){
                    log.info("服务查询计费项数据为空 orderInfo：{}", orderInfo.getOrderNo());
                    continue;
                }
                for (Item item : serviceDetail.getItemList()) {
                    String productSpecId = item.getProductSpecId();

                    List<BillingItem> billList = item.getBillingItemList().stream().filter(bill -> bill.getBillingItemRule() == 1).collect(Collectors.toList());
                    BillingItem billingItem = billList.get(0);

                    OrderSimpleProductSpecItemResponse orderSpecItemResponse = new OrderSimpleProductSpecItemResponse();
                    orderSpecItemResponse.setItemNum(billingItem.getTotalQuantity());
                    orderSpecItemResponse.setItemType(billingItem.getItemType());
                    orderSpecItemResponse.setItemUnit(billingItem.getItemUnit());
                    productSpecIdItemMap.put(Long.valueOf(productSpecId),orderSpecItemResponse);
                }
            }
        }
        return productSpecIdItemMap;
    }


    private LambdaQueryWrapper<AchievementProductDetailModel> searchProductExcel(ExcelProductRequest search, UserLoginInfoDTO userInfo) {
        LambdaQueryWrapper<AchievementProductDetailModel> queryWrapper = new LambdaQueryWrapper<>();
        permitHandler(queryWrapper, userInfo);
        // 过滤中小数据
        queryWrapper
                .eq(!NumberConstants.INTEGER_VALUE_0.equals(search.getAchievementSource()), AchievementProductDetailModel::getAchievementSource, search.getAchievementSource())
                .eq(AchievementProductDetailModel::getDeleteFlag, YesOrNotEnum.NO.getCode())
                .eq(AchievementProductDetailModel::getDisplayed, NumberConstants.INTEGER_VALUE_0);
        if (ObjectUtils.isNotEmpty(search)) {
            String orderProductId = search.getOrderProductId();
            String orderNo = search.getOrderNo();
            String customerName = search.getCustomerName();
            String businessId = search.getBusinessId();
            String businessName = search.getBusinessName();
            String contractNo = search.getContractNo();
            String saleType = search.getSaleType();
            String orgId = search.getOrgId();

            Date startTime = search.getStartTime();
            if (ObjectUtil.isNotEmpty(startTime)) {
                startTime = DateUtils.getStartOfDay(startTime);
            }
            Date endTime = search.getEndTime();
            if (ObjectUtil.isNotEmpty(endTime)) {
                endTime = DateUtils.getEndOfDay(endTime);
            }

            queryWrapper.eq(StringUtils.isNotBlank(orderProductId), AchievementProductDetailModel::getOrderProductId, orderProductId);
            queryWrapper.eq(StringUtils.isNotBlank(saleType), AchievementProductDetailModel::getSaleType, saleType);
            queryWrapper.eq(StringUtils.isNotBlank(orderNo), AchievementProductDetailModel::getOrderNo, orderNo);
            queryWrapper.like(StringUtils.isNotBlank(customerName), AchievementProductDetailModel::getCustomerName, customerName);
            queryWrapper.eq(StringUtils.isNotBlank(businessId), AchievementProductDetailModel::getBusinessId, businessId);
            queryWrapper.like(StringUtils.isNotBlank(businessName), AchievementProductDetailModel::getBusinessRepresentative, businessName);
            queryWrapper.eq(StringUtils.isNotBlank(contractNo), AchievementProductDetailModel::getContractNo, contractNo);
            queryWrapper.between(ObjectUtils.isNotEmpty(startTime) && ObjectUtils.isNotEmpty(endTime), AchievementProductDetailModel::getStatisticsTime, startTime, endTime);
            queryWrapper.eq(ObjectUtils.isNotEmpty(search.getAchievementId()), AchievementProductDetailModel::getAchievementId, search.getAchievementId());
            queryWrapper.eq(Objects.nonNull(search.getIsAbnormal()), AchievementProductDetailModel::getIsAbnormal, search.getIsAbnormal());
            queryWrapper.and(StringUtils.isNotBlank(orgId),
                    wrapper -> wrapper
                            .eq(AchievementProductDetailModel::getCompanyId, orgId)
                            .or()
                            .eq(AchievementProductDetailModel::getDeptId, orgId)
                            .or()
                            .eq(AchievementProductDetailModel::getDivisionId, orgId)
            );
            
            // 添加商务月范围查询条件
            if (StringUtils.isNotBlank(search.getStartMonth())) {
                queryWrapper.ge(AchievementProductDetailModel::getBusinessMonth, search.getStartMonth());
            }
            if (StringUtils.isNotBlank(search.getEndMonth())) {
                queryWrapper.le(AchievementProductDetailModel::getBusinessMonth, search.getEndMonth());
            }
        }


        queryWrapper.orderByDesc(AchievementProductDetailModel::getAchievementId);
        return queryWrapper;
    }

    private void permitHandler(LambdaQueryWrapper<AchievementProductDetailModel> queryWrapper, UserLoginInfoDTO userInfo) {
        DataPermitEnum dataPermit = DataPermitEnum.getDataPermitEnumByCode(userInfo.getUserDataPermissionsDTO().getDataPermissionsType());
        switch (dataPermit) {
            case SELF:
                queryWrapper.eq(AchievementProductDetailModel::getBusinessId, userInfo.getUserId());
                break;
            case SELF_AND_COMPANY:
                queryWrapper.eq(AchievementProductDetailModel::getBusinessId, userInfo.getUserId());
                if (Objects.nonNull(userInfo.getOrgPathInfoDTO().getCompanyId())) {
                    queryWrapper.eq(AchievementProductDetailModel::getCompanyId, userInfo.getOrgPathInfoDTO().getCompanyId());
                }
                break;
            case DEPT:
                if (Objects.nonNull(userInfo.getOrgPathInfoDTO().getDeptId())) {
                    queryWrapper.eq(AchievementProductDetailModel::getDeptId, userInfo.getOrgPathInfoDTO().getDeptId());
                } else {
                    queryWrapper.eq(AchievementProductDetailModel::getBusinessId, userInfo.getUserId());
                }
                break;
            case CAREER:
                if (Objects.nonNull(userInfo.getOrgPathInfoDTO().getCareerId())) {
                    queryWrapper.eq(AchievementProductDetailModel::getDivisionId, userInfo.getOrgPathInfoDTO().getCareerId());
                } else {
                    queryWrapper.eq(AchievementProductDetailModel::getBusinessId, userInfo.getUserId());
                }
                break;
            case COMPANY:
                if (Objects.nonNull(userInfo.getOrgPathInfoDTO().getCompanyId())) {
                    queryWrapper.eq(AchievementProductDetailModel::getCompanyId, userInfo.getOrgPathInfoDTO().getCompanyId());
                } else {
                    queryWrapper.eq(AchievementProductDetailModel::getBusinessId, userInfo.getUserId());
                }
        }
    }

    private List<AchievementThirdUploadDto> parseThirdExcel(MultipartFile file) throws Exception {
        List<AchievementThirdUploadDto> list = new ArrayList<>();
        Workbook workbook = WorkbookFactory.create(file.getInputStream());
        // 读取第一个 sheet
        Sheet sheet = workbook.getSheetAt(0);
        Iterator<Row> rowIterator = sheet.iterator();
        // 跳过标题行
        rowIterator.next();

        while (rowIterator.hasNext()) {
            Row row = rowIterator.next();
            if (isRowEmpty(row)) {
                continue;
            }
            AchievementThirdUploadDto dto = new AchievementThirdUploadDto();

            dto.setThirdId(ExcelUtil.getStringCellValueOrNull(row.getCell(0)));
            dto.setBusinessMonth(ExcelUtil.getStringCellValueOrNull(row.getCell(1)));
            dto.setOrderDetailNo(ExcelUtil.getStringCellValueOrNull(row.getCell(2)));
            dto.setProductId(ExcelUtil.getLongCellValue(row.getCell(3)));
            dto.setProductName(ExcelUtil.getStringCellValueOrNull(row.getCell(4)));
            dto.setProductType(ExcelUtil.getStringCellValueOrNull(row.getCell(5)));
            dto.setBusinessType(ExcelUtil.getIntegerCellValue(row.getCell(6)));
            dto.setWebInfo(ExcelUtil.getIntegerCellValue(row.getCell(7)));
            dto.setState(ExcelUtil.getIntegerCellValue(row.getCell(8)));
            dto.setOrderRecordCode(ExcelUtil.getStringCellValueOrNull(row.getCell(9)));
            dto.setCustId(ExcelUtil.getStringCellValueOrNull(row.getCell(10)));
            dto.setCustName(ExcelUtil.getStringCellValueOrNull(row.getCell(11)));
            dto.setCustType(ExcelUtil.getIntegerCellValue(row.getCell(12)));
            dto.setCustCity(ExcelUtil.getStringCellValueOrNull(row.getCell(13)));
            dto.setContractCode(ExcelUtil.getStringCellValueOrNull(row.getCell(14)));
            dto.setSalerId(ExcelUtil.getStringCellValueOrNull(row.getCell(15)));
            dto.setSalerName(ExcelUtil.getStringCellValueOrNull(row.getCell(16)));
            dto.setShareType(ExcelUtil.getIntegerCellValue(row.getCell(17)));
            dto.setOrgId(ExcelUtil.getLongCellValue(row.getCell(18)));
            dto.setAreaId(ExcelUtil.getLongCellValue(row.getCell(19)));
            dto.setBuId(ExcelUtil.getLongCellValue(row.getCell(20)));
            dto.setDeptId(ExcelUtil.getLongCellValue(row.getCell(21)));
            dto.setSingingAmount(ExcelUtil.getBigDecimalFromCell(row.getCell(22)));
            dto.setActualAccount(ExcelUtil.getBigDecimalFromCell(row.getCell(23)));
            dto.setDiscountAccount(ExcelUtil.getBigDecimalFromCell(row.getCell(24)));
            dto.setSingingDate(ExcelUtil.getDateCellValue(row.getCell(25)));
            dto.setToAccountDate(ExcelUtil.getDateCellValue(row.getCell(26)));
            dto.setFirstStandardAccount(ExcelUtil.getBigDecimalFromCell(row.getCell(27)));
            dto.setFirstActualAccount(ExcelUtil.getBigDecimalFromCell(row.getCell(28)));
            dto.setRenewStandardAccount(ExcelUtil.getBigDecimalFromCell(row.getCell(29)));
            dto.setRenewActualAccount(ExcelUtil.getBigDecimalFromCell(row.getCell(30)));
            dto.setNetCashAccount(ExcelUtil.getBigDecimalFromCell(row.getCell(31)));
            dto.setSaleHiredMoney(ExcelUtil.getBigDecimalFromCell(row.getCell(32)));
            dto.setRelaySaleHiredMoney(ExcelUtil.getBigDecimalFromCell(row.getCell(33)));
            dto.setDelaySaleHiredMoney(ExcelUtil.getBigDecimalFromCell(row.getCell(34)));
            dto.setManagerHiredMoney(ExcelUtil.getBigDecimalFromCell(row.getCell(35)));
            dto.setCurrentPrice(ExcelUtil.getBigDecimalFromCell(row.getCell(36)));
            dto.setSubManagerHiredMoney(ExcelUtil.getBigDecimalFromCell(row.getCell(37)));
            dto.setDataState(ExcelUtil.getIntegerCellValue(row.getCell(38)));
            dto.setDbInsertTime(ExcelUtil.getDateCellValue(row.getCell(39)));
            dto.setCreater(ExcelUtil.getStringCellValueOrNull(row.getCell(40)));
            dto.setDbUpdateTime(ExcelUtil.getDateCellValue(row.getCell(41)));
            dto.setUpdater(ExcelUtil.getStringCellValueOrNull(row.getCell(42)));
            dto.setRemark(ExcelUtil.getStringCellValueOrNull(row.getCell(43)));
            // 跨境会多一列
            dto.setAchievementId(ExcelUtil.getLongCellValue(row.getCell(44)));

            list.add(dto);
        }
        workbook.close();
        return list;
    }

    private List<AchievementProductDetailModel> createProductAchievementForZq(List<AchievementThirdUploadDto> uploadList) {
        Map<String, Long> monthCache = new HashMap<>();
        Map<Long, Integer> productSaasCache = new HashMap<>();
        Map<String, String> orgCache = new HashMap<>();
        List<AchievementProductDetailModel> achievements = new ArrayList<>();
        for (AchievementThirdUploadDto uploadDto : uploadList) {
            AchievementProductDetailModel productAchievement = createInsertModelForZq(uploadDto, monthCache, productSaasCache, orgCache);
            achievements.add(productAchievement);
            // 设置orderId给mq用
            uploadDto.setOrderId(productAchievement.getOrderId());
        }
        return achievements;
    }

    private Map<String, List<AchievementProductDetailModel>> createProductAchievementForKj(List<AchievementThirdUploadDto> uploadList) {
        Map<Long, Integer> productSaasCache = new HashMap<>();
        Map<String, String> orgCache = new HashMap<>();

        List<Long> kjAchievementIds = uploadList.stream().map(AchievementThirdUploadDto::getAchievementId)
                .filter(ObjectUtil::isNotNull).collect(Collectors.toList());
        List<AchievementProductDetailModel> existingAchievements = achievementProductDetailRepository.lambdaQuery()
                .in(AchievementProductDetailModel::getAchievementId, kjAchievementIds)
                .eq(AchievementProductDetailModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                .list();
        Map<Long, AchievementProductDetailModel> existingByAchievementId = new HashMap<>();
        existingAchievements.forEach(t -> existingByAchievementId.put(t.getAchievementId(), t));
        Map<String, Map<Long, List<AchievementProductDetailModel>>> achievementsByContractNoAndProductId = existingAchievements.stream()
                .collect(Collectors.groupingBy(AchievementProductDetailModel::getContractNo,
                        Collectors.groupingBy(AchievementProductDetailModel::getProductId)));

        List<AchievementProductDetailModel> insertAchievements = new ArrayList<>();
        List<AchievementProductDetailModel> updateAchievements = new ArrayList<>();
        for (AchievementThirdUploadDto uploadDto : uploadList) {
            if (existingByAchievementId.containsKey(uploadDto.getAchievementId())) {
                AchievementProductDetailModel updateModelForKj = createUpdateModelForKj(uploadDto,
                        existingByAchievementId.get(uploadDto.getAchievementId()), productSaasCache, orgCache);
                updateAchievements.add(updateModelForKj);
                continue;
            }

            List<AchievementProductDetailModel> achievements = Optional.of(achievementsByContractNoAndProductId)
                    .map(map -> map.get(uploadDto.getContractCode()))
                    .map(map -> map.get(uploadDto.getProductId()))
                    .orElseGet(() -> {
                        return achievementProductDetailRepository.lambdaQuery()
                                .eq(AchievementProductDetailModel::getContractNo, uploadDto.getContractCode())
                                .eq(AchievementProductDetailModel::getProductId, uploadDto.getProductId())
                                .eq(AchievementProductDetailModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                                .list();
                    });
            AchievementProductDetailModel productAchievement = createInsertModelForKj(uploadDto, achievements,
                    productSaasCache, orgCache);
            insertAchievements.add(productAchievement);
        }
        Map<String, List<AchievementProductDetailModel>> result = new HashMap<>();
        result.put(TaskTypeEnum.ADD.getMsg(), insertAchievements);
        result.put(TaskTypeEnum.UPDATE.getMsg(), updateAchievements);
        return result;
    }

    private AchievementProductDetailModel createInsertModelForZq(AchievementThirdUploadDto uploadDto,
            Map<String, Long> monthCache, Map<Long, Integer> productSaasCache, Map<String, String> orgCache) {
        AchievementProductDetailModel productAchievement = new AchievementProductDetailModel();
        productAchievement.setAchievementId(IdUtil.getSnowflakeNextId());
        Long businessMonthId = monthCache.computeIfAbsent(uploadDto.getBusinessMonth(), businessMonthService::getMonthInfoByBusinessMonth);
        productAchievement.setBusinessMonthId(businessMonthId);
        productAchievement.setBusinessMonth(uploadDto.getBusinessMonth());
        productAchievement.setOrderProductId(uploadDto.getOrderDetailNo());
        productAchievement.setProductId(uploadDto.getProductId());
        productAchievement.setSiteFlag(uploadDto.getWebInfo());
        productAchievement.setProductName(uploadDto.getProductName());
        productAchievement.setProductType(uploadDto.getProductType());
        productAchievement.setSaleType(getSaleType(uploadDto.getBusinessType()));
        productAchievement.setStatus(getStatus(uploadDto.getState()));
        productAchievement.setOrderId(IdUtil.getSnowflake().nextId());
        productAchievement.setOrderNo(uploadDto.getOrderRecordCode());
        productAchievement.setOrderSource(OrderSourceEnum.UNKNOWN_ZERO.getType());
        productAchievement.setCustomerId(uploadDto.getCustId());
        productAchievement.setCustomerName(uploadDto.getCustName());
        productAchievement.setCustomerType(getCustomerType(uploadDto.getCustType()));
        productAchievement.setContractNo(uploadDto.getContractCode());
        productAchievement.setBusinessId(uploadDto.getSalerId());
        productAchievement.setBusinessRepresentative(uploadDto.getSalerName());
        if (NumberConstants.INTEGER_VALUE_0.equals(uploadDto.getShareType())) {
            productAchievement.setMainSplitPerson(MainSplitPersonEnum.ASSISTANT.getCode());
        } else if (NumberConstants.INTEGER_VALUE_1.equals(uploadDto.getShareType())) {
            productAchievement.setMainSplitPerson(MainSplitPersonEnum.MAIN.getCode());
        }
        productAchievement.setCompanyId(uploadDto.getOrgId());
        productAchievement.setRegionId(uploadDto.getAreaId());
        productAchievement.setDivisionId(uploadDto.getBuId());
        productAchievement.setDeptId(uploadDto.getDeptId());
        productAchievement.setCompany(innerService.queryOrgNameByIdWithCache(productAchievement.getCompanyId(), "公司", orgCache));
        productAchievement.setDepartment(innerService.queryOrgNameByIdWithCache(productAchievement.getDeptId(), "部门", orgCache));
        productAchievement.setDivision(innerService.queryOrgNameByIdWithCache(productAchievement.getDeptId(), "事业部", orgCache));
        productAchievement.setStandardPrice(uploadDto.getCurrentPrice());
        productAchievement.setPayableAmount(uploadDto.getSingingAmount());
        productAchievement.setPaidAmount(uploadDto.getActualAccount());
        productAchievement.setDiscountRate(uploadDto.getDiscountAccount());
        productAchievement.setDeliveryMethod(NumberConstants.INTEGER_VALUE_1);
        productAchievement.setOrderType(OrderTypeEnum.NORMAL.getType());
        productAchievement.setStatisticsTime(uploadDto.getToAccountDate());
        productAchievement.setSignedTime(uploadDto.getSingingDate());
        productAchievement.setPaymentTime(uploadDto.getToAccountDate());
        productAchievement.setFirstYearQuote(uploadDto.getFirstStandardAccount());
        productAchievement.setFirstYearRevenue(uploadDto.getFirstActualAccount());
        productAchievement.setRenewalQuote(uploadDto.getRenewStandardAccount());
        productAchievement.setRenewalRevenue(uploadDto.getRenewActualAccount());
        productAchievement.setNetCash(uploadDto.getNetCashAccount());
        productAchievement.setAgentCommissionAchievement(uploadDto.getSaleHiredMoney());
        productAchievement.setAgentActualCommission(uploadDto.getRelaySaleHiredMoney());
        productAchievement.setAgentDeferredCommission(uploadDto.getDelaySaleHiredMoney());
        productAchievement.setDeptCommission(uploadDto.getManagerHiredMoney());
        productAchievement.setDivCommission(uploadDto.getManagerHiredMoney());
        productAchievement.setBranchCommission(uploadDto.getSubManagerHiredMoney());
        productAchievement.setAchievementSource(AchievementSourceEnum.ZHONGXIAO.getCode());
        productAchievement.setThirdAchievementId(uploadDto.getThirdId());
        productAchievement.setDataChangeType(DataChangeTypeEnum.NORMAL.getChangeType());
        productAchievement.setLatestRemark(uploadDto.getRemark());
        Integer isSaas = productSaasCache.computeIfAbsent(uploadDto.getProductId(),
                k -> saasTabService.checkIsSaasZhongQi(uploadDto.getProductId()) ? SaasEnum.YES.getCode() : SaasEnum.NO.getCode());
        productAchievement.setIsSaas(isSaas);
        return productAchievement;
    }

    private AchievementProductDetailModel createInsertModelForKj(AchievementThirdUploadDto uploadDto,
            List<AchievementProductDetailModel> existingAchievements, Map<Long, Integer> productSaasCache, Map<String, String> orgCache) {
        Assert.notEmpty(existingAchievements, "合同编号-商品Id不存在于原业绩中，合同编号：" + uploadDto.getContractCode()
                + "，商品Id：" + uploadDto.getProductId());

        AchievementProductDetailModel productAchievement = new AchievementProductDetailModel();
        productAchievement.setAchievementId(ObjectUtil.defaultIfNull(uploadDto.getAchievementId(), IdUtil.getSnowflakeNextId()));
        productAchievement.setBusinessMonthId(existingAchievements.get(0).getBusinessMonthId());
        productAchievement.setBusinessMonth(uploadDto.getBusinessMonth());
        productAchievement.setOrderProductId(uploadDto.getOrderDetailNo());
        String serveNo = existingAchievements.stream().filter(ObjectUtil::isNotEmpty)
                .findAny().map(AchievementProductDetailModel::getServeNo).orElse(null);
        productAchievement.setServeNo(serveNo);
        productAchievement.setProductId(uploadDto.getProductId());
        productAchievement.setSiteFlag(uploadDto.getWebInfo());
        productAchievement.setProductName(uploadDto.getProductName());
        productAchievement.setProductType(existingAchievements.get(0).getProductType());
        productAchievement.setSaleType(getSaleType(uploadDto.getBusinessType()));
        productAchievement.setStatus(getStatus(uploadDto.getState()));
        productAchievement.setOrderId(existingAchievements.get(0).getOrderId());
        productAchievement.setOrderNo(uploadDto.getOrderRecordCode());
        productAchievement.setOrderSource(existingAchievements.get(0).getOrderSource());
        productAchievement.setCustomerId(uploadDto.getCustId());
        productAchievement.setCustomerName(uploadDto.getCustName());
        productAchievement.setCustomerType(getCustomerType(uploadDto.getCustType()));
        productAchievement.setProvinceCode(existingAchievements.get(0).getProvinceCode());
        productAchievement.setProvinceName(existingAchievements.get(0).getProvinceName());
        productAchievement.setCityCode(existingAchievements.get(0).getCityCode());
        productAchievement.setCityName(existingAchievements.get(0).getCityName());
        productAchievement.setDistrictCode(existingAchievements.get(0).getDistrictCode());
        productAchievement.setDistrictName(existingAchievements.get(0).getDistrictName());
        productAchievement.setCustomerRegion(existingAchievements.get(0).getCustomerRegion());
        productAchievement.setContractNo(uploadDto.getContractCode());
        productAchievement.setBusinessId(uploadDto.getSalerId());
        productAchievement.setBusinessRepresentative(uploadDto.getSalerName());
        if (NumberConstants.INTEGER_VALUE_0.equals(uploadDto.getShareType())) {
            productAchievement.setMainSplitPerson(MainSplitPersonEnum.ASSISTANT.getCode());
        } else if (NumberConstants.INTEGER_VALUE_1.equals(uploadDto.getShareType())) {
            productAchievement.setMainSplitPerson(MainSplitPersonEnum.MAIN.getCode());
        }
        productAchievement.setCompanyId(uploadDto.getOrgId());
        productAchievement.setRegionId(uploadDto.getAreaId());
        productAchievement.setDivisionId(uploadDto.getBuId());
        productAchievement.setDeptId(uploadDto.getDeptId());
        productAchievement.setCompany(innerService.queryOrgNameByIdWithCache(productAchievement.getCompanyId(), "公司", orgCache));
        productAchievement.setDepartment(innerService.queryOrgNameByIdWithCache(productAchievement.getDeptId(), "部门", orgCache));
        productAchievement.setDivision(innerService.queryOrgNameByIdWithCache(productAchievement.getDeptId(), "事业部", orgCache));
        productAchievement.setStandardPrice(uploadDto.getCurrentPrice());
        productAchievement.setPayableAmount(uploadDto.getSingingAmount());
        productAchievement.setPaidAmount(uploadDto.getActualAccount());
        productAchievement.setDiscountRate(uploadDto.getDiscountAccount());
        productAchievement.setDeliveryMethod(existingAchievements.get(0).getDeliveryMethod());
        productAchievement.setOrderType(existingAchievements.get(0).getOrderType());
        productAchievement.setStatisticsTime(uploadDto.getToAccountDate());
        productAchievement.setSignedTime(uploadDto.getSingingDate());
        productAchievement.setPaymentTime(uploadDto.getToAccountDate());
        productAchievement.setFirstYearQuote(uploadDto.getFirstStandardAccount());
        productAchievement.setFirstYearRevenue(uploadDto.getFirstActualAccount());
        productAchievement.setRenewalQuote(uploadDto.getRenewStandardAccount());
        productAchievement.setRenewalRevenue(uploadDto.getRenewActualAccount());
        productAchievement.setNetCash(uploadDto.getNetCashAccount());
        productAchievement.setAgentCommissionAchievement(uploadDto.getSaleHiredMoney());
        productAchievement.setAgentActualCommission(uploadDto.getRelaySaleHiredMoney());
        productAchievement.setAgentDeferredCommission(uploadDto.getDelaySaleHiredMoney());
        productAchievement.setDeptCommission(uploadDto.getManagerHiredMoney());
        productAchievement.setDivCommission(uploadDto.getManagerHiredMoney());
        productAchievement.setBranchCommission(uploadDto.getSubManagerHiredMoney());
        productAchievement.setServeFinishTime(existingAchievements.get(0).getServeFinishTime());
        productAchievement.setAchievementSource(AchievementSourceEnum.KUAJINFG.getCode());
        productAchievement.setDataChangeType(DataChangeTypeEnum.ARTIFICIAL_NEW.getChangeType());
        productAchievement.setLatestRemark(uploadDto.getRemark());
        Integer isSaas = productSaasCache.computeIfAbsent(uploadDto.getProductId(),
                k -> saasTabService.checkIsSaas(uploadDto.getProductId()) ? SaasEnum.YES.getCode() : SaasEnum.NO.getCode());
        productAchievement.setIsSaas(isSaas);
        return productAchievement;
    }

    private AchievementProductDetailModel createUpdateModelForKj(AchievementThirdUploadDto uploadDto,
            AchievementProductDetailModel existingAchievement, Map<Long, Integer> productSaasCache, Map<String, String> orgCache) {
        AchievementProductDetailModel productAchievement = new AchievementProductDetailModel();
        productAchievement.setId(existingAchievement.getId());
        productAchievement.setAchievementId(existingAchievement.getAchievementId());
        productAchievement.setCustomerType(getCustomerType(uploadDto.getCustType()));
        if (NumberConstants.INTEGER_VALUE_0.equals(uploadDto.getShareType())) {
            productAchievement.setMainSplitPerson(MainSplitPersonEnum.ASSISTANT.getCode());
        } else if (NumberConstants.INTEGER_VALUE_1.equals(uploadDto.getShareType())) {
            productAchievement.setMainSplitPerson(MainSplitPersonEnum.MAIN.getCode());
        }
        productAchievement.setBusinessId(uploadDto.getSalerId());
        productAchievement.setBusinessRepresentative(uploadDto.getSalerName());
        productAchievement.setCompanyId(uploadDto.getOrgId());
        productAchievement.setRegionId(uploadDto.getAreaId());
        productAchievement.setDivisionId(uploadDto.getBuId());
        productAchievement.setDeptId(uploadDto.getDeptId());
        productAchievement.setCompany(innerService.queryOrgNameByIdWithCache(productAchievement.getCompanyId(), "公司", orgCache));
        productAchievement.setDepartment(innerService.queryOrgNameByIdWithCache(productAchievement.getDeptId(), "部门", orgCache));
        productAchievement.setDivision(innerService.queryOrgNameByIdWithCache(productAchievement.getDeptId(), "事业部", orgCache));
        productAchievement.setNetCash(uploadDto.getNetCashAccount());
        productAchievement.setAgentCommissionAchievement(uploadDto.getSaleHiredMoney());
        productAchievement.setAgentActualCommission(uploadDto.getRelaySaleHiredMoney());
        productAchievement.setAgentDeferredCommission(uploadDto.getDelaySaleHiredMoney());
        productAchievement.setDeptCommission(uploadDto.getManagerHiredMoney());
        productAchievement.setDivCommission(uploadDto.getManagerHiredMoney());
        productAchievement.setBranchCommission(uploadDto.getSubManagerHiredMoney());
        Integer isSaas = productSaasCache.computeIfAbsent(uploadDto.getProductId(),
                k -> saasTabService.checkIsSaas(uploadDto.getProductId()) ? SaasEnum.YES.getCode() : SaasEnum.NO.getCode());
        productAchievement.setIsSaas(isSaas);
        String history = existingAchievement.getRemarkHistory();
        history = StrUtil.isBlank(history)
                ? existingAchievement.getLatestRemark()
                : StrUtil.format("{}|{}", existingAchievement.getLatestRemark(), history);
        productAchievement.setRemarkHistory(history);
        productAchievement.setLatestRemark(uploadDto.getRemark());
        productAchievement.setDataChangeType(DataChangeTypeEnum.ARTIFICIAL_UPDATE.getChangeType());
        return productAchievement;
    }

    private Integer getSaleType(Integer thirdBusinessType) {
        if (SaleTypeEnum.NEW_OPEN.getCode().equals(thirdBusinessType)) {
            // 1新开 -> 1新开
            return OrderSaleTypeEnum.OPEN.getType();
        }
        if (SaleTypeEnum.RENEW.getCode().equals(thirdBusinessType)) {
            // 2续费 -> 2续费
            return OrderSaleTypeEnum.RENEW.getType();
        }
        if (SaleTypeEnum.TRANSFER.getCode().equals(thirdBusinessType)) {
            // 20转款 -> 5转款
            return OrderSaleTypeEnum.TRANSFER.getType();
        }
        if (SaleTypeEnum.ADDITIONAL_PURCHASE.getCode().equals(thirdBusinessType)) {
            // 4另购充值 -> 4另购
            return OrderSaleTypeEnum.ANOTHER_BUY.getType();
        }
        if (SaleTypeEnum.DOMAIN_TRANSFER.getCode().equals(thirdBusinessType)) {
            // 3310域名转入 -> 1新开
            return OrderSaleTypeEnum.OPEN.getType();
        }
        if (SaleTypeEnum.UPGRADE.getCode().equals(thirdBusinessType)) {
            // 3扩容 -> 4另购
            return OrderSaleTypeEnum.ANOTHER_BUY.getType();
        }
        if (SaleTypeEnum.HIGH_PRICE_REDEMPTION.getCode().equals(thirdBusinessType)) {
            // 5高价赎回 -> 7高价赎回
            return OrderSaleTypeEnum.HIGH_PRICE_REDEMPTION.getType();
        }
        if (SaleTypeEnum.ZADD_UPGRADE.getCode().equals(thirdBusinessType) ||
                SaleTypeEnum.CAPACITY_EXPANSION_ZT.getCode().equals(thirdBusinessType) ||
                SaleTypeEnum.ZTSZT_UPGRADE.getCode().equals(thirdBusinessType) ||
                SaleTypeEnum.ZTSZM_UPGRADE.getCode().equals(thirdBusinessType) ||
                SaleTypeEnum.TRANSFER_IN.getCode().equals(thirdBusinessType) ||
                SaleTypeEnum.NZTSZT_UPGRADE.getCode().equals(thirdBusinessType) ||
                SaleTypeEnum.NZTSZM_UPGRADE.getCode().equals(thirdBusinessType) ||
                SaleTypeEnum.NZTSZM_UPGRADE_DA.getCode().equals(thirdBusinessType) ||
                SaleTypeEnum.DSP_UPGRADE.getCode().equals(thirdBusinessType) ||
                SaleTypeEnum.EIGHTY_EIGHT_UPGRADE.getCode().equals(thirdBusinessType) ||
                SaleTypeEnum.PORTAL_EXTENSION_UPGRADE.getCode().equals(thirdBusinessType)) {
            // 7升级(ZT升级ZT) 8zadd升级 3115Z+升级ZTSZT 3116Z+升级ZTSZM 6转入 3117NZ+升级ZTSZT  3118NZ+升级ZTSZM 169大把推续费升级 12DSP升级 88升级 188门户扩展升级 -> 3升级
            return OrderSaleTypeEnum.UPGRADE.getType();
        }
        if (SaleTypeEnum.REFUND.getCode().equals(thirdBusinessType)) {
            // 18退款 -> 6退款
            return OrderSaleTypeEnum.REFUND.getType();
        }
        return null;
    }

    private Integer getStatus(Integer thirdState) {
        if (NumberConstants.INTEGER_VALUE_0.equals(thirdState)) {
            return StatusEnum.VALID.getCode();
        }
        if (NumberConstants.INTEGER_VALUE_1.equals(thirdState)) {
            return StatusEnum.NOT_VALID.getCode();
        }
        if (NumberConstants.INTEGER_VALUE_2.equals(thirdState)) {
            return StatusEnum.REFUND.getCode();
        }
        if (NumberConstants.INTEGER_VALUE_3.equals(thirdState)) {
            return StatusEnum.COMPLETED.getCode();
        }
        return null;
    }

    private Integer getCustomerType(Integer thirdCustomerType) {
        if (NumberConstants.INTEGER_VALUE_1.equals(thirdCustomerType)) {
            return CustomerTypeEnum.OLD.getCode();
        }
        if (NumberConstants.INTEGER_VALUE_2.equals(thirdCustomerType)) {
            return CustomerTypeEnum.NEW.getCode();
        }
        if (NumberConstants.INTEGER_VALUE_3.equals(thirdCustomerType)) {
            return CustomerTypeEnum.NON_NEW_OLD.getCode();
        }
        return null;
    }

    private List<MqOrderPaymentInfoModel> createMqOrderPaymentInfo(List<AchievementThirdUploadDto> uploadList) {
        return uploadList.stream()
                .map(uploadDto -> {
                    MqOrderPaymentInfoModel mqOrderPaymentInfoModel = new MqOrderPaymentInfoModel();
                    mqOrderPaymentInfoModel.setTaskId(IdUtil.getSnowflake().nextId());
                    mqOrderPaymentInfoModel.setTaskType(TaskTypeEnum.ADD.getMsg());
                    mqOrderPaymentInfoModel.setTaskStatus(TaskStatusEnum.YES.getCode().toString());
                    mqOrderPaymentInfoModel.setOrderId(uploadDto.getOrderId());
                    mqOrderPaymentInfoModel.setAchievementSource(AchievementSourceEnum.ZHONGXIAO.getCode());
                    mqOrderPaymentInfoModel.setThirdAchievementId(uploadDto.getThirdId());
                    mqOrderPaymentInfoModel.setPaymentTime(uploadDto.getToAccountDate());
                    mqOrderPaymentInfoModel.setCustomerId(uploadDto.getCustId());
                    mqOrderPaymentInfoModel.setCalculateType(CalculateTypeEnum.PAYMENT.getCode());
                    mqOrderPaymentInfoModel.setCreateTime(uploadDto.getToAccountDate());
                    mqOrderPaymentInfoModel.setThirdSource(ThirdSourceEnum.EXCEL.getMsg());
                    return mqOrderPaymentInfoModel;
                }).collect(Collectors.toList());
    }

    private List<ThirdAchievementModel> createThirdAchievement(List<AchievementThirdUploadDto> uploadList) {
        return uploadList.stream()
                .map(uploadDto -> {
                    ThirdAchievementModel thirdAchievementModel = BeanUtil.copyProperties(uploadDto, ThirdAchievementModel.class);
                    thirdAchievementModel.setDataSource(NumberConstants.INTEGER_VALUE_1);
                    thirdAchievementModel.setTaskId(uploadDto.getOrderId());
                    thirdAchievementModel.setTaskType(TaskTypeEnum.ADD.getMsg());
                    thirdAchievementModel.setTaskStatus(TaskStatusEnum.YES.getCode());
                    thirdAchievementModel.setThirdSource(ThirdSourceEnum.EXCEL.getMsg());
                    return thirdAchievementModel;
                }).collect(Collectors.toList());
    }
}
