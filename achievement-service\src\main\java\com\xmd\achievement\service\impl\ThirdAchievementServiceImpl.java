package com.xmd.achievement.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xmd.achievement.dao.entity.MqOrderPaymentInfoModel;
import com.xmd.achievement.dao.entity.ThirdAchievementModel;
import com.xmd.achievement.dao.repository.IThirdAchievementRepository;
import com.xmd.achievement.rpc.InnerService;
import com.xmd.achievement.rpc.entity.dto.OrgInfoResponse;
import com.xmd.achievement.service.MqOrderPaymentInfoService;
import com.xmd.achievement.service.ThirdAchievementService;
import com.xmd.achievement.service.entity.request.ThirdPerformanceRequest;
import com.xmd.achievement.support.constant.NumberConstants;
import com.xmd.achievement.support.constant.enums.*;
import com.xmd.achievement.support.constant.exceptions.BusinessException;
import com.xmd.achievement.util.enums.CalculateTypeEnum;
import com.xmd.achievement.util.enums.DateTimeFormatStyleEnum;
import com.xmd.achievement.web.annotate.lock.annotation.Lock;
import com.xmd.achievement.web.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/01/15/10:19
 * @since 1.0
 */
@Service
@Slf4j
public class ThirdAchievementServiceImpl implements ThirdAchievementService {
    @Resource
    IThirdAchievementRepository mqThirdAchievementRepository;

    @Resource
    MqOrderPaymentInfoService mqOrderPaymentInfoService;

    @Autowired
    InnerService innerService;

    private static final String SUFFIX = "ZQKJ";

    @Override
    @Lock("'MqThirdAchievement'+#request.thirdId")
    @Transactional(rollbackFor = Exception.class)
    public Boolean flowing(ThirdPerformanceRequest request, ThirdSourceEnum thirdSourceEnum) {
        try {
            List<Integer> dataStates = new ArrayList<>();
            dataStates.add(NumberConstants.INTEGER_VALUE_0);
            dataStates.add(NumberConstants.INTEGER_VALUE_3);

            if (!dataStates.contains(request.getDataState())) {
                log.warn("中小数据流水录入,状态不为正常/删除状态,thirdId:{},dataState:{}", request.getThirdId(), request.getDataState());
                return true;
            }

            ThirdAchievementModel model = new ThirdAchievementModel();
            BeanUtil.copyProperties(request, model);
            // 中企传过来的数据，currentPrice 代表标准价 没有事业部提成业绩
            if (ObjectUtil.isEmpty(request.getCurrentPrice())) {
                model.setStandardPrice(BigDecimal.ZERO);
            } else {
                model.setStandardPrice(BigDecimal.valueOf(request.getCurrentPrice()));
            }
            model.setCurrentPrice(model.getManagerHiredMoney());

            // 实时查询商务的组织信息 中企过来的数据只有id 没有name 需要实时去查
            OrgInfoResponse orgInfo = innerService.queryOrgInfoById(Convert.toLong(model.getOrgId()));
            if (ObjectUtil.isNotEmpty(orgInfo)) {
                model.setCompany(orgInfo.getName());
            } else {
                log.warn("查询公司组织信息为空，公司id：{}", model.getOrgId());
            }
            OrgInfoResponse deptInfo = innerService.queryOrgInfoById(Convert.toLong(model.getDeptId()));
            if (ObjectUtil.isNotEmpty(deptInfo)) {
                model.setDepartment(deptInfo.getName());
            } else {
                log.warn("查询部门信息为空，部门id：{}", model.getDeptId());
            }

            model.setOrderId(String.valueOf(IdUtil.getSnowflake().nextId()));
            model.setTaskId(IdUtil.getSnowflake().nextId());
            model.setCreateName(request.getCreateUserName());
            model.setUpdateName(request.getUpdateUserName());
            model.setThirdSource(thirdSourceEnum.getMsg());

            model.setSingingDate(DateUtils.stringToDate(request.getSingingDate(), DateTimeFormatStyleEnum.yyyy_MM_dd_HH_mm_ss));
            model.setToAccountDate(DateUtils.stringToDate(request.getToAccountDate(), DateTimeFormatStyleEnum.yyyy_MM_dd_HH_mm_ss));
            model.setServeFinishTime(DateUtils.stringToDate(request.getServeFinishTime(), DateTimeFormatStyleEnum.yyyy_MM_dd_HH_mm_ss));
            model.setDbInsertTime(DateUtils.stringToDate(request.getDbInsertTime(), DateTimeFormatStyleEnum.yyyy_MM_dd_HH_mm_ss));
            model.setDbUpdateTime(DateUtils.stringToDate(request.getDbUpdateTime(), DateTimeFormatStyleEnum.yyyy_MM_dd_HH_mm_ss));

            List<ThirdAchievementModel> models = mqThirdAchievementRepository.lambdaQuery()
                    .eq(ThirdAchievementModel::getThirdId, request.getThirdId())
                    .eq(ThirdAchievementModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode()).list();
            // 新增
            if (ObjectUtil.isEmpty(models)) {
                if (TaskTypeEnum.DELETE.getCode().equals(request.getDataState())) {
                    log.info("中小数据流水录入删除数据，但库中没有原数据，不处理，请求参数{}", request);
                    return true;
                }
                model.setTaskType(TaskTypeEnum.ADD.getMsg());
            } else {
                // 修改或删除 将元数据进行删除标识 并将未执行完成的任务修改已完成 保存新数据
                mqThirdAchievementRepository.lambdaUpdate()
                        .eq(ThirdAchievementModel::getThirdId, models.get(0).getThirdId())
                        .set(ThirdAchievementModel::getTaskStatus, TaskStatusEnum.YES.getCode())
                        .set(ThirdAchievementModel::getDeleteFlag, DeleteFlagEnum.DELETE.getCode())
                        .update();
                if (TaskTypeEnum.DELETE.getCode().equals(request.getDataState())) {
                    model.setTaskType(TaskTypeEnum.DELETE.getMsg());
                } else {
                    model.setTaskType(TaskTypeEnum.UPDATE.getMsg());
                }
            }

            // 保存任务表
            MqOrderPaymentInfoModel mqOrderPaymentInfoModel = new MqOrderPaymentInfoModel();
            mqOrderPaymentInfoModel.setTaskId(IdUtil.getSnowflake().nextId());
            mqOrderPaymentInfoModel.setTaskType(model.getTaskType());
            mqOrderPaymentInfoModel.setOrderId(model.getTaskId());
            mqOrderPaymentInfoModel.setAchievementSource(AchievementSourceEnum.ZHONGXIAO.getCode());
            mqOrderPaymentInfoModel.setThirdAchievementId(model.getThirdId());
            mqOrderPaymentInfoModel.setPaymentTime(model.getToAccountDate());
            mqOrderPaymentInfoModel.setCustomerId(model.getCustId());
            mqOrderPaymentInfoModel.setCalculateType(CalculateTypeEnum.PAYMENT.getCode());
            mqOrderPaymentInfoModel.setCreateTime(model.getToAccountDate());
            mqOrderPaymentInfoModel.setDisplayed(ObjectUtil.defaultIfNull(model.getDisplayed(), NumberConstants.INTEGER_VALUE_0));
            mqOrderPaymentInfoModel.setThirdSource(thirdSourceEnum.getMsg());
            mqOrderPaymentInfoService.saveModel(mqOrderPaymentInfoModel);

            model.setTaskStatus(TaskStatusEnum.YES.getCode());
            mqThirdAchievementRepository.save(model);
            return true;
        } catch (Exception ex) {
            log.error("中小数据流水录入失败,request:{}，异常信息:", JSONUtil.toJsonStr(request), ex);
            throw new BusinessException("中小数据流水录入失败,thirdId:" + request.getThirdId());
        }
    }

    private void handleSuffixes(ThirdAchievementModel model) {
        // orderDetailNo
        String orderDetailNo = model.getOrderDetailNo();
        if (ObjectUtil.isNotEmpty(orderDetailNo) && orderDetailNo.endsWith(SUFFIX)) {
            model.setOrderDetailNo(orderDetailNo.substring(0, orderDetailNo.length() - SUFFIX.length()));
        }
        // orderRecordCode
        String orderRecordCode = model.getOrderRecordCode();
        if (ObjectUtil.isNotEmpty(orderRecordCode) && orderRecordCode.endsWith(SUFFIX)) {
            model.setOrderRecordCode(orderRecordCode.substring(0, orderRecordCode.length() - SUFFIX.length()));
        }
        // custId
        String custId = model.getCustId();
        if (ObjectUtil.isNotEmpty(custId) && custId.endsWith(SUFFIX)) {
            model.setCustId(custId.substring(0, custId.length() - SUFFIX.length()));
        }
        // contractCode
        String contractCode = model.getContractCode();
        if (ObjectUtil.isNotEmpty(contractCode) && contractCode.endsWith(SUFFIX)) {
            model.setContractCode(contractCode.substring(0, contractCode.length() - SUFFIX.length()));
        }
    }

    @Override
    public List<ThirdAchievementModel> queryExecuteTask() {
        return mqThirdAchievementRepository.list(
                new LambdaQueryWrapper<ThirdAchievementModel>()
                        .eq(ThirdAchievementModel::getTaskStatus, TaskStatusEnum.NO.getCode())
                        .eq(ThirdAchievementModel::getDeleteFlag, YesOrNotEnum.NO.getCode())
                        .eq(ThirdAchievementModel::getTaskType, TaskTypeEnum.ADD.getMsg())
                        .orderByAsc(ThirdAchievementModel::getToAccountDate)
                        .last("LIMIT 1000"));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void excelFlowing(List<ThirdPerformanceRequest> requests, ThirdSourceEnum thirdSourceEnum) {
        List<ThirdAchievementModel> saveModels = new ArrayList<>();
        List<MqOrderPaymentInfoModel> mq = Lists.newArrayList();
        int count = 1;
        for (ThirdPerformanceRequest request : requests) {
            try {
                List<Integer> dataStates = new ArrayList<>();
                dataStates.add(NumberConstants.INTEGER_VALUE_0);
                dataStates.add(NumberConstants.INTEGER_VALUE_3);

                if (!dataStates.contains(request.getDataState())) {
                    log.warn("中小数据流水录入,状态不为正常/删除状态,thirdId:{},dataStates:{}", request.getThirdId(), request.getDataState());
                    count++;
                    continue;
                }
                ThirdAchievementModel model = new ThirdAchievementModel();
                model.setThirdId(ObjectUtil.isEmpty(request.getThirdId()) ? String.valueOf(IdUtil.getSnowflake().nextId()) : request.getThirdId());

                List<ThirdAchievementModel> models = mqThirdAchievementRepository.list(
                        new LambdaQueryWrapper<ThirdAchievementModel>()
                                .eq(ThirdAchievementModel::getThirdId, request.getThirdId()));

                BeanUtil.copyProperties(request, model);
                // 中企传过来的数据，currentPrice 代表标准价 没有事业部提成业绩
                if (ObjectUtil.isEmpty(request.getCurrentPrice())) {
                    model.setStandardPrice(BigDecimal.ZERO);
                } else {
                    model.setStandardPrice(BigDecimal.valueOf(request.getCurrentPrice()));
                }
                model.setCurrentPrice(model.getManagerHiredMoney());
                if (ObjectUtil.isEmpty(request.getSubManagerHiredMoney())) {
                    model.setSubManagerHiredMoney(BigDecimal.ZERO.doubleValue());
                }

                model.setOrderId(String.valueOf(IdUtil.getSnowflake().nextId()));
                model.setTaskId(IdUtil.getSnowflake().nextId());
                model.setCreateName(request.getCreateUserName());
                model.setUpdateName(request.getUpdateUserName());
                model.setThirdSource(thirdSourceEnum.getMsg());
                model.setTaskStatus(TaskStatusEnum.NO.getCode());

                model.setSingingDate(DateUtils.stringToDate(request.getSingingDate(), DateTimeFormatStyleEnum.standard.getCode()));
                model.setToAccountDate(DateUtils.stringToDate(request.getToAccountDate(), DateTimeFormatStyleEnum.standard.getCode()));
                model.setServeFinishTime(DateUtils.stringToDate(request.getServeFinishTime(), DateTimeFormatStyleEnum.standard.getCode()));
                model.setDbInsertTime(DateUtils.stringToDate(request.getDbInsertTime(), DateTimeFormatStyleEnum.standard.getCode()));
                model.setDbUpdateTime(DateUtils.stringToDate(request.getDbUpdateTime(), DateTimeFormatStyleEnum.standard.getCode()));
                // 为了处理文件字段格式，在原字段后面加了 ZQKJ后缀
                handleSuffixes(model);
                // 新增
                if (ObjectUtil.isEmpty(models)) {
                    model.setTaskType(TaskTypeEnum.ADD.getMsg());
                } else {
                    // 修改或删除 将元数据进行删除标识 并将未执行完成的任务修改已完成 保存新数据
                    mqThirdAchievementRepository.lambdaUpdate()
                            .eq(ThirdAchievementModel::getThirdId, models.get(0).getThirdId())
                            .set(ThirdAchievementModel::getTaskStatus, TaskStatusEnum.YES.getCode())
                            .set(ThirdAchievementModel::getDeleteFlag, YesOrNotEnum.YES.getCode())
                            .update();
                    if (TaskTypeEnum.DELETE.getCode().equals(request.getDataState())) {
                        model.setTaskType(TaskTypeEnum.DELETE.getMsg());
                    } else {
                        model.setTaskType(TaskTypeEnum.UPDATE.getMsg());
                    }
                }
                saveModels.add(model);
                count++;
                // 保存任务表
                if (TaskTypeEnum.ADD.getMsg().equals(model.getTaskType())) {
                    MqOrderPaymentInfoModel mqOrderPaymentInfoModel = new MqOrderPaymentInfoModel();
                    mqOrderPaymentInfoModel.setTaskId(IdUtil.getSnowflake().nextId());
                    mqOrderPaymentInfoModel.setTaskType(model.getTaskType());
                    mqOrderPaymentInfoModel.setOrderId(model.getTaskId());
                    mqOrderPaymentInfoModel.setAchievementSource(AchievementSourceEnum.ZHONGXIAO.getCode());
                    mqOrderPaymentInfoModel.setThirdAchievementId(model.getThirdId());
                    mqOrderPaymentInfoModel.setPaymentTime(model.getToAccountDate());
                    mqOrderPaymentInfoModel.setCustomerId(model.getCustId());
                    mqOrderPaymentInfoModel.setCalculateType(CalculateTypeEnum.PAYMENT.getCode());
                    mqOrderPaymentInfoModel.setCreateTime(model.getToAccountDate());
                    mqOrderPaymentInfoModel.setDisplayed(ObjectUtil.isNotEmpty(model.getDisplayed()) ? model.getDisplayed() : NumberConstants.INTEGER_VALUE_0);
                    mqOrderPaymentInfoModel.setThirdSource(thirdSourceEnum.getMsg());
                    mq.add(mqOrderPaymentInfoModel);
                }
                if (mq.size() == 100) {
                    mqOrderPaymentInfoService.saveModels(mq);
                    mq.clear();
                }
                if (saveModels.size() == 100) {
                    mqThirdAchievementRepository.saveBatch(saveModels, saveModels.size());
                    saveModels.clear();
                }
                log.info("中小数据流水录入,thirdId:{}，当前条数:{}", request.getThirdId(), count);
            } catch (Exception ex) {
                log.error("中小数据流水录入失败,request:{}，异常信息:", JSONUtil.toJsonStr(request), ex);
                throw new BusinessException("中小数据流水录入失败,thirdId:" + request.getThirdId());
            }
        }
        if (ObjectUtil.isNotEmpty(mq)) {
            mqOrderPaymentInfoService.saveModels(mq);
        }
        if (ObjectUtil.isNotEmpty(saveModels)) {
            mqThirdAchievementRepository.saveBatch(saveModels, saveModels.size());
        }
        log.info("{}中小数据流水录入完成，count总条数:{},requests总条数:{}", DateUtils.format(new Date(), DateTimeFormatStyleEnum.yyyy_MM_dd_HH_mm_ss), count, requests.size());
    }

    @Override
    public ThirdAchievementModel getInfoByTaskId(Long taskId) {
        return mqThirdAchievementRepository.getOne(
                new LambdaQueryWrapper<ThirdAchievementModel>()
                        .eq(ThirdAchievementModel::getTaskId, taskId));
    }

    @Override
    public ThirdAchievementModel getInfoByThirdId(String thirdId) {
        return mqThirdAchievementRepository.getOne(
                new LambdaQueryWrapper<ThirdAchievementModel>()
                        .eq(ThirdAchievementModel::getThirdId, thirdId)
                        .eq(ThirdAchievementModel::getTaskType,TaskTypeEnum.ADD.getMsg())
                        .orderByDesc(ThirdAchievementModel::getId)
                        .last("limit 1"));
    }

    @Override
    public List<ThirdAchievementModel> getInfoByOrderNo(String orderNo) {
        if (ObjectUtil.isEmpty(orderNo)) {
            return new ArrayList<>();
        }
        return mqThirdAchievementRepository.list(
                new LambdaQueryWrapper<ThirdAchievementModel>()
                        .eq(ThirdAchievementModel::getOrderRecordCode, orderNo)
                        .eq(ThirdAchievementModel::getTaskType, TaskTypeEnum.ADD.getMsg())
                        .and(wrapper -> wrapper.isNotNull(ThirdAchievementModel::getState)
                                .notIn(ThirdAchievementModel::getState,
                                       ThirdAchievementStateEnum.INVALID.getCode(),
                                       ThirdAchievementStateEnum.COMPLETED.getCode()))
                        .orderByDesc(ThirdAchievementModel::getId));
    }

    @Override
    public void syncDtae() {
        int pageSize = 1; // 当前页码
        int size = 70;   // 每页大小
        boolean hasMoreData = true; // 是否还有更多数据

        while (hasMoreData) {
            log.info("同步三方数据到支付完成表中,pageSize:{},size:{}", pageSize, 100);
            // 分页查询
            Page<ThirdAchievementModel> page = new Page<>(pageSize, size);
            Page<ThirdAchievementModel> list = mqThirdAchievementRepository.page(page,
                    new LambdaQueryWrapper<ThirdAchievementModel>()
                            .eq(ThirdAchievementModel::getTaskType, TaskTypeEnum.ADD.getMsg())
                            .orderByAsc(ThirdAchievementModel::getToAccountDate));

            // 判断是否有数据
            if (ObjectUtil.isEmpty(list.getRecords())) {
                hasMoreData = false; // 没有更多数据，退出循环
            } else {
                // 处理查询到的数据
                List<MqOrderPaymentInfoModel> mqOrderPaymentInfoModels = new ArrayList<>();
                for (ThirdAchievementModel model : list.getRecords()) {
                    MqOrderPaymentInfoModel mqOrderPaymentInfoModel = new MqOrderPaymentInfoModel();
                    mqOrderPaymentInfoModel.setTaskId(IdUtil.getSnowflake().nextId());
                    mqOrderPaymentInfoModel.setTaskType(model.getTaskType());
                    mqOrderPaymentInfoModel.setOrderId(model.getTaskId());
                    mqOrderPaymentInfoModel.setAchievementSource(AchievementSourceEnum.ZHONGXIAO.getCode());
                    mqOrderPaymentInfoModel.setThirdAchievementId(model.getThirdId());
                    mqOrderPaymentInfoModel.setPaymentTime(model.getToAccountDate());
                    mqOrderPaymentInfoModel.setCustomerId(model.getCustId());
                    mqOrderPaymentInfoModels.add(mqOrderPaymentInfoModel);
                }

                // 保存数据
                mqOrderPaymentInfoService.saveModels(mqOrderPaymentInfoModels);

                // 增加页码，继续查询下一页
                pageSize++;
            }
        }
    }

    @Override
    public void updatePayTime() {
        mqOrderPaymentInfoService.updatePayTime();
    }

    @Override
    public Date getMinToAccountDateByOrderRecordCode(String orderRecordCode) {
        if (orderRecordCode == null || orderRecordCode.trim().isEmpty()) {
            return null;
        }

        ThirdAchievementModel result = mqThirdAchievementRepository.lambdaQuery()
                .eq(ThirdAchievementModel::getOrderRecordCode, orderRecordCode)
                .isNotNull(ThirdAchievementModel::getToAccountDate)
                .orderByAsc(ThirdAchievementModel::getToAccountDate)
                .last("LIMIT 1")
                .one();

        return result != null ? result.getToAccountDate() : null;
    }

    @Override
    public ThirdAchievementModel getMinToAccountDateRecordByOrderRecordCode(String orderRecordCode) {
        if (orderRecordCode == null || orderRecordCode.trim().isEmpty()) {
            return null;
        }

        return mqThirdAchievementRepository.lambdaQuery()
                .eq(ThirdAchievementModel::getOrderRecordCode, orderRecordCode)
                .isNotNull(ThirdAchievementModel::getToAccountDate)
                .isNotNull(ThirdAchievementModel::getState)
                .ne(ThirdAchievementModel::getState, ThirdAchievementStateEnum.INVALID.getCode())
                .ne(ThirdAchievementModel::getState, ThirdAchievementStateEnum.COMPLETED.getCode())
                .isNotNull(ThirdAchievementModel::getShareType)
                .ne(ThirdAchievementModel::getShareType, ShareTypeEnum.SUB.getCode())
                .orderByAsc(ThirdAchievementModel::getToAccountDate)
                .last("LIMIT 1")
                .one();
    }

}
