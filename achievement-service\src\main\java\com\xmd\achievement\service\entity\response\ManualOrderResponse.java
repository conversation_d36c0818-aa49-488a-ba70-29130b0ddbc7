package com.xmd.achievement.service.entity.response;

import cn.idev.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Data
public class ManualOrderResponse {
    @ExcelProperty("订单号")
    private String orderNo;
    
    @ExcelProperty("订单产品编码")
    private String orderProductCode;
    
    @ExcelProperty("订单时间")
    private String orderTime;
    
    @ExcelProperty("处理状态")
    private String isSuccess;
    
    @ExcelProperty("处理消息")
    private String message;

    public ManualOrderResponse() {
    }

    public ManualOrderResponse(String orderNo, String orderProductCode,String orderTime,String isSuccess) {
        this.orderNo = orderNo;
        this.orderProductCode = orderProductCode;
        this.isSuccess = isSuccess;
        this.orderTime=orderTime;
    }
}
