package com.xmd.achievement.dao.repository;

import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xmd.achievement.dao.dto.CustomerSaasGroupDto;
import com.xmd.achievement.dao.dto.CustomerSaasGroupQueryDto;
import com.xmd.achievement.dao.dto.SearchCustomerSaasDto;
import com.xmd.achievement.dao.entity.CustomerSaasModel;

public interface ICustomerSaasRepository extends IService<CustomerSaasModel> {
    /**
     * 获取指定客户的最大流失时间churnDate
     * @param customerId 客户ID
     * @return 最大流失时间
     */
    Date getMaxChurnDateByCustomerId(String customerId);

    /**
     * 获取指定客户在指定日期之前的最大流失时间churnDate
     * @param customerId 客户ID
     * @param date 截止日期（order_create_time < date）
     * @return 最大流失时间
     */
    Date getMaxChurnDateByCustomerId(String customerId, Date date,Long orderId);


    int countOrderBetween(String customerId, Date start, Date end, Integer orderSource);

    List<SearchCustomerSaasDto> searchCustomerSaasCount(List<String> businessIds, String startDate, String endDate);

    /**
     * 根据商务月、客户ID、商务ID分组查询客户SaaS信息，支持名称模糊查询（分页）
     * @param page 分页参数
     * @param request 查询请求参数
     * @return 分页查询结果
     */
    IPage<CustomerSaasGroupDto> searchCustomerSaasGroupedPage(Page<CustomerSaasGroupDto> page, CustomerSaasGroupQueryDto request);
}
