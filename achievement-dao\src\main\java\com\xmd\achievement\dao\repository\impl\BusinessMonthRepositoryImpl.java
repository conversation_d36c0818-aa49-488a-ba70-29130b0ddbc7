package com.xmd.achievement.dao.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xmd.achievement.dao.entity.BusinessMonthModel;
import com.xmd.achievement.dao.mapper.BusinessMonthMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xmd.achievement.dao.repository.IBusinessMonthRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 商务月表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@Service
@Slf4j
public class BusinessMonthRepositoryImpl extends ServiceImpl<BusinessMonthMapper, BusinessMonthModel> implements IBusinessMonthRepository {

    @Resource
    private BusinessMonthMapper businessMonthMapper;

    @Override
    public List<BusinessMonthModel> selectAllBusinessMonthList() {
        LambdaQueryWrapper<BusinessMonthModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusinessMonthModel::getDeleteFlag, 0);
        return this.list(queryWrapper);
    }

    @Override
    public List<BusinessMonthModel> selectBusinessMonthByMonthList(List<String> monthList) {
        LambdaQueryWrapper<BusinessMonthModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(BusinessMonthModel::getMonth, monthList);
        queryWrapper.eq(BusinessMonthModel::getDeleteFlag, 0);
        return this.list(queryWrapper);
    }

    @Override
    public BusinessMonthModel selectBusinessMonthByDate(Date createTime) {
        LambdaQueryWrapper<BusinessMonthModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.le(BusinessMonthModel::getStartDate, createTime);
        queryWrapper.eq(BusinessMonthModel::getDeleteFlag, 0);
        queryWrapper.orderByDesc(BusinessMonthModel::getMonth);
        queryWrapper.last("limit 1");
        return this.getOne(queryWrapper);
    }

    @Override
    public List<BusinessMonthModel> selectBusinessMonthByThreeMonth(String currentMonthStr) {
        LambdaQueryWrapper<BusinessMonthModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.le(BusinessMonthModel::getMonth, currentMonthStr);
        queryWrapper.eq(BusinessMonthModel::getDeleteFlag, 0);
        queryWrapper.orderByDesc(BusinessMonthModel::getMonth);
        queryWrapper.last("limit 3");
        return this.list(queryWrapper);
    }

    @Override
    public BusinessMonthModel selectBusinessMonthByCurrentDate(String currentMonthStr) {
        LambdaQueryWrapper<BusinessMonthModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusinessMonthModel::getMonth, currentMonthStr);
        queryWrapper.eq(BusinessMonthModel::getDeleteFlag, 0);
        return this.getOne(queryWrapper);
    }

    @Override
    public BusinessMonthModel selectCurrentBusinessMonth() {
        LambdaQueryWrapper<BusinessMonthModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByDesc(BusinessMonthModel::getMonth);
        queryWrapper.last("limit 1");
        return this.getOne(queryWrapper);
    }

    @Override
    public BusinessMonthModel selectPreviousBusinessMonth(String currentMonth) {
        LambdaQueryWrapper<BusinessMonthModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.lt(BusinessMonthModel::getMonth, currentMonth);
        queryWrapper.eq(BusinessMonthModel::getDeleteFlag, 0);
        queryWrapper.orderByDesc(BusinessMonthModel::getMonth);
        queryWrapper.last("limit 1");
        return this.getOne(queryWrapper);
    }

}