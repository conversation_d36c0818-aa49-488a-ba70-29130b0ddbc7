package com.xmd.achievement.handler.init;

import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import com.xmd.achievement.service.ICustomerSaasService;

@Component
public class InitAsyncService {

    @Resource
    private ICustomerSaasService customerSaasService;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 异步重试计算历史数据
     */
    @Async
    public void retryRecalculateHistory() {
        String REDIS_KEY_RETRY = "customer_saas:retry:";
        String retryValue = stringRedisTemplate.opsForValue().get(REDIS_KEY_RETRY);
        if (retryValue == null) {
            return;
        }
        String lockKey = "customer_saas:retry:recalculateHistory:lock";
        RLock lock = null;
        try {
            lock = redissonClient.getLock(lockKey);
            boolean acquired = lock.tryLock(0, -1, TimeUnit.SECONDS);
            if (acquired) {
                customerSaasService.recalculateHistory();
            }
        } catch (Exception e) {
        }
    }
}
