package com.xmd.achievement.service.impl;

import com.xmd.achievement.dao.entity.AchievementProductDetailModel;
import com.xmd.achievement.dao.entity.BusinessMonthModel;
import com.xmd.achievement.dao.repository.IAchievementProductDetailRepository;
import com.xmd.achievement.dao.repository.IBusinessMonthRepository;
import com.xmd.achievement.handler.statistics.BusinessAchHandler;
import com.xmd.achievement.service.IBusinessMonthService;
import com.xmd.achievement.service.SyncBusinessAchievementStatisticsService;
import com.xmd.achievement.util.enums.BusinessAchievementUpdateTypeEnum;
import com.xmd.achievement.util.enums.DateTimeFormatStyleEnum;
import com.xmd.achievement.web.annotate.lock.annotation.Lock;
import com.xmd.achievement.web.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SyncBusinessAchievementStatisticsServiceImpl implements SyncBusinessAchievementStatisticsService {


    @Resource
    private IBusinessMonthRepository businessMonthRepository;

    @Resource
    private IAchievementProductDetailRepository achievementProductDetailRepository;

    @Resource
    private BusinessAchHandler businessAchHandler;
    @Resource
    private IBusinessMonthService businessMonthService;

    @Override
    @Lock("'syncBusinessAchievementStatistics_' + #employeeId + '_' + #businessMonth")
    public void syncBusinessAchievementStatistics(String employeeId,String businessMonth,Boolean isSyncAll) {
        log.info("SyncBusinessAchievementStatisticsServiceImpl.syncBusinessAchievementStatistics employeeId:{} businessMonth:{}",employeeId,businessMonth);
        List<AchievementProductDetailModel> achList = null;

        if(null != isSyncAll && isSyncAll){
            //同步全量
            achList =  achievementProductDetailRepository.selectAchievementProductDetailListByMonth(null,employeeId);
        }else if(StringUtils.isEmpty(employeeId) && StringUtils.isEmpty(businessMonth)){
            // 同步当前商务月，同时检查上一个商务月是否冻结
            String currentMonth = DateUtils.formatDate(new Date(), DateTimeFormatStyleEnum.yyyy_MM.getCode());
            achList = achievementProductDetailRepository.selectAchievementProductDetailListByMonth(currentMonth,employeeId);
            
            // 检查并添加上一个商务月的数据（如果未冻结）
            addPreviousMonthDataIfUnfrozen(achList, employeeId);
        }else{
            //指定重跑
            if(!StringUtils.isEmpty(businessMonth)){
                achList =  achievementProductDetailRepository.selectAchievementProductDetailListByMonth(businessMonth,employeeId);
            }
        }
        
        //根据商务月分组
        Map<String, List<AchievementProductDetailModel>> byAchievementMonthMap = achList.stream().filter(ach-> StringUtils.isNotEmpty(ach.getBusinessMonth())).collect(Collectors.groupingBy(AchievementProductDetailModel::getBusinessMonth));

        for (Map.Entry<String, List<AchievementProductDetailModel>> entry : byAchievementMonthMap.entrySet()) {
            List<AchievementProductDetailModel> list = entry.getValue();
            if (StringUtils.isBlank(employeeId)) {
                businessAchHandler.achStatistics(list, null, BusinessAchievementUpdateTypeEnum.NORMAL.getUpdateType());
                continue;
            }
            businessAchHandler.achStatisticsForEmployeeId(list, null, BusinessAchievementUpdateTypeEnum.NORMAL.getUpdateType());
        }
    }

    /**
     * 检查上一个商务月是否冻结，如果未冻结则添加上一个商务月的数据到当前列表中
     * 
     * @param achList 当前业绩数据列表
     * @param employeeId 员工ID
     */
    private void addPreviousMonthDataIfUnfrozen(List<AchievementProductDetailModel> achList, String employeeId) {
        BusinessMonthModel currentBusinessMonth = businessMonthRepository.selectCurrentBusinessMonth();
        if (currentBusinessMonth != null) {
            BusinessMonthModel previousMonth = businessMonthRepository.selectPreviousBusinessMonth(currentBusinessMonth.getMonth());
            if (previousMonth != null && !businessMonthService.isMonthFrozen(previousMonth)) {
                log.info("发现上一个商务月{}未冻结，加入重新计算", previousMonth.getMonth());
                List<AchievementProductDetailModel> previousMonthList = 
                    achievementProductDetailRepository.selectAchievementProductDetailListByMonth(previousMonth.getMonth(), employeeId);
                if (previousMonthList != null && !previousMonthList.isEmpty()) {
                    achList.addAll(previousMonthList);
                    log.info("已将上一个商务月{}的{}条业绩数据加入重新计算", previousMonth.getMonth(), previousMonthList.size());
                }
            }
        }
    }
}
