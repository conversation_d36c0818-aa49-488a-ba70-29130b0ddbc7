package com.xmd.achievement.handler.achievement;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xmd.achievement.dao.entity.AchievementCategoryDetailModel;
import com.xmd.achievement.dao.entity.AchievementPolicyModel;
import com.xmd.achievement.dao.entity.AchievementProductDetailModel;
import com.xmd.achievement.dao.entity.AchievementSpecDetailModel;
import com.xmd.achievement.dao.entity.BusinessMonthModel;
import com.xmd.achievement.dao.entity.MqOrderPaymentInfoModel;
import com.xmd.achievement.dao.entity.MqOrderRefundInfoModel;
import com.xmd.achievement.dao.repository.IAchievementCategoryDetailRepository;
import com.xmd.achievement.dao.repository.IAchievementPolicyRepository;
import com.xmd.achievement.dao.repository.IAchievementProductDetailRepository;
import com.xmd.achievement.dao.repository.IAchievementSpecDetailRepository;
import com.xmd.achievement.dao.repository.IMqOrderPaymentInfoRepository;
import com.xmd.achievement.dao.repository.IMqOrderRefundInfoRepository;
import com.xmd.achievement.rpc.InnerService;
import com.xmd.achievement.rpc.entity.dto.AfterSalesItemResp;
import com.xmd.achievement.rpc.entity.dto.AfterSalesItemSpecResp;
import com.xmd.achievement.rpc.entity.dto.AfterSalesOrderDetailResp;
import com.xmd.achievement.service.IBusinessMonthService;
import com.xmd.achievement.service.ICustomerSaasService;
import com.xmd.achievement.support.constant.NumberConstants;
import com.xmd.achievement.support.constant.enums.AchievementSourceEnum;
import com.xmd.achievement.support.constant.enums.DeleteFlagEnum;
import com.xmd.achievement.support.constant.enums.OrderSaleTypeEnum;
import com.xmd.achievement.support.constant.enums.PolicyRevenueNodeEnum;
import com.xmd.achievement.support.constant.enums.TaskStatusEnum;
import com.xmd.achievement.support.constant.enums.TaskTypeEnum;
import com.xmd.achievement.support.constant.enums.ThirdSourceEnum;
import com.xmd.achievement.support.mq.RocketMqOperate;
import com.xmd.achievement.util.enums.CalculateTypeEnum;
import com.xmd.achievement.util.enums.StatusEnum;
import com.xmd.achievement.web.config.ProductAchievementConfig;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class AchievementRefundHandler {

    @Resource
    private IMqOrderRefundInfoRepository mqOrderRefundInfoRepository;

    @Resource
    private IAchievementSpecDetailRepository achievementSpecDetailRepository;

    @Resource
    private IAchievementProductDetailRepository achievementProductDetailRepository;

    @Resource
    private IAchievementCategoryDetailRepository achievementCategoryDetailRepository;

    @Resource
    private IBusinessMonthService businessMonthService;

    @Resource
    private ICustomerSaasService customerSaasService;

    @Resource
    private AchievementRefundCommonHandler refundCommonHandler;

    // 自注入，用于调用带事务注解的方法
    @Resource
    private AchievementRefundHandler self;

    @Resource
    private RocketMqOperate rocketMqOperate;

    @Resource
    private InnerService innerService;

    @Resource
    private ProductAchievementConfig productAchievementConfig;

    @Resource
    private AchievementRefundWebsiteHandler achievementRefundWebsiteHandler;

    @Resource
    private IAchievementPolicyRepository achievementPolicyRepository;

    @Resource
    private IMqOrderPaymentInfoRepository mqOrderPaymentInfoRepository;

    @Resource
    private AchievementHandler achievementHandler;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    // ==================== 主要入口方法 ====================

    /**
     * 处理待处理的退款任务数据
     * 条件：task_status=1 AND delete_flag=0 AND fail_count<5
     * 遍历数据并查询相关的业绩明细表
     */
    public void processPendingRefundTasks() {
        log.info("开始处理待处理的退款任务数据");

        // 查询符合条件的退款任务数据
        LambdaQueryWrapper<MqOrderRefundInfoModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MqOrderRefundInfoModel::getTaskStatus, TaskStatusEnum.NO.getCode())
                .eq(MqOrderRefundInfoModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE)
                .eq(MqOrderRefundInfoModel::getSaleType, OrderSaleTypeEnum.REFUND.getType())
                .le(MqOrderRefundInfoModel::getFailCount, NumberConstants.INTEGER_VALUE_5); // 失败次数小于阈值

        List<MqOrderRefundInfoModel> refundTaskList = mqOrderRefundInfoRepository.list(queryWrapper);

        log.info("查询到待处理的退款任务数据条数: {}", refundTaskList.size());

        // 遍历退款任务数据
        for (MqOrderRefundInfoModel refundTask : refundTaskList) {
            processRefundTask(refundTask);
        }

        log.info("退款任务处理完成");

    }

    private void processRefundTaskCore(MqOrderRefundInfoModel refundTask) {
        self.processRefundTaskByType(refundTask);
        customerSaasService.refundHandler(refundTask);
    }

    public void processRefundTask(MqOrderRefundInfoModel refundTask) {
        // 通过自注入调用带事务和锁的方法
        self.processRefundTaskWithLock(refundTask);
    }
    
    @Transactional(rollbackFor = Exception.class)
    public void processRefundTaskWithLock(MqOrderRefundInfoModel refundTask) {
        String lockKey = "processRefundTask_" + refundTask.getTaskId();
        
        try {
            // 尝试获取Redis分布式锁，如果已存在则直接返回
            if (Boolean.TRUE.equals(stringRedisTemplate.hasKey(lockKey))) {
                log.info("任务ID {} 正在被其他服务器处理，跳过", refundTask.getTaskId());
                return;
            }
            
            // 设置Redis锁，过期时间5分钟
            Boolean lockAcquired = stringRedisTemplate.opsForValue().setIfAbsent(lockKey, "processing", 5, TimeUnit.MINUTES);
            if (!Boolean.TRUE.equals(lockAcquired)) {
                log.info("任务ID {} 获取分布式锁失败，跳过", refundTask.getTaskId());
                return;
            }
            
            log.info("任务ID {} 获取分布式锁成功，开始处理", refundTask.getTaskId());
            
            if (TaskStatusEnum.YES.getCode().equals(refundTask.getTaskStatus())) {
                return;
            }
            
            processRefundTaskCore(refundTask);
            // 执行通用处理逻辑
            updateTaskStatusToCompleted(refundTask);
            
        } catch (Exception e) {
            log.error("处理退款任务失败，任务ID: {}, 订单ID: {}, task_type: {}, 错误信息: {}",
                    refundTask.getTaskId(), refundTask.getOrderId(), refundTask.getTaskType(), e.getMessage(), e);
            // 处理失败，增加失败次数
            updateTaskFailCount(refundTask);
            throw e; // 重新抛出异常以触发事务回滚
        } finally {
            // 无论成功或失败都要删除锁
            stringRedisTemplate.delete(lockKey);
            log.info("任务ID {} 处理完成，释放分布式锁", refundTask.getTaskId());
        }
    }

    /**
     * 根据task_type调用不同的处理方法
     *
     * @param refundTask 退款任务数据
     */
    @Transactional(rollbackFor = Exception.class)
    public void processRefundTaskByType(MqOrderRefundInfoModel refundTask) {
        String taskType = refundTask.getTaskType();

        if (TaskTypeEnum.ADD.getMsg().equals(taskType)) {
            // 处理新增类型的退款任务
            processAddRefundTask(refundTask);
        } else if (TaskTypeEnum.UPDATE.getMsg().equals(taskType)) {
            // 处理修改类型的退款任务
            processUpdateRefundTask(refundTask);
        } else if (TaskTypeEnum.DELETE.getMsg().equals(taskType)) {
            // 处理删除类型的退款任务
            processDeleteRefundTask(refundTask);
        } else {
            log.warn("未知的task_type类型，任务ID: {}, 订单ID: {}, task_type: {}",
                    refundTask.getTaskId(), refundTask.getOrderId(), taskType);
            throw new IllegalArgumentException("未支持的task_type类型: " + taskType);
        }
    }

    /**
     * 处理新增类型的退款任务
     *
     * @param refundTask 退款任务数据
     */
    private void processAddRefundTask(MqOrderRefundInfoModel refundTask) {
        log.info("处理ADD类型退款任务 - 任务ID: {}, 订单ID: {}",
                refundTask.getTaskId(), refundTask.getOrderId());
        addRefundTaskHandler(refundTask);
    }

    // ==================== 任务类型处理方法 ====================

    /**
     * 处理修改类型的退款任务
     *
     * @param refundTask 退款任务数据
     */
    private void processUpdateRefundTask(MqOrderRefundInfoModel refundTask) {
        log.info("处理UPDATE类型退款任务 - 任务ID: {}, 订单ID: {}",
                refundTask.getTaskId(), refundTask.getOrderId());
        // TODO: 实现修改类型的退款处理逻辑
        log.warn("UPDATE类型退款任务处理逻辑待实现");
    }

    /**
     * 处理删除类型的退款任务
     *
     * @param refundTask 退款任务数据
     */
    private void processDeleteRefundTask(MqOrderRefundInfoModel refundTask) {
        log.info("处理DELETE类型退款任务 - 任务ID: {}, 订单ID: {}",
                refundTask.getTaskId(), refundTask.getOrderId());
        // TODO: 实现删除类型的退款处理逻辑
        log.warn("DELETE类型退款任务处理逻辑待实现");
    }

    // ==================== 核心业务处理方法 ====================

    /**
     * 处理单个退款任务（原有的ADD类型处理逻辑）
     *
     * @param refundTask 退款任务数据
     */
    private void addRefundTaskHandler(MqOrderRefundInfoModel refundTask) {
        Long orderId = refundTask.getOrderId();
        Long productId = refundTask.getProductId();
        String orderProductCode = refundTask.getOrderProductCode();

        log.info("处理退款任务 - 任务ID: {}, 订单ID: {}, 商品ID: {}, 订单商品编码: {}",
                refundTask.getTaskId(), orderId, productId, orderProductCode);

        // 1. 查询售后订单详情
        AfterSalesOrderDetailResp response = innerService.queryAfterSalesOrderDetail(refundTask.getAftersaleOrderNo());
        if (response == null) {
            log.error("查询售后订单详情失败，售后订单号: {}，直接退出", refundTask.getAftersaleOrderNo());
            return;
        }

        // 2. 先查出 productDetailList
        List<AchievementProductDetailModel> productDetailList = queryAchievementProductDetail(orderId, productId,
                orderProductCode);
        log.info("查询到业绩商品明细数据条数: {}", productDetailList.size());

        if (CollectionUtils.isEmpty(productDetailList)) {
            log.warn("查询业绩商品明细数据为空，订单ID: {}，商品ID: {}，订单商品编码: {}，直接退出",
                    orderId, productId, orderProductCode);
            return;
        }

        // 3. 检测是否有异常业绩，如果有则发送微信消息
        refundCommonHandler.checkAbnormalAndSendWxMessage(productDetailList);

        // 3. 定义需要更新的3个表的对象数组
        List<AchievementProductDetailModel> refundProductList = new ArrayList<>();
        List<AchievementCategoryDetailModel> refundCategoryList = new ArrayList<>();
        List<AchievementSpecDetailModel> refundSpecList = new ArrayList<>();

        // 4.生成退款业绩流水
        generateRefundAchievementFlow(refundProductList, refundCategoryList, refundSpecList, refundTask, response);

        // 4. 保存所有退款记录
        saveAllRefundRecords(refundProductList, refundCategoryList, refundSpecList);

        // 清空列表，为网站产品特殊处理做准备
        refundProductList.clear();
        refundCategoryList.clear();
        refundSpecList.clear();

        for (AchievementProductDetailModel productDetail : productDetailList) {
            // 判断是否为网站产品，如果是则需要特殊处理
            if (productDetail.getProductId().equals(Long.valueOf(productAchievementConfig.getWebsite()))
                    && productDetail.getPaidAmount() != null
                    && productDetail.getPaidAmount().compareTo(BigDecimal.ZERO) > 0) {
                achievementRefundWebsiteHandler.processWebsiteProductRefund(productDetail, refundTask,
                        refundProductList, refundCategoryList, refundSpecList);
            }
        }

        // 6. 保存网站产品特殊处理产生的退款记录
        if (!refundProductList.isEmpty() || !refundCategoryList.isEmpty() || !refundSpecList.isEmpty()) {
            saveAllRefundRecords(refundProductList, refundCategoryList, refundSpecList);
        }
    }

    /**
     * 生成退款业绩流水
     *
     * @param refundProductList  业绩商品明细退款列表
     * @param refundCategoryList 业绩分类明细退款列表
     * @param refundSpecList     业绩规格明细退款列表
     * @param refundTask         退款任务数据
     * @param response           售后订单详情响应
     */
    private void generateRefundAchievementFlow(List<AchievementProductDetailModel> refundProductList,
            List<AchievementCategoryDetailModel> refundCategoryList,
            List<AchievementSpecDetailModel> refundSpecList,
            MqOrderRefundInfoModel refundTask,
            AfterSalesOrderDetailResp response) {

        if (response != null && response.getAfterSalesItemResps() != null) {
            for (AfterSalesItemResp item : response.getAfterSalesItemResps()) {
                String orderProductCode = item.getOrderProductCode();
                Long productId = item.getItemId(); // 直接使用itemId作为商品ID

                // 检测必要参数是否为空
                if (orderProductCode == null || productId == null) {
                    log.warn("商品编码 {} 或商品ID {} 为空，跳过处理", orderProductCode, productId);
                    continue;
                }
                // 检测缓发业绩信息
                checkDeferredCommissionInfo(item, response);

                // 生成退款业绩
                generateRefundAchievement(refundProductList, refundCategoryList, refundSpecList, item, response,
                        refundTask);
            }
        }
    }

    /**
     * 保存所有退款记录到数据库
     */
    private void saveAllRefundRecords(List<AchievementProductDetailModel> refundProductList,
            List<AchievementCategoryDetailModel> refundCategoryList,
            List<AchievementSpecDetailModel> refundSpecList) {
        try {
            // 保存业绩商品明细退款记录
            if (!refundProductList.isEmpty()) {
                achievementProductDetailRepository.saveBatch(refundProductList);
                log.info("成功保存 {} 条业绩商品明细退款记录", refundProductList.size());
            }

            // 保存业绩分类明细退款记录
            if (!refundCategoryList.isEmpty()) {
                achievementCategoryDetailRepository.saveBatch(refundCategoryList);
                log.info("成功保存 {} 条业绩分类明细退款记录", refundCategoryList.size());
            }

            // 保存业绩规格明细退款记录
            if (!refundSpecList.isEmpty()) {
                achievementSpecDetailRepository.saveBatch(refundSpecList);
                log.info("成功保存 {} 条业绩规格明细退款记录", refundSpecList.size());
            }

        } catch (Exception e) {
            log.error("保存退款记录失败: {}", e.getMessage(), e);
            // 重新抛出异常，让上层处理
            throw e;
        }
    }

    /**
     * 更新任务状态为已完成
     */
    private void updateTaskStatusToCompleted(MqOrderRefundInfoModel refundTask) {
        try {
            refundTask.setTaskStatus(TaskStatusEnum.YES.getCode());
            mqOrderRefundInfoRepository.updateById(refundTask);
            log.info("任务处理成功，更新状态为已完成 - 任务ID: {}", refundTask.getTaskId());
        } catch (Exception e) {
            refundTask.setFailReason(e.getMessage());
            mqOrderRefundInfoRepository.updateById(refundTask);
            log.error("更新任务状态失败 - 任务ID: {}, 错误: {}", refundTask.getTaskId(), e.getMessage(), e);
        }
    }

    /**
     * 更新任务失败次数
     */
    private void updateTaskFailCount(MqOrderRefundInfoModel refundTask) {
        try {
            Integer currentFailCount = refundTask.getFailCount() != null ? refundTask.getFailCount() : 0;
            refundTask.setFailCount(currentFailCount + 1);
            refundTask.setUpdateTime(new Date());
            mqOrderRefundInfoRepository.updateById(refundTask);
            log.info("任务处理失败，更新失败次数 - 任务ID: {}, 失败次数: {}", refundTask.getTaskId(), refundTask.getFailCount());
        } catch (Exception e) {
            log.error("更新任务失败次数失败 - 任务ID: {}, 错误: {}", refundTask.getTaskId(), e.getMessage(), e);
        }
    }

    /**
     * 查询业绩商品明细表
     *
     * @param orderId          订单ID
     * @param productId        商品ID
     * @param orderProductCode 订单商品编码（映射为orderProductId）
     * @return 业绩商品明细列表
     */
    private List<AchievementProductDetailModel> queryAchievementProductDetail(Long orderId, Long productId,
            String orderProductCode) {
        LambdaQueryWrapper<AchievementProductDetailModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AchievementProductDetailModel::getOrderId, orderId)
                .eq(StrUtil.isNotBlank(orderProductCode), AchievementProductDetailModel::getOrderProductId,
                        orderProductCode)
                .eq(AchievementProductDetailModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode());

        return achievementProductDetailRepository.list(queryWrapper);
    }

    /**
     * 根据订单商品编码查询原始业绩明细记录
     *
     * @param orderProductCode 订单商品编码
     * @return 业绩商品明细列表
     */
    private List<AchievementProductDetailModel> queryOriginalProductDetails(String orderProductCode) {
        LambdaQueryWrapper<AchievementProductDetailModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AchievementProductDetailModel::getOrderProductId, orderProductCode)
                .eq(AchievementProductDetailModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                .in(AchievementProductDetailModel::getStatus, StatusEnum.VALID.getCode(),
                        StatusEnum.COMPLETED.getCode());
        return achievementProductDetailRepository.list(queryWrapper);
    }

    /**
     * 检测缓发业绩信息
     *
     * @param item     售后商品信息
     * @param response 售后订单详情响应
     */
    private void checkDeferredCommissionInfo(AfterSalesItemResp item, AfterSalesOrderDetailResp response) {
        Long itemId = item.getItemId();
        String orderProductCode = item.getOrderProductCode();

        log.info("开始检测商品编码 {} (itemId: {}) 的缓发业绩信息", orderProductCode, itemId);

        // 1. 根据itemId查询achievement_policy表的revenue_node值
        LambdaQueryWrapper<AchievementPolicyModel> policyQueryWrapper = new LambdaQueryWrapper<>();
        policyQueryWrapper.eq(AchievementPolicyModel::getProductId, itemId)
                .eq(AchievementPolicyModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                .last("LIMIT 1");

        AchievementPolicyModel policy = achievementPolicyRepository.getOne(policyQueryWrapper);

        if (policy == null) {
            log.warn("商品ID {} 未找到对应的业绩政策配置", itemId);
            return;
        }

        Integer revenueNode = policy.getRevenueNode();
        if (PolicyRevenueNodeEnum.PAID_FINISHED.getType().equals(revenueNode)) {
            return;
        }
        // 查询业绩明细记录
        List<AchievementProductDetailModel> productDetails = queryOriginalProductDetails(orderProductCode);

        if (CollectionUtils.isEmpty(productDetails)) {
            log.warn("商品编码 {} 未找到业绩明细记录", orderProductCode);
            return;
        }

        Set<String> processedCombinations = new HashSet<>();

        for (AchievementProductDetailModel detail : productDetails) {
            // 构建唯一标识：orderProductId + payType + installmentNum
            String combinationKey = detail.getOrderProductId() + "_" + detail.getPayType() + "_"
                    + detail.getInstallmentNum();

            if (processedCombinations.contains(combinationKey)) {
                continue;
            }

            // 记录已处理的组合
            processedCombinations.add(combinationKey);
            checkPaymentInfo(detail, response);
        }

    }

    /**
     * 检查支付信息表中的缓发业绩相关数据
     *
     * @param detail   业绩明细记录
     * @param response 售后订单详情响应
     */
    private void checkPaymentInfo(AchievementProductDetailModel detail, AfterSalesOrderDetailResp response) {
        log.info("检查业绩记录 {} 对应的支付信息", detail.getAchievementId());

        // 根据achievement_id查询achievement_category_detail表的count
        LambdaQueryWrapper<AchievementCategoryDetailModel> categoryQueryWrapper = new LambdaQueryWrapper<>();
        categoryQueryWrapper.eq(AchievementCategoryDetailModel::getAchievementId, detail.getAchievementId())
                .eq(AchievementCategoryDetailModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode());

        long categoryCount = achievementCategoryDetailRepository.count(categoryQueryWrapper);
        if (categoryCount == 0) {
            log.info("业绩ID {} 未找到分类明细记录，直接返回", detail.getAchievementId());
            return;
        }

        // 查询mq_order_payment_info表的count
        LambdaQueryWrapper<MqOrderPaymentInfoModel> paymentQueryWrapper = new LambdaQueryWrapper<>();
        paymentQueryWrapper.eq(MqOrderPaymentInfoModel::getOrderProductId, detail.getOrderProductId())
                .eq(MqOrderPaymentInfoModel::getCalculateType, CalculateTypeEnum.SERVEINPROGRESS.getCode()) // 枚举值2
                .eq(MqOrderPaymentInfoModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                .eq(MqOrderPaymentInfoModel::getInstallmentStatus, detail.getPayType())
                .eq(MqOrderPaymentInfoModel::getInstallmentNum, detail.getInstallmentNum());

        long paymentCount = mqOrderPaymentInfoRepository.count(paymentQueryWrapper);
        if (paymentCount > 0) {
            return;
        }

        // 根据AchievementProductDetailModel创建MqOrderPaymentInfoModel对象
        MqOrderPaymentInfoModel model = createMqOrderPaymentInfoModel(detail, response);

        // 调用AchievementHandler.newHandlerServiceAchievement处理服务中业绩
        if (CalculateTypeEnum.SERVEINPROGRESS.getCode().equals(model.getCalculateType())) {
            achievementHandler.newHandlerServiceAchievement(model, false, null);
        }

    }

    /**
     * 根据AchievementProductDetailModel创建MqOrderPaymentInfoModel对象
     *
     * @param detail   业绩明细记录
     * @param response 售后订单详情响应对象
     * @return MqOrderPaymentInfoModel对象
     */
    private MqOrderPaymentInfoModel createMqOrderPaymentInfoModel(AchievementProductDetailModel detail,
            AfterSalesOrderDetailResp response) {
        MqOrderPaymentInfoModel model = new MqOrderPaymentInfoModel();
        // 设置基本信息
        model.setTaskId(IdUtil.getSnowflakeNextId());
        model.setTaskType(TaskTypeEnum.ADD.getMsg());
        model.setCalculateType(CalculateTypeEnum.SERVEINPROGRESS.getCode()); // 服务中
        model.setOrderId(detail.getOrderId());
        model.setTaskStatus("1"); // 未完成
        model.setProductId(detail.getProductId());
        model.setServeNo(detail.getServeNo());
        model.setAchievementSource(AchievementSourceEnum.KUAJINFG.getCode());
        model.setThirdAchievementId(detail.getThirdAchievementId());
        model.setPaymentTime(detail.getPaymentTime());
        model.setCustomerId(detail.getCustomerId());
        model.setThirdSource(ThirdSourceEnum.ONLINE.getMsg());
        model.setInstallmentStatus(Integer.parseInt(detail.getPayType()));
        model.setInstallmentNum(detail.getInstallmentNum());
        model.setOrderProductId(detail.getOrderProductId());
        model.setDisplayed(0);
        model.setCustomerId(detail.getCustomerId());
        // 设置时间信息，使用售后订单的申请时间
        Date applyTime = response.getAfterSalesOrderResp().getApplyTime();
        model.setCreateTime(applyTime);
        model.setUpdateTime(applyTime);
        model.setDeleteFlag(DeleteFlagEnum.NOT_DELETE.getCode());

        log.info("创建MqOrderPaymentInfoModel对象 - 任务ID: {}, 订单ID: {}, 商品ID: {}, 计算类型: {}",
                model.getTaskId(), model.getOrderId(), model.getProductId(), model.getCalculateType());

        return model;
    }

    /**
     * 生成退款业绩
     *
     * @param refundProductList  业绩商品明细退款列表
     * @param refundCategoryList 业绩分类明细退款列表
     * @param refundSpecList     业绩规格明细退款列表
     * @param item               售后商品信息
     * @param response           售后订单详情响应
     * @param refundTask         退款任务数据
     */
    private void generateRefundAchievement(List<AchievementProductDetailModel> refundProductList,
            List<AchievementCategoryDetailModel> refundCategoryList,
            List<AchievementSpecDetailModel> refundSpecList,
            AfterSalesItemResp item,
            AfterSalesOrderDetailResp response,
            MqOrderRefundInfoModel refundTask) {

        String orderProductCode = item.getOrderProductCode();
        Long productId = item.getItemId();

        log.info("开始生成商品编码 {} (商品ID: {}) 的退款业绩", orderProductCode, productId);

        // 1. 根据orderProductCode查询achievement_product_detail
        List<AchievementProductDetailModel> productDetailList = queryOriginalProductDetails(orderProductCode);

        if (CollectionUtils.isEmpty(productDetailList)) {
            log.warn("商品编码 {} 未找到业绩明细记录，无法生成退款业绩", orderProductCode);
            return;
        }

        log.info("商品编码 {} 查询到 {} 条业绩明细记录", orderProductCode, productDetailList.size());

        // 2. 遍历achievement_product_detail的结果
        for (AchievementProductDetailModel productDetail : productDetailList) {
            Long achievementId = productDetail.getAchievementId();

            // 根据achievement_id去匹配achievement_category_detail，查询count
            LambdaQueryWrapper<AchievementCategoryDetailModel> categoryQueryWrapper = new LambdaQueryWrapper<>();
            categoryQueryWrapper.eq(AchievementCategoryDetailModel::getAchievementId, achievementId)
                    .eq(AchievementCategoryDetailModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode());

            long categoryCount = achievementCategoryDetailRepository.count(categoryQueryWrapper);

            if (categoryCount == 0) {
                generateProductLevelRefund(refundProductList, productDetail, item, response, refundTask);
            } else {
                generateSpecLevelRefund(refundProductList, refundCategoryList, refundSpecList, productDetail, item,
                        response, refundTask);
            }
        }

        log.info("完成商品编码 {} 的退款业绩生成", orderProductCode);
    }

    /**
     * 生成product级别的退款业绩
     *
     * @param refundProductList 业绩商品明细退款列表
     * @param productDetail     业绩商品明细
     * @param item              售后商品信息
     * @param response          售后订单详情响应
     * @param refundTask        退款任务数据
     */
    private void generateProductLevelRefund(List<AchievementProductDetailModel> refundProductList,
            AchievementProductDetailModel productDetail,
            AfterSalesItemResp item,
            AfterSalesOrderDetailResp response,
            MqOrderRefundInfoModel refundTask) {

        Long achievementId = productDetail.getAchievementId();
        log.info("开始生成业绩ID {} 的product级别退款业绩", achievementId);

        // 1. 生成退款商品明细记录
        AchievementProductDetailModel refundProduct = generateRefundProductFromItem(
                productDetail, item, response, refundTask);

        // 2. 添加到退款列表中
        refundProductList.add(refundProduct);

        log.info("完成业绩ID {} 的product级别退款业绩生成", achievementId);
    }

    /**
     * 根据售后商品信息生成退款商品明细记录
     *
     * @param productDetail 原始业绩商品明细
     * @param item          售后商品信息
     * @param response      售后订单详情响应
     * @param refundTask    退款任务数据
     * @return 退款商品明细记录
     */
    private AchievementProductDetailModel generateRefundProductFromItem(
            AchievementProductDetailModel productDetail,
            AfterSalesItemResp item,
            AfterSalesOrderDetailResp response,
            MqOrderRefundInfoModel refundTask) {

        Long achievementId = productDetail.getAchievementId();

        // 计算退款比例：refundAmount/paidAmount，保留6位小数，四舍五入
        BigDecimal refundAmount = item.getRefundAmount();
        BigDecimal paidAmount = item.getPaidAmount();
        BigDecimal multiplier;

        if (refundAmount == null || paidAmount == null || paidAmount.compareTo(BigDecimal.ZERO) == 0) {
            log.warn("业绩ID {} 退款金额或已付金额为空或为0，使用默认退款比例1", achievementId);
            multiplier = BigDecimal.ONE;
        } else {
            multiplier = refundAmount.divide(paidAmount, 6, RoundingMode.HALF_UP);
            log.info("业绩ID {} 计算退款比例：{}/{} = {}", achievementId, refundAmount, paidAmount, multiplier);
        }

        // 获取商务月信息
        Date defaultTime = response.getAfterSalesOrderResp().getApplyTime();
        Date businessMonthTime = defaultTime;

        Date statisticsTime=response.getAfterSalesOrderResp().getApplyTime();

        // 如果是转款，尝试从mq_order_payment_info表获取create_time
        if (OrderSaleTypeEnum.TRANSFER.getType().equals(refundTask.getSaleType())) {
            LambdaQueryWrapper<MqOrderPaymentInfoModel> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(MqOrderPaymentInfoModel::getOrderId, refundTask.getTransferInOrderId())
                    .eq(MqOrderPaymentInfoModel::getCalculateType, CalculateTypeEnum.PAYMENT.getCode())
                    .eq(MqOrderPaymentInfoModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                    .last("limit 1");
            MqOrderPaymentInfoModel paymentInfo = mqOrderPaymentInfoRepository.getOne(queryWrapper);
            if (paymentInfo != null) {
                businessMonthTime = paymentInfo.getCreateTime();
                statisticsTime=paymentInfo.getCreateTime();
            }
        }

        BusinessMonthModel businessMonth = businessMonthService.getMonthInfo(businessMonthTime);

        // 调用AchievementRefundCommonHandler的createRefundProduct方法
        return refundCommonHandler.createRefundProduct(
                productDetail,
                businessMonth,
                statisticsTime,
                refundTask,
                multiplier);
    }

    /**
     * 生成spec级别的退款业绩
     *
     * @param refundProductList  业绩商品明细退款列表
     * @param refundCategoryList 业绩分类明细退款列表
     * @param refundSpecList     业绩规格明细退款列表
     * @param productDetail      业绩商品明细
     * @param item               售后商品信息
     * @param response           售后订单详情响应
     * @param refundTask         退款任务数据
     */
    private void generateSpecLevelRefund(List<AchievementProductDetailModel> refundProductList,
            List<AchievementCategoryDetailModel> refundCategoryList,
            List<AchievementSpecDetailModel> refundSpecList,
            AchievementProductDetailModel productDetail,
            AfterSalesItemResp item,
            AfterSalesOrderDetailResp response,
            MqOrderRefundInfoModel refundTask) {

        Long achievementId = productDetail.getAchievementId();
        log.info("开始生成业绩ID {} 的spec级别退款业绩", achievementId);

        // 1. 构建售后规格响应Map
        Map<Long, AfterSalesItemSpecResp> specRespMap = buildSpecRespMap(item);

        // 2. 查询分类明细和规格明细数据
        List<AchievementCategoryDetailModel> categoryDetailList = queryCategoryDetailList(achievementId);
        List<AchievementSpecDetailModel> specDetailList = querySpecDetailList(categoryDetailList);

        // 3. 按spec_id分组规格明细数据
        Map<Long, List<AchievementSpecDetailModel>> specDetailMap = groupSpecDetailBySpecId(specDetailList);

        // 4. 生成退款商品明细记录
        AchievementProductDetailModel refundProduct = generateRefundProductFromItem(
                productDetail, item, response, refundTask);
        refundProductList.add(refundProduct);

        // 5. 生成退款分类明细记录，并获取分类ID映射关系
        Map<Long, Long> categoryIdMap = generateSpecLevelRefundCategories(refundCategoryList, categoryDetailList,
                refundProduct.getAchievementId(), response, specRespMap, specDetailList);

        // 6. 生成退款规格明细记录
        generateSpecLevelRefundSpecs(refundSpecList, specDetailMap, categoryIdMap,
                response, specRespMap, achievementId);

        log.info("完成业绩ID {} 的spec级别退款业绩生成", achievementId);
    }

    /**
     * 构建售后规格响应Map
     */
    private Map<Long, AfterSalesItemSpecResp> buildSpecRespMap(AfterSalesItemResp item) {
        Map<Long, AfterSalesItemSpecResp> specRespMap = Optional.ofNullable(item.getAfterSalesItemSpecResps())
                .orElse(Collections.emptyList())
                .stream()
                .filter(spec -> spec.getProductSpecId() != null)
                .collect(Collectors.toMap(
                        AfterSalesItemSpecResp::getProductSpecId,
                        Function.identity(),
                        (existing, replacement) -> existing,
                        HashMap::new));
        return specRespMap;
    }

    /**
     * 查询分类明细列表
     */
    private List<AchievementCategoryDetailModel> queryCategoryDetailList(Long achievementId) {
        LambdaQueryWrapper<AchievementCategoryDetailModel> categoryQueryWrapper = new LambdaQueryWrapper<>();
        categoryQueryWrapper.eq(AchievementCategoryDetailModel::getAchievementId, achievementId)
                .eq(AchievementCategoryDetailModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode());

        List<AchievementCategoryDetailModel> categoryDetailList = achievementCategoryDetailRepository
                .list(categoryQueryWrapper);
        log.info("业绩ID {} 查询到 {} 条分类明细记录", achievementId, categoryDetailList.size());
        return categoryDetailList;
    }

    /**
     * 查询规格明细列表
     */
    private List<AchievementSpecDetailModel> querySpecDetailList(
            List<AchievementCategoryDetailModel> categoryDetailList) {
        List<Long> categoryIds = categoryDetailList.stream()
                .map(AchievementCategoryDetailModel::getAchievementCategoryId)
                .collect(Collectors.toList());

        List<AchievementSpecDetailModel> specDetailList = new ArrayList<>();
        if (!categoryIds.isEmpty()) {
            LambdaQueryWrapper<AchievementSpecDetailModel> specQueryWrapper = new LambdaQueryWrapper<>();
            specQueryWrapper.in(AchievementSpecDetailModel::getAchievementCategoryId, categoryIds)
                    .eq(AchievementSpecDetailModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode());

            specDetailList = achievementSpecDetailRepository.list(specQueryWrapper);
        }
        return specDetailList;
    }

    /**
     * 按spec_id分组规格明细数据
     */
    private Map<Long, List<AchievementSpecDetailModel>> groupSpecDetailBySpecId(
            List<AchievementSpecDetailModel> specDetailList) {
        Map<Long, List<AchievementSpecDetailModel>> specDetailMap = specDetailList.stream()
                .filter(spec -> spec.getSpecId() != null)
                .collect(Collectors.groupingBy(AchievementSpecDetailModel::getSpecId));
        return specDetailMap;
    }

    /**
     * 生成spec级别的退款分类明细记录
     *
     * @return 原始分类ID到新分类ID的映射关系
     */
    private Map<Long, Long> generateSpecLevelRefundCategories(
            List<AchievementCategoryDetailModel> refundCategoryList,
            List<AchievementCategoryDetailModel> categoryDetailList,
            Long newAchievementId,
            AfterSalesOrderDetailResp response,
            Map<Long, AfterSalesItemSpecResp> specRespMap,
            List<AchievementSpecDetailModel> allSpecDetailList) {

        log.info("开始生成 {} 条分类明细的退款记录", categoryDetailList.size());

        Map<Long, Long> categoryIdMap = new HashMap<>();

        for (AchievementCategoryDetailModel originalCategory : categoryDetailList) {
            // 根据分类对应的spec_id计算退款比例
            BigDecimal multiplier = calculateCategoryRefundMultiplier(originalCategory, specRespMap, allSpecDetailList);

            // 创建分类明细退款记录
            AchievementCategoryDetailModel refundCategory = refundCommonHandler.createRefundCategory(
                    originalCategory,
                    newAchievementId,
                    response.getAfterSalesOrderResp().getApplyTime(),
                    multiplier);

            refundCategoryList.add(refundCategory);

            // 建立原始分类ID到新分类ID的映射关系
            categoryIdMap.put(originalCategory.getAchievementCategoryId(),
                    refundCategory.getAchievementCategoryId());

            log.info("分类ID {} 创建退款记录成功，新分类ID: {}，退款比例: {}",
                    originalCategory.getAchievementCategoryId(),
                    refundCategory.getAchievementCategoryId(),
                    multiplier);
        }

        return categoryIdMap;
    }

    /**
     * 计算分类明细的退款比例
     */
    private BigDecimal calculateCategoryRefundMultiplier(
            AchievementCategoryDetailModel originalCategory,
            Map<Long, AfterSalesItemSpecResp> specRespMap,
            List<AchievementSpecDetailModel> allSpecDetailList) {

        // 从已有的规格明细列表中筛选出该分类下的规格明细
        List<AchievementSpecDetailModel> specList = allSpecDetailList.stream()
                .filter(spec -> originalCategory.getAchievementCategoryId().equals(spec.getAchievementCategoryId()))
                .collect(Collectors.toList());

        if (specList.isEmpty()) {
            return BigDecimal.ONE;
        }

        // 计算该分类下所有规格的退款比例的加权平均值
        BigDecimal totalRefundAmount = BigDecimal.ZERO;
        BigDecimal totalPaidAmount = BigDecimal.ZERO;

        for (AchievementSpecDetailModel spec : specList) {
            AfterSalesItemSpecResp specResp = specRespMap.get(spec.getSpecId());
            if (specResp != null && specResp.getRefundAmount() != null && specResp.getPaidAmount() != null) {
                totalRefundAmount = totalRefundAmount.add(specResp.getRefundAmount());
                totalPaidAmount = totalPaidAmount.add(specResp.getPaidAmount());
            }
        }

        if (totalPaidAmount.compareTo(BigDecimal.ZERO) == 0) {
            log.warn("分类ID {} 下所有规格的总已付金额为0，使用默认退款比例1", originalCategory.getAchievementCategoryId());
            return BigDecimal.ONE;
        }

        BigDecimal multiplier = totalRefundAmount.divide(totalPaidAmount, 6, RoundingMode.HALF_UP);
        log.info("分类ID {} 计算退款比例：{}/{} = {}",
                originalCategory.getAchievementCategoryId(), totalRefundAmount, totalPaidAmount, multiplier);

        return multiplier;
    }

    /**
     * 生成spec级别的退款规格明细记录
     */
    private void generateSpecLevelRefundSpecs(
            List<AchievementSpecDetailModel> refundSpecList,
            Map<Long, List<AchievementSpecDetailModel>> specDetailMap,
            Map<Long, Long> categoryIdMap,
            AfterSalesOrderDetailResp response,
            Map<Long, AfterSalesItemSpecResp> specRespMap,
            Long achievementId) {

        log.info("开始生成业绩ID {} 的规格明细退款记录", achievementId);

        // 遍历规格明细分组
        for (Map.Entry<Long, List<AchievementSpecDetailModel>> entry : specDetailMap.entrySet()) {
            Long specId = entry.getKey();
            List<AchievementSpecDetailModel> specList = entry.getValue();

            // 根据spec_id匹配出AfterSalesItemSpecResp对象
            AfterSalesItemSpecResp matchedSpecResp = specRespMap.get(specId);

            if (matchedSpecResp == null) {
                continue;
            }
            // 计算退款比例
            BigDecimal refundAmount = matchedSpecResp.getRefundAmount();
            BigDecimal paidAmount = matchedSpecResp.getPaidAmount();
            BigDecimal multiplier = null;
            if (refundAmount == null || paidAmount == null || paidAmount.compareTo(BigDecimal.ZERO) == 0) {
                multiplier = BigDecimal.ONE;
            } else {
                multiplier = refundAmount.divide(paidAmount, 6, RoundingMode.HALF_UP);
            }

            // 为每个规格明细记录生成退款记录
            for (AchievementSpecDetailModel originalSpec : specList) {
                // 获取对应的新分类ID
                Long newAchievementCategoryId = categoryIdMap.get(originalSpec.getAchievementCategoryId());

                if (newAchievementCategoryId == null) {
                    log.warn("业绩ID {} 规格ID {} 未找到对应的分类明细退款记录，跳过处理", achievementId, specId);
                    continue;
                }

                // 调用createRefundSpec方法创建退款记录
                AchievementSpecDetailModel refundSpec = refundCommonHandler.createRefundSpec(
                        originalSpec,
                        newAchievementCategoryId,
                        response.getAfterSalesOrderResp().getApplyTime(),
                        multiplier);

                refundSpecList.add(refundSpec);

                log.info("业绩ID {} 规格ID {} 创建退款记录成功，退款比例: {}",
                        achievementId, specId, multiplier);
            }

        }
    }
}
