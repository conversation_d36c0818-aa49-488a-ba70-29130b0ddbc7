package com.xmd.achievement.dao.repository.impl;

import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xmd.achievement.dao.entity.MqOrderPaymentInfoModel;
import com.xmd.achievement.dao.mapper.MqOrderPaymentInfoMapper;
import com.xmd.achievement.dao.repository.IMqOrderPaymentInfoRepository;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-26
 */
@Service
@Slf4j
public class MqOrderPaymentInfoRepositoryImpl extends ServiceImpl<MqOrderPaymentInfoMapper,MqOrderPaymentInfoModel> implements IMqOrderPaymentInfoRepository {

@Resource
private MqOrderPaymentInfoMapper mqOrderPaymentInfoMapper;

    @Override
    public List<MqOrderPaymentInfoModel> selectListByOrderIdAndInstallment(Long orderId, List<Integer> installmentNumList,Long productId, String orderProductId) {
        LambdaQueryWrapper<MqOrderPaymentInfoModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MqOrderPaymentInfoModel::getOrderId,orderId);
        queryWrapper.in(CollectionUtils.isNotEmpty(installmentNumList),MqOrderPaymentInfoModel::getInstallmentNum,installmentNumList);
        queryWrapper.eq(MqOrderPaymentInfoModel::getDeleteFlag,0);
        queryWrapper.eq(MqOrderPaymentInfoModel::getInstallmentStatus,2);
        queryWrapper.eq(null != productId, MqOrderPaymentInfoModel::getProductId,productId);
        queryWrapper.eq(StringUtils.isNotEmpty(orderProductId) , MqOrderPaymentInfoModel::getOrderProductId,orderProductId);
        return this.list(queryWrapper);
    }

    @Override
    public MqOrderPaymentInfoModel selectCheckOne(Long orderId, Long productId, String orderProductId, Integer installment) {
        LambdaQueryWrapper<MqOrderPaymentInfoModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MqOrderPaymentInfoModel::getOrderId,orderId);
        queryWrapper.eq(StringUtils.isNotEmpty(orderProductId), MqOrderPaymentInfoModel::getOrderProductId,orderProductId);
        queryWrapper.eq(MqOrderPaymentInfoModel::getInstallmentStatus,installment);
        queryWrapper.eq(MqOrderPaymentInfoModel::getDeleteFlag,0);
        queryWrapper.eq(null != productId, MqOrderPaymentInfoModel::getProductId,productId);
        queryWrapper.last("limit 1");
        return this.getOne(queryWrapper);
    }

    @Override
    public void updateByOrderId(Long orderId, Integer calculateType) {
        LambdaUpdateWrapper<MqOrderPaymentInfoModel> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(MqOrderPaymentInfoModel::getTaskStatus,2);
        updateWrapper.eq(MqOrderPaymentInfoModel::getOrderId,orderId);
        updateWrapper.eq(MqOrderPaymentInfoModel::getCalculateType,calculateType);
        this.update(updateWrapper);
    }

    @Override
    public List<MqOrderPaymentInfoModel> selectByOrderIdAndCalculateType(Long orderId, Integer calculateType) {
        LambdaQueryWrapper<MqOrderPaymentInfoModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MqOrderPaymentInfoModel::getOrderId,orderId);
        queryWrapper.eq(MqOrderPaymentInfoModel::getCalculateType,calculateType);
        return this.list(queryWrapper);
    }

    @Override
    public List<MqOrderPaymentInfoModel> selectListByOrderId(Long orderId) {
        LambdaQueryWrapper<MqOrderPaymentInfoModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MqOrderPaymentInfoModel::getOrderId,orderId);
        queryWrapper.eq(MqOrderPaymentInfoModel::getDeleteFlag,0);
        return mqOrderPaymentInfoMapper.selectList(queryWrapper);
    }
}