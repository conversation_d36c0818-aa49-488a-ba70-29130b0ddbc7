package com.xmd.achievement.handler.achievement;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.xmd.achievement.dao.entity.AchievementSpecDetailModel;
import com.xmd.achievement.rpc.entity.dto.ProductDiscountRulesListForAchievementResponse;
import com.xmd.achievement.support.constant.NumberConstants;
import com.xmd.achievement.support.constant.enums.LinkManTypeEnum;
import com.xmd.achievement.support.constant.enums.OrderTypeEnum;
import com.xmd.achievement.util.enums.SaleTypeEnum;
import com.xmd.achievement.web.config.HalfCommissionConfig;
import com.xmd.achievement.web.config.ProductAchievementConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;

/**
 *商代提成业绩<br/>
 * ●当岗位为商务代表/商务主管时，商代提成业绩<br/>
 * ○在同一笔订单中，两个商品规格进行了组合规格业绩设置，则这两个规格则提成比例都按照组合规格相应类型的政策性成本取值；<br/>
 * ○在同一笔订单中，商品规格未进行组合规格业绩设置，则政策性成本按该规格的政策性成本计算：<br/>
 * ■普通订单<br/>
 * ●售卖类型为新开，商代提成业绩=净现金x（1-商代政策性成新开）;<br/>
 * ●售卖类型为续费，商代提成业绩=净现金x（1-商代政策性成本续费）;<br/>
 * ●售卖类型为升级，商代提成业绩=净现金x（1-商代政策性成本升级）;<br/>
 * ●售卖类型为另购，商代提成业绩=净现金x（1-商代政策性成本另购）;<br/>
 * ■折扣订单<br/>
 * ●商品折扣≥分司总监基础折扣，<br/>
 *  ○售卖类型为新开，商代提成业绩=净现金x（1-商代政策性成新开）;<br/>
 *  ○售卖类型为续费，商代提成业绩=净现金x（1-商代政策性成本续费）;<br/>
 *  ○售卖类型为升级，商代提成业绩=净现金x（1-商代政策性成本升级）;<br/>
 *  ○售卖类型为另购，商代提成业绩=净现金x（1-商代政策性成本另购）;<br/>
 * ●提成减半线≤商品折扣＜分司总监基础折扣<br/>
 *  ○售卖类型为新开，商代提成业绩=净现金x（1-商代政策性成新开）x50%;<br/>
 *  ○售卖类型为续费，商代提成业绩=净现金x（1-商代政策性成本续费）x50%;<br/>
 *  ○售卖类型为升级，商代提成业绩=净现金x（1-商代政策性成本升级）x50%;<br/>
 *  ○售卖类型为另购，商代提成业绩=净现金x（1-商代政策性成本另购）x50%;<br/>
 * ●商品折扣＜提成减半线,商代提成业绩=0;<br/>
 * <AUTHOR>
 * @date: 2024/12/20 14:51
 */
@Slf4j
@Service
public class SpecCalculateSalesCommissionAmountHandler implements CalculateAmountHandler {
    @Resource
    private PolicyCostCalculator policyCostCalculator;
    @Resource
    private HalfCommissionConfig halfCommissionConfig;
    @Resource
    private ProductAchievementConfig productAchievementConfig;

    @Value("${prepaid-charge-category.id}")
    private Long categoryId;

    @Override
    public void calculate(CalculateFactInfo factInfo) {
        AchievementSpecDetailModel spec = factInfo.getSpec();
        try {
            //计算政策性成本
            policyCostCalculator.calculatePolicyCosts(factInfo);
            //计算特殊商品的政策性成本
            policyCostCalculator.calculateSpecialProductPolicyCosts(factInfo);
            //商代提成成本
            BigDecimal policyCost = factInfo.getCalculatePolicyCosts().get(spec.getSpecId());
            //净现金
            BigDecimal netCash = spec.getNetCash();
            // 预存费的特殊逻辑 规格提成业绩=实付金额*（1-政策性成本）
            if (factInfo.getSpec().getProductCategoryId().equals(categoryId) && factInfo.getOrderSaleType().equals(SaleTypeEnum.NEW_OPEN.getType())) {
                //先判断0提成线
                if (factInfo.getZeroNetCash()) {
                    netCash = BigDecimal.ZERO;
                 } else {
                    //判断是否分单 分单净现金 = 实付 * 0.5
                    boolean splitOrder = factInfo.getOrderInfo().getOrderContactResponseList().stream().anyMatch(c -> Objects.equals(c.getType(), LinkManTypeEnum.SUB_SALE.getType()));
                    netCash = splitOrder ? spec.getPaidAmount().multiply(BigDecimal.valueOf(NumberConstants.DOUBLE_VALUE_0_5)) : spec.getPaidAmount();
                }
            }
            BigDecimal amount;
            if (spec.getPaidAmount().compareTo(BigDecimal.ZERO) == 0) {
                amount = BigDecimal.ZERO;
                spec.setAgentCommAchv(amount);
                return;
            }
            // 2025-08-12 新加逻辑，广告通商品另购中如果有第一次购买，按照新开计算，提成用实付金额计算
            List<Long> newOpenSpecIdList = factInfo.getNewOpenSpecIdList();
            if (factInfo.getOrderSaleType().equals(SaleTypeEnum.ADDITIONAL_PURCHASE.getType()) && ObjectUtil.isNotEmpty(newOpenSpecIdList) && newOpenSpecIdList.contains(spec.getSpecId())) {
                netCash = spec.getPaidAmount();
            }
            //普通订单
            if (OrderTypeEnum.NORMAL.getType().equals(factInfo.getOrderType())) {
                //获取佣金金额
                amount = NumberUtil.mul(netCash, new BigDecimal(NumberConstants.INTEGER_VALUE_1).subtract(policyCost)).setScale(NumberConstants.INTEGER_VALUE_3, RoundingMode.HALF_UP);
                spec.setAgentCommAchv(amount);
            } else if (OrderTypeEnum.DISCOUNT.getType().equals(factInfo.getOrderType())) {
                //获取当前折扣率
                BigDecimal currentDiscountRate = factInfo.getProduct().getDiscountRate();

                //currentDiscountRate乘以10，保留小数位
                currentDiscountRate = currentDiscountRate.multiply(new BigDecimal(NumberConstants.INTEGER_VALUE_10)).setScale(NumberConstants.INTEGER_VALUE_3, RoundingMode.HALF_UP);

                //获取折扣规则
                ProductDiscountRulesListForAchievementResponse discountRule = factInfo.getProductDiscountRulesList().stream().filter(dr -> dr.getProductId().equals(factInfo.getProduct().getProductId())).findFirst().orElse(null);
                //获取分总折扣
                BigDecimal branchDiscount = null == discountRule ? BigDecimal.ZERO : discountRule.getBranchDiscount();
                //获取半佣金线
                BigDecimal halfCommissionLine = null == discountRule ? BigDecimal.ZERO : discountRule.getHalfCommissionLine();
                //获取佣金金额
                if (currentDiscountRate.compareTo(branchDiscount) >= 0) {
                    amount = NumberUtil.mul(netCash, new BigDecimal(NumberConstants.INTEGER_VALUE_1).subtract(policyCost)).setScale(NumberConstants.INTEGER_VALUE_3, RoundingMode.HALF_UP);
                    spec.setAgentCommAchv(amount);
                } else if (currentDiscountRate.compareTo(halfCommissionLine) >= 0 && currentDiscountRate.compareTo(branchDiscount) < 0) {
                    // 广告通、技服提成受半佣金线控制
                    List<Long> productIds = halfCommissionConfig.getProductIds();
                    if (CollUtil.isNotEmpty(productIds) && productIds.contains(spec.getProductId())) {
                        List<Long> categoryIds = halfCommissionConfig.getCategoryIds();
                        if (CollUtil.isNotEmpty(categoryIds) && categoryIds.contains(spec.getCategoryId())) {
                            amount = NumberUtil.mul(netCash, BigDecimal.ONE.subtract(policyCost), new BigDecimal(NumberConstants.DOUBLE_VALUE_0_5)).setScale(NumberConstants.INTEGER_VALUE_3, RoundingMode.HALF_UP);
                        } else {
                            amount = NumberUtil.mul(netCash, BigDecimal.ONE.subtract(policyCost)).setScale(NumberConstants.INTEGER_VALUE_3, RoundingMode.HALF_UP);
                        }
                    } else {
                        // 正常商品受半佣金线控制
                        amount = NumberUtil.mul(netCash, BigDecimal.ONE.subtract(policyCost), new BigDecimal(NumberConstants.DOUBLE_VALUE_0_5)).setScale(NumberConstants.INTEGER_VALUE_3, RoundingMode.HALF_UP);
                    }
                    spec.setAgentCommAchv(amount);
                } else if (currentDiscountRate.compareTo(halfCommissionLine) < 0) {
                    spec.setAgentCommAchv(BigDecimal.ZERO);
                }
            }
        } finally {
            log.warn("商代提成业绩 规格id:{},参数:{}", factInfo.getSpec().getSpecId(), JSON.toJSONString(factInfo));
        }
    }
}
