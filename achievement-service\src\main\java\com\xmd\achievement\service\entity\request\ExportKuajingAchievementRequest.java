package com.xmd.achievement.service.entity.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/01/10/17:14
 * @since 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ExportKuajingAchievementRequest extends PageRequest implements Serializable {
    @Schema(description = "签单开始时间", example = "2024-01-01T12:00:00", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date signTimeStrat;

    @Schema(description = "签单结束时间", example = "2024-01-01T12:00:00", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date signTimeEnd;

}
