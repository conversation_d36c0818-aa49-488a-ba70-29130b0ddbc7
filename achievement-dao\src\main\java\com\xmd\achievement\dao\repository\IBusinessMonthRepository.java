package com.xmd.achievement.dao.repository;

import com.xmd.achievement.dao.entity.BusinessMonthModel;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 商务月表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
public interface IBusinessMonthRepository extends IService<BusinessMonthModel> {

     List<BusinessMonthModel> selectAllBusinessMonthList();

     List<BusinessMonthModel> selectBusinessMonthByMonthList(List<String> monthList);

     BusinessMonthModel selectBusinessMonthByDate(Date createTime);

     List<BusinessMonthModel> selectBusinessMonthByThreeMonth(String currentMonthStr);

     BusinessMonthModel selectBusinessMonthByCurrentDate(String currentMonthStr);

     BusinessMonthModel selectCurrentBusinessMonth();

     /**
      * 获取指定商务月的上一个商务月
      * @param currentMonth 当前商务月，格式 yyyy-MM
      * @return 上一个商务月信息
      */
     BusinessMonthModel selectPreviousBusinessMonth(String currentMonth);
}
