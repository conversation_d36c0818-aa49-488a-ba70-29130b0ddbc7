package com.xmd.achievement.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xmd.achievement.cache.constant.CacheConstant;
import com.xmd.achievement.dao.entity.SaleTaskAuditModel;
import com.xmd.achievement.dao.entity.SaleTaskModel;
import com.xmd.achievement.dao.entity.SaleTaskOpeLogModel;
import com.xmd.achievement.dao.repository.ISaleTaskRepository;
import com.xmd.achievement.rpc.InnerService;
import com.xmd.achievement.rpc.ManagementService;
import com.xmd.achievement.rpc.entity.dto.*;
import com.xmd.achievement.service.IBusinessMonthService;
import com.xmd.achievement.service.IFreezeMonthErrorLogService;
import com.xmd.achievement.service.IOrganizationReportService;
import com.xmd.achievement.service.SaleTaskAuditService;
import com.xmd.achievement.service.SaleTaskOpeLogService;
import com.xmd.achievement.service.SaleTaskService;
import com.xmd.achievement.service.entity.dto.ImportExcelExceptionDTO;
import com.xmd.achievement.service.entity.request.*;
import com.xmd.achievement.service.entity.response.OpenSaleTaskResponse;
import com.xmd.achievement.service.entity.response.QuerySaleTaskListResponse;
import com.xmd.achievement.service.entity.response.SaleTaskBranchResponse;
import com.xmd.achievement.service.entity.response.SaleTaskListInfoResponse;
import com.xmd.achievement.support.constant.NumberConstants;
import com.xmd.achievement.support.constant.enums.DataPermitEnum;
import com.xmd.achievement.support.constant.enums.DeleteFlagEnum;
import com.xmd.achievement.support.constant.enums.MarketCategoryTypeEnum;
import com.xmd.achievement.support.constant.enums.OrganizationTypeEnum;
import com.xmd.achievement.support.constant.exceptions.BusinessException;
import com.xmd.achievement.support.redis.RedisCache;
import com.xmd.achievement.util.enums.SaleTaskStatusEnum;
import com.xmd.achievement.web.entity.response.WebCodeMessageEnum;
import com.xmd.achievement.web.entity.response.WebResult;
import com.xmd.achievement.web.util.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.xmd.achievement.dao.entity.SaleTaskModel.ORG_TYPE_COMPANY;
import static com.xmd.achievement.dao.entity.SaleTaskModel.ORG_TYPE_DEPT;
import static com.xmd.achievement.util.constant.UtilConstant.REQUEST_SOURCE;
import static com.xmd.achievement.util.date.DateUtils.getPreviousMonth;
import static com.xmd.achievement.util.date.DateUtils.parseYearMonth;
import static com.xmd.achievement.util.enums.SaleTaskStatusEnum.*;
import static com.xmd.achievement.web.entity.response.WebCodeMessageEnum.REQUEST_PARAM_EXCEPTION;

@Service
@Slf4j
public class SaleTaskServiceImpl implements SaleTaskService {

    @Value("${sale-task.headquarters-name}")
    private String headquartersName;

    @Value("${sale-task.headquarters-org-id}")
    private Long headquartersOrgId;

    @Autowired
    private ISaleTaskRepository saleTaskRepository;

    @Autowired
    private SaleTaskAuditService saleTaskAuditService;

    @Autowired
    private SaleTaskOpeLogService saleTaskOpeLogService;

    @Autowired
    private ManagementService managementService;

    @Autowired
    private IOrganizationReportService organizationReportService;

    @Autowired
    private InnerService innerService;

    @Autowired
    private IFreezeMonthErrorLogService freezeMonthErrorLogService;

    @Autowired
    private IBusinessMonthService businessMonthService;

    @Resource
    private RedisCache redisCache;

    public static final String MONTH_REPORT_COMPANY_REDIS = "month_report_company";
    public static final String MONTH_REPORT_DEPT_REDIS = "month_report_dept";

    @Override
    public QuerySaleTaskListResponse queryList(QuerySaleTaskListRequest request) {
        QuerySaleTaskListResponse resp = new QuerySaleTaskListResponse();
        LambdaQueryWrapper<SaleTaskModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SaleTaskModel::getBusinessMonth, request.getBusinessMonth())
                .eq(SaleTaskModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode());
        UserLoginInfoDTO userInfo = UserContext.getCurrentUserInfo();
        UserDataPermissionsDTO permissionsDTO = userInfo.getUserDataPermissionsDTO();
        List<Long> noLeaderOrg = getNoLeaderDept(request.getBusinessMonth());
        // 总部权限查询所有
        if (isHeadQuartersRole(permissionsDTO)) {
            List<SaleTaskModel> models = saleTaskRepository.list(queryWrapper);
            if (CollectionUtils.isEmpty(models)) {
                return null;
            }
            Map<Long, List<SaleTaskModel>> taskGroup = models.stream().collect(Collectors.groupingBy(SaleTaskModel::getParentId));
            List<SaleTaskModel> headQuartersTasks = taskGroup.get(SaleTaskModel.ROOT_PARENT_ID);
            if (CollectionUtils.isEmpty(headQuartersTasks)) {
                return null;
            }
            SaleTaskModel headQuartersTask = headQuartersTasks.get(0);
            BeanUtils.copyProperties(headQuartersTask, resp);

            Long levelTwoParentId = headQuartersTask.getId();
            List<SaleTaskModel> companyTasks = taskGroup.get(levelTwoParentId);
            //过滤分公司任务状态
            if (CollUtil.isNotEmpty(companyTasks) && Objects.nonNull(request.getBranchStatus())) {
                companyTasks = companyTasks.stream().filter(task -> Objects.equals(request.getBranchStatus(), task.getTaskStatus()))
                        .collect(Collectors.toList());
            }
            if (CollectionUtils.isEmpty(companyTasks)) {
                return resp;
            }
            assembleChildren(resp, companyTasks);

            List<QuerySaleTaskListResponse> removeList = Lists.newArrayList();
            for (QuerySaleTaskListResponse companyTask : resp.getChildren()) {
                List<SaleTaskModel> thirdTasks = taskGroup.get(companyTask.getId());

                //过滤第三级机构审核状态，如果为空则删除对应上级机构
                if (Objects.nonNull(request.getReviewStatus())) {
                    if (CollUtil.isEmpty(thirdTasks)) {
                        removeList.add(companyTask);
                        continue;
                    } else {
                        thirdTasks = thirdTasks.stream().filter(task -> Objects.equals(request.getReviewStatus(), task.getTaskStatus())).collect(Collectors.toList());
                        if (CollUtil.isEmpty(thirdTasks)) {
                            removeList.add(companyTask);
                            continue;
                        }
                    }
                }

                assembleChildren(companyTask, thirdTasks);
                //如果分公司下属机构为空则跳出循环
                if (CollectionUtils.isEmpty(companyTask.getChildren())) {
                    continue;
                }

                for (QuerySaleTaskListResponse departmentTask : companyTask.getChildren()) {
                    assembleChildren(departmentTask, taskGroup.get(departmentTask.getId()));
                }
            }

            resp.getChildren().removeAll(removeList);
            setNoLeaderValue(resp, noLeaderOrg);
            return resp;
        }

        // 非总部权限
        Long currentOrgId = getCurrentOrgId(userInfo);
        queryWrapper.eq(SaleTaskModel::getOrgId, currentOrgId);
        SaleTaskModel saleTaskModel = saleTaskRepository.getOne(queryWrapper);
        if (Objects.isNull(saleTaskModel)) {
            return null;
        }
        BeanUtils.copyProperties(saleTaskModel, resp);


        LambdaQueryWrapper<SaleTaskModel> queryWrapper2 = new LambdaQueryWrapper<>();
        queryWrapper2.eq(SaleTaskModel::getParentId, saleTaskModel.getId());
        List<SaleTaskModel> models = saleTaskRepository.list(queryWrapper2);
        if (CollectionUtils.isEmpty(models)) {
            setNoLeaderValue(resp, noLeaderOrg);
            return resp;
        }

        assembleChildren(resp, models);

        List<Long> parenIds = models.stream().map(SaleTaskModel::getId).collect(Collectors.toList());
        LambdaQueryWrapper<SaleTaskModel> queryWrapper3 = new LambdaQueryWrapper<>();
        queryWrapper3.in(SaleTaskModel::getParentId, parenIds);
        List<SaleTaskModel> businessGroupTasks = saleTaskRepository.list(queryWrapper3);
        if (CollectionUtils.isEmpty(businessGroupTasks)) {
            return resp;
        }

        Map<Long, List<SaleTaskModel>> taskGroup = businessGroupTasks.stream().collect(Collectors.groupingBy(SaleTaskModel::getParentId));
        for (QuerySaleTaskListResponse task : resp.getChildren()) {
            assembleChildren(task, taskGroup.get(task.getId()));
        }
        setNoLeaderValue(resp, noLeaderOrg);
        return resp;
    }

    public void setNoLeaderValue(QuerySaleTaskListResponse node, List<Long> orgIdList) {
        if (node == null || orgIdList == null) {
            return;
        }

        // 判断当前节点的 orgId 是否在列表中
        if (node.getOrgId() != null && orgIdList.contains(node.getOrgId())) {
            node.setNoLeader(NumberConstants.INTEGER_VALUE_0);
        } else {
            node.setNoLeader(NumberConstants.INTEGER_VALUE_1);
        }

        // 递归处理子节点
        if (node.getChildren() != null && !node.getChildren().isEmpty()) {
            for (QuerySaleTaskListResponse child : node.getChildren()) {
                setNoLeaderValue(child, orgIdList);
            }
        }
    }

    private void assembleChildren(QuerySaleTaskListResponse resp, List<SaleTaskModel> taskModels) {
        if (CollectionUtils.isEmpty(taskModels)) {
            return;
        }

        List<QuerySaleTaskListResponse> companyTasks = taskModels.stream().map(m -> {
            QuerySaleTaskListResponse res = new QuerySaleTaskListResponse();
            BeanUtils.copyProperties(m, res);
            return res;
        }).collect(Collectors.toList());
        resp.setChildren(companyTasks);
    }

    @Override
    public List<QuerySaleTaskListResponse> getPreCommitInfo(String businessMonth) {
        //查询当前用户的数据权限
        List<OrgInfoNodeResponse> organizationRoleList = organizationReportService.getOrganizationRoleList();
        if (CollectionUtils.isEmpty(organizationRoleList)) {
            return Lists.newArrayList();
        }

        UserLoginInfoDTO userInfo = UserContext.getCurrentUserInfo();

        List<QuerySaleTaskListResponse> resp = Lists.newArrayListWithCapacity(NumberConstants.INTEGER_VALUE_50);
        QuerySaleTaskListResponse currentOrgTask = new QuerySaleTaskListResponse();
        currentOrgTask.setBusinessMonth(businessMonth);
        resp.add(currentOrgTask);

        // 根据登录人数据权限组装所属级别销售任务数据
        UserDataPermissionsDTO userPermission = userInfo.getUserDataPermissionsDTO();

        // 总部查询所有分公司
        if (isHeadQuartersRole(userPermission) || isCompanyRole(userPermission)) {
            OrgInfoNodeResponse orgInfoNodeResponse = organizationRoleList.get(0);
            currentOrgTask.setOrgId(orgInfoNodeResponse.getOrgId());
            currentOrgTask.setOrgName(orgInfoNodeResponse.getName());

            SaleTaskModel one = saleTaskRepository.lambdaQuery()
                    .eq(SaleTaskModel::getOrgId, currentOrgTask.getOrgId())
                    .eq(SaleTaskModel::getBusinessMonth, businessMonth).one();
            if (Objects.nonNull(one)) {
                currentOrgTask.setBasicTask(one.getBasicTask());
            }

            QueryLeveRelationResponse relationResponse = innerService.queryLeveRelation(orgInfoNodeResponse.getOrgId());
            //当前机构市场类别
            if (Objects.nonNull(relationResponse) && Objects.nonNull(relationResponse.getCurrentLeveOrgInfo())) {
                Integer marketCategoryId = relationResponse.getCurrentLeveOrgInfo().getMarketCategoryId();
                if (Objects.isNull(marketCategoryId)) {
                    currentOrgTask.setMarketClassification("");
                } else {
                    currentOrgTask.setMarketClassification(MarketCategoryTypeEnum.getNameByType(marketCategoryId));
                }
            }
            //下一级机构市场类别
            if (Objects.nonNull(relationResponse) && Objects.nonNull(relationResponse.getLowerLeveOrgInfos())) {
                List<Long> orgIdList = relationResponse.getLowerLeveOrgInfos().stream().map(LowerLeveRelationResponse::getOrgId)
                        .map(Long::parseLong).distinct().collect(Collectors.toList());

                List<SaleTaskModel> list = saleTaskRepository.lambdaQuery().in(SaleTaskModel::getOrgId, orgIdList)
                        .eq(SaleTaskModel::getBusinessMonth, businessMonth).list();
                //机构基本任务map
                Map<Long, BigDecimal> basicTaskMap = CollUtil.isEmpty(list) ? Maps.newHashMap()
                        : list.stream().collect(Collectors.toMap(SaleTaskModel::getOrgId, SaleTaskModel::getBasicTask));

                List<OrgInfoNodeResponse> childOrgInfoNodeRespList = orgInfoNodeResponse.getChildOrgInfoNodeRespList();
                if (CollUtil.isEmpty(childOrgInfoNodeRespList)) {
                    return resp;
                }
                //真正的商务机构，过滤了commerceFlag为0的商务机构
                List<Long> orgCommerceFlagList = childOrgInfoNodeRespList.stream().map(OrgInfoNodeResponse::getOrgId).collect(Collectors.toList());

                relationResponse.getLowerLeveOrgInfos().forEach(child -> {
                    Long orgId = Convert.toLong(child.getOrgId());
                    if (orgCommerceFlagList.contains(orgId)) {
                        QuerySaleTaskListResponse childTask = new QuerySaleTaskListResponse();
                        childTask.setOrgId(orgId);
                        childTask.setOrgName(child.getName());
                        childTask.setMarketClassification("");
                        childTask.setBasicTask(basicTaskMap.getOrDefault(childTask.getOrgId(), null));
                        Optional.ofNullable(child.getMarketCategoryId()).ifPresent(categoryId ->
                                childTask.setMarketClassification(MarketCategoryTypeEnum.getNameByType(categoryId)));
                        resp.add(childTask);
                    }
                });
            }
        } else if (isCareerRole(userPermission) || isDeptRole(userPermission)) {
            OrgInfoNodeResponse orgInfoNodeResponse = organizationRoleList.get(0);
            currentOrgTask.setOrgId(orgInfoNodeResponse.getOrgId());
            currentOrgTask.setOrgName(orgInfoNodeResponse.getName());
        }

        // 分司及事业部销售任务已下发，直接查询获取当前登录人所属机构的销售任务
        LambdaQueryWrapper<SaleTaskModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SaleTaskModel::getBusinessMonth, businessMonth);
        queryWrapper.eq(SaleTaskModel::getOrgId, currentOrgTask.getOrgId());

        Optional.ofNullable(saleTaskRepository.getOne(queryWrapper)).ifPresent(model -> {
            BeanUtils.copyProperties(model, currentOrgTask);
        });
        return resp;
    }

    private boolean isHeadQuartersRole(UserDataPermissionsDTO userPermission) {
        return DataPermitEnum.ALL.getCode().equals(userPermission.getDataPermissionsType());
    }

    private boolean isCompanyRole(UserDataPermissionsDTO userPermission) {
        return DataPermitEnum.COMPANY.getCode().equals(userPermission.getDataPermissionsType());
    }

    private boolean isCareerRole(UserDataPermissionsDTO userPermission) {
        return DataPermitEnum.CAREER.getCode().equals(userPermission.getDataPermissionsType());
    }

    private boolean isDeptRole(UserDataPermissionsDTO userPermission) {
        return DataPermitEnum.DEPT.getCode().equals(userPermission.getDataPermissionsType());
    }

    private Long getCurrentOrgId(UserLoginInfoDTO userInfo) {
        UserDataPermissionsDTO userPermission = userInfo.getUserDataPermissionsDTO();
        OrgPathInfoDTO orgPathInfoDTO = userInfo.getOrgPathInfoDTO();
        if (isHeadQuartersRole(userPermission)) {
            return getHeadQuartersOrgInfo().getLeft();
        }
        if (isCompanyRole(userPermission)) {
            return Objects.isNull(orgPathInfoDTO.getCompanyId()) ? 99999L : orgPathInfoDTO.getCompanyId();
        }
        if (isCareerRole(userPermission)) {
            return orgPathInfoDTO.getCareerId();
        }
        return orgPathInfoDTO.getDeptId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public WebResult<Void> taskCommit(SaleTaskCommitRequest tasks) {
        List<SaleTaskEditRequest> taskList = tasks.getTasks();
        String businessMonth = tasks.getBusinessMonth();
        if (CollectionUtils.isEmpty(taskList)) {
            return WebResult.success();
        }
        if (StringUtils.isNotBlank(businessMonth)&& businessMonthService.isMonthFrozen(businessMonth)) {
            freezeMonthErrorLogService.add(businessMonthService.getMonthInfoByBusinessMonth(businessMonth), taskList);
            throw new BusinessException(WebCodeMessageEnum.BUSINESS_MONTH_FROZEN_ERROR.getMsg());
        }

        UserLoginInfoDTO userInfo = UserContext.getCurrentUserInfo();
        UserDataPermissionsDTO userPermission = userInfo.getUserDataPermissionsDTO();
        Integer permissionsType = userPermission.getDataPermissionsType();
        Long currentUserOrgId = getCurrentOrgId(userInfo);
        if (!DataPermitEnum.ALL.getCode().equals(permissionsType)
                && !DataPermitEnum.COMPANY.getCode().equals(permissionsType)
                && !DataPermitEnum.CAREER.getCode().equals(permissionsType)) {
            return WebResult.success();
        }

        // 1. 如果商务月有数据直接删除（只删除下级）
        taskList.remove(0);
        List<Long> orgIds = taskList.stream().map(SaleTaskEditRequest::getOrgId).filter(Objects::nonNull).collect(Collectors.toList());
        LambdaQueryWrapper<SaleTaskModel> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SaleTaskModel::getBusinessMonth, businessMonth);
        wrapper.in(SaleTaskModel::getOrgId, orgIds);
        List<SaleTaskModel> models = saleTaskRepository.list(wrapper);
        if (CollectionUtils.isNotEmpty(models)) {
            List<Long> secondIds = models.stream().map(SaleTaskModel::getId).collect(Collectors.toList());
            Long count = saleTaskRepository.lambdaQuery()
                    .in(SaleTaskModel::getParentId, secondIds)
                    .eq(SaleTaskModel::getBusinessMonth, businessMonth).count();
            if (count > 0) {
                if (DataPermitEnum.ALL.getCode().equals(permissionsType)) {
                    return WebResult.error(WebCodeMessageEnum.COMPANY_EXIST_COMMIT_SALE_TASK);
                } else {
                    return WebResult.error(WebCodeMessageEnum.DEPT_EXIST_COMMIT_SALE_TASK);
                }
            }
            saleTaskRepository.removeBatchByIds(models.stream().map(SaleTaskModel::getId).collect(Collectors.toList()));
        }

        // 2. 更新或插入汇总数据
        BigDecimal basicTaskTotal = new BigDecimal("0.0");

        // 汇总下级机构基本任务总额
        for (SaleTaskEditRequest saleTaskRequest : taskList) {
            basicTaskTotal = basicTaskTotal.add(saleTaskRequest.getBasicTask()).setScale(3, RoundingMode.HALF_UP);
        }

        // 2. 如果是总部权限
        LambdaQueryWrapper<SaleTaskModel> currentUserWrapper = new LambdaQueryWrapper<>();
        currentUserWrapper.eq(SaleTaskModel::getBusinessMonth, businessMonth);
        currentUserWrapper.eq(SaleTaskModel::getOrgId, currentUserOrgId);
        SaleTaskModel currentUserTask = saleTaskRepository.getOne(currentUserWrapper);

        if (DataPermitEnum.ALL.getCode().equals(permissionsType) && Objects.isNull(currentUserTask)) {
            currentUserTask = getSaleTaskModel(businessMonth, basicTaskTotal);
            saleTaskRepository.save(currentUserTask);
        }

        if (DataPermitEnum.ALL.getCode().equals(permissionsType)) {
            // 非首次提交任务，更新总部销售任务
            currentUserTask.setBasicTask(basicTaskTotal);
            //currentUserTask.setBranchOfficeTotal(basicTaskTotal);
            currentUserTask.setTaskStatus(SaleTaskStatusEnum.ISSUED.getKey());
            saleTaskRepository.updateById(currentUserTask);
        }

        // 如果不是总部权限，销售任务为空说明总部未下发
        if (!DataPermitEnum.ALL.getCode().equals(permissionsType) && Objects.isNull(currentUserTask)) {
            throw new BusinessException("销售任务未下发");
        }

        // 2.2 如果分司或事业部权限
        if (DataPermitEnum.COMPANY.getCode().equals(permissionsType) || DataPermitEnum.CAREER.getCode().equals(permissionsType)) {
            BigDecimal subtract = basicTaskTotal.subtract(currentUserTask.getDepartmentTotal()).setScale(3, RoundingMode.HALF_UP);
            //currentUserTask.setDepartmentTotal(basicTaskTotal);
            currentUserTask.setTaskStatus(SaleTaskStatusEnum.COMMITTED.getKey());

            // 还需要更新上级任务汇总
            List<SaleTaskModel> parentModels = Lists.newArrayList();
            //summaryData(currentUserTask.getParentId(), parentModels, subtract);
            parentModels.add(currentUserTask);
            saleTaskRepository.updateBatchById(parentModels);
        }

        // 3. 插入新销售任务数据
        List<SaleTaskModel> saleTaskModels = Lists.newArrayList();
        int orgType = getNextLevelOrgType(currentUserTask.getOrgType());
        for (SaleTaskEditRequest saleTaskReq : taskList) {
            SaleTaskModel saleTaskModel = new SaleTaskModel();
            BeanUtils.copyProperties(saleTaskReq, saleTaskModel);
            saleTaskModel.setOrgType(orgType);
            saleTaskModel.setTaskId(IdUtil.getSnowflakeNextId());
            saleTaskModel.setParentId(currentUserTask.getId());
            saleTaskModel.setBusinessMonth(businessMonth);
            saleTaskModel.setUpdateUserId(userInfo.getUserId());
            saleTaskModel.setUpdateUserName(userInfo.getName());
            if (DataPermitEnum.ALL.getCode().equals(permissionsType)) {
                saleTaskModel.setTaskStatus(UNCOMMITTED.getKey());
            } else {
                saleTaskModel.setTaskStatus(PENDING_APPROVAL.getKey());
            }
            saleTaskModels.add(saleTaskModel);
        }
        saleTaskRepository.saveBatch(saleTaskModels);

        // 4. 记录操作日志
        saleTaskOpeLogService.saveBatch(saleTaskModels, SaleTaskOpeLogModel.LOG_OPE_TYPE_ADD);

        return WebResult.success();
    }

    @NotNull
    private SaleTaskModel getSaleTaskModel(String businessMonth, BigDecimal basicTaskTotal) {
        SaleTaskModel currentUserTaskModel = new SaleTaskModel();
        // 首次提交任务，生成一条总部汇总的销售任务
        currentUserTaskModel.setTaskId(IdUtil.getSnowflakeNextId());
        currentUserTaskModel.setTaskStatus(SaleTaskStatusEnum.ISSUED.getKey());
        currentUserTaskModel.setBusinessMonth(businessMonth);
        Pair<Long, String> pair = getHeadQuartersOrgInfo();
        currentUserTaskModel.setOrgId(pair.getLeft());
        currentUserTaskModel.setOrgName(pair.getRight());
        currentUserTaskModel.setOrgType(SaleTaskModel.ORG_TYPE_HEAD);
        currentUserTaskModel.setBusinessMonth(businessMonth);
        currentUserTaskModel.setBasicTask(basicTaskTotal);
        //currentUserTaskModel.setBranchOfficeTotal(basicTaskTotal);
        return currentUserTaskModel;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void revoke(Long id) {
        if (Objects.isNull(id)) {
            return;
        }

        UserLoginInfoDTO userInfo = UserContext.getCurrentUserInfo();
        Long currentOrgId = getCurrentOrgId(userInfo);

        SaleTaskModel model = saleTaskRepository.getById(id);
        if (Objects.isNull(model)) {
            return;
        }
        
        if (businessMonthService.isMonthFrozen(model.getBusinessMonth())) {
            freezeMonthErrorLogService.add(businessMonthService.getMonthInfoByBusinessMonth(model.getBusinessMonth()), model);
            throw new BusinessException(WebCodeMessageEnum.BUSINESS_MONTH_FROZEN_ERROR.getMsg());
        }

        Integer permissionsType = userInfo.getUserDataPermissionsDTO().getDataPermissionsType();
        //查询上级机构
        SaleTaskModel parentModel = saleTaskRepository.getById(model.getParentId());

        // 验证操作权限
        boolean currentOrgOperate = Objects.equals(model.getOrgId(), currentOrgId);// 操作当前登录人机构销售任务
        boolean lowerLevelOperate = false;// 操作当前登录人下级机构销售任务
        if (!currentOrgOperate) {
            if (Objects.nonNull(parentModel) && Objects.equals(parentModel.getOrgId(), currentOrgId)) {
                lowerLevelOperate = true;
            }

            if (!lowerLevelOperate) {
                throw new BusinessException(WebCodeMessageEnum.ROLE_NO_PERMISSION.getMsg());
            }
        }

        // 验证任务状态
        if (SaleTaskStatusEnum.ISSUED.getKey() != model.getTaskStatus()
                && PENDING_APPROVAL.getKey() != model.getTaskStatus()
                && UNCOMMITTED.getKey() != model.getTaskStatus()
                && SaleTaskStatusEnum.COMMITTED.getKey() != model.getTaskStatus()) {
            throw new BusinessException("任务状态异常");
        }

        //如果是分公司撤销还要修改上一级总部视角下的部门任务汇总和分司任务汇总
        if (DataPermitEnum.COMPANY.getCode().equals(permissionsType) && Objects.nonNull(parentModel)){
            if (model.getDepartmentTotal().compareTo(parentModel.getDepartmentTotal()) <= 0) {
                parentModel.setDepartmentTotal(NumberUtil.sub(parentModel.getDepartmentTotal(), model.getDepartmentTotal()));
            }
            if (model.getBranchOfficeTotal().compareTo(parentModel.getBranchOfficeTotal()) <= 0) {
                parentModel.setBranchOfficeTotal(NumberUtil.sub(parentModel.getBranchOfficeTotal(), model.getBranchOfficeTotal()));
            }
            saleTaskRepository.updateById(parentModel);
        }

        //撤销后实际任务全部置为0
        model.setBranchOfficeTotal(BigDecimal.ZERO);
        model.setDepartmentTotal(BigDecimal.ZERO);
        //当前机构状态变更
        model.setTaskStatus(REVOKED.getKey());
        saleTaskRepository.updateById(model);
        if (lowerLevelOperate) {
            return;
        }

        // 下属机构全部变更为初始化状态
        LambdaQueryWrapper<SaleTaskModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SaleTaskModel::getParentId, model.getId());
        List<SaleTaskModel> models = saleTaskRepository.list(queryWrapper);

        models.forEach(m -> {
            m.setTaskStatus(SaleTaskStatusEnum.INIT.getKey());
            //撤销后下级实际任务全部置为0
            m.setBranchOfficeTotal(BigDecimal.ZERO);
            m.setDepartmentTotal(BigDecimal.ZERO);
        });

        //下级机构状态变更
        saleTaskRepository.updateBatchById(models);

        //如果是总部撤销，则删除下属分公司存在下发第三级机构的任务
        if (DataPermitEnum.ALL.getCode().equals(permissionsType) && CollUtil.isNotEmpty(models)) {
            List<Long> thirdLevelOrgIds = models.stream().map(SaleTaskModel::getId).collect(Collectors.toList());
            saleTaskRepository.lambdaUpdate().in(SaleTaskModel::getParentId, thirdLevelOrgIds).remove();
        }
    }

    @Override
    public SaleTaskListInfoResponse view(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }

        SaleTaskListInfoResponse resp = new SaleTaskListInfoResponse();

        SaleTaskModel model = saleTaskRepository.getById(id);
        BeanUtils.copyProperties(model, resp);

        Optional.ofNullable(saleTaskAuditService.getLatestAuditByTaskId(model.getTaskId())).ifPresent(auditModel -> {
            resp.setLatestAuditTime(auditModel.getCreateTime());
            resp.setLatestAuditResult(auditModel.getAuditResult());
            resp.setLatestAuditRemark(auditModel.getAuditRemark());
        });

        return resp;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void audit(SaleTaskAuditRequest auditRequest) {
        List<Long> ids = auditRequest.getIds();
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }

        LambdaQueryWrapper<SaleTaskModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(SaleTaskModel::getId, ids);
        List<SaleTaskModel> models = saleTaskRepository.list(queryWrapper);
        if (CollectionUtils.isEmpty(models)) {
            return;
        }

        // 1. 业务参数校验

        // 1.1 校验商务月是否一致
        long count = models.stream().map(SaleTaskModel::getBusinessMonth).distinct().count();
        if (count > 1) {
            throw new BusinessException("审核的销售任务必须同一个商务月");
        }

        // 1.2 校验审核数据权限
        Integer orgType = models.get(0).getOrgType();
        UserLoginInfoDTO userInfo = UserContext.getCurrentUserInfo();
        UserDataPermissionsDTO userPermission = userInfo.getUserDataPermissionsDTO();
        // 总部审核分公司提交的部门销售任务、分公司审核事业部提交的商务组销售任务
        if (!(isHeadQuartersRole(userPermission) && ORG_TYPE_DEPT == orgType)
                && !isCompanyRole(userPermission) && SaleTaskModel.ORG_TYPE_BUSINESS == orgType) {
            throw new BusinessException("没有销售任务数据审核权限");
        }

        // 2. 审核任务
        Integer auditResult = auditRequest.getAuditResult();
        Integer taskStatus = SaleTaskAuditModel.AUDIT_PASS.equals(auditResult) ? SaleTaskStatusEnum.PASSED.getKey() : SaleTaskStatusEnum.REJECTED.getKey();
        List<SaleTaskModel> updateModels = Lists.newArrayList();
        for (SaleTaskModel model : models) {
            if (PENDING_APPROVAL.getKey() != model.getTaskStatus()) {
                // 总部权限，且已通过
                if (!(isHeadQuartersRole(userPermission) && SaleTaskStatusEnum.PASSED.getKey() == model.getTaskStatus())) {
                    continue;
                }
            }
            if (businessMonthService.isMonthFrozen(model.getBusinessMonth())) {
                freezeMonthErrorLogService.add(businessMonthService.getMonthInfoByBusinessMonth(model.getBusinessMonth()), model);
                throw new BusinessException(WebCodeMessageEnum.BUSINESS_MONTH_FROZEN_ERROR.getMsg());
            }

            model.setTaskStatus(taskStatus);
            if (isHeadQuartersRole(userPermission) && taskStatus == SaleTaskStatusEnum.PASSED.getKey()) {
                model.setDepartmentTotal(model.getBasicTask());
            }
            updateModels.add(model);
        }
        saleTaskRepository.updateBatchById(updateModels);

        //3. 审核通过后汇总（分公司视角）部门实际任务汇总、自己分司任务实际汇总，（总部视角）分司任务实际汇总
        updateParentTaskTotals(updateModels, auditResult);

        //4. 生成审核记录
        saleTaskAuditService.saleTaskAudit(updateModels, auditRequest);
    }

    @Override
    public SaleTaskListInfoResponse editInfo(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }

        // 查询销售任务
        SaleTaskModel model = saleTaskRepository.getById(id);
        if (Objects.isNull(model)) {
            return null;
        }

        // 校验是否当前用户机构销售任务
        UserLoginInfoDTO userInfo = UserContext.getCurrentUserInfo();
        Long currentOrgId = getCurrentOrgId(userInfo);

        // 判断是否编辑下级销售任务
        if (!model.getOrgId().equals(currentOrgId)) {
            SaleTaskModel parentModel = saleTaskRepository.getById(model.getParentId());
            if (Objects.isNull(parentModel) || !parentModel.getOrgId().equals(currentOrgId)) {
                return null;
            }
        }

        SaleTaskListInfoResponse resp = new SaleTaskListInfoResponse();
        BeanUtils.copyProperties(model, resp);
        return resp;

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public WebResult<Void> edit(SaleTaskEditRequest request) {
        if (Objects.isNull(request) || Objects.isNull(request.getId())) {
            return WebResult.success();
        }

        // 查询销售任务
        SaleTaskModel model = saleTaskRepository.getById(request.getId());
        if (Objects.isNull(model)) {
            return WebResult.success();
        }

        if (businessMonthService.isMonthFrozen(model.getBusinessMonth())) {
            freezeMonthErrorLogService.add(businessMonthService.getMonthInfoByBusinessMonth(model.getBusinessMonth()), model);
            return WebResult.error(WebCodeMessageEnum.BUSINESS_MONTH_FROZEN_ERROR);
        }


        // 校验修改数据权限
        UserLoginInfoDTO userInfo = UserContext.getCurrentUserInfo();
        UserDataPermissionsDTO dataPermissions = userInfo.getUserDataPermissionsDTO();
        if (ORG_TYPE_COMPANY == model.getOrgType() && !isHeadQuartersRole(dataPermissions)) {
            return WebResult.error(WebCodeMessageEnum.NO_DATA_EDIT_PERMISSION);
        }
        if (ORG_TYPE_DEPT == model.getOrgType()) {
            if (!isCompanyRole(dataPermissions)) {
                return WebResult.error(WebCodeMessageEnum.NO_DATA_EDIT_PERMISSION);
            } else if (Objects.equals(SaleTaskStatusEnum.PASSED.getKey(), model.getTaskStatus())) {
                return WebResult.error(WebCodeMessageEnum.PASSED_DATA_EDIT_ERROR);
            }
        }
        if (SaleTaskModel.ORG_TYPE_BUSINESS == model.getOrgType() && !isCareerRole(dataPermissions)) {
            return WebResult.error(WebCodeMessageEnum.NO_DATA_EDIT_PERMISSION);
        }

        // 记录原始值
        BigDecimal previousBasic = model.getBasicTask();
        // 差值
        BigDecimal subtract = request.getBasicTask().subtract(previousBasic).setScale(3, RoundingMode.HALF_UP);

        // 1. 设置销售任务的新值并且修改任务状态
        model.setBasicTask(request.getBasicTask());
        // 总部权限查询所有
        if (isHeadQuartersRole(dataPermissions)) {
            model.setTaskStatus(SaleTaskStatusEnum.ISSUED.getKey());
        } else {
            //查询其他平级机构任务
            List<SaleTaskModel> otherOrgModels = saleTaskRepository.lambdaQuery()
                    .eq(SaleTaskModel::getParentId, model.getParentId())
                    .ne(SaleTaskModel::getId, model.getId())
                    .list();
            //其他平级机构销售任务之和
            BigDecimal basicTaskSum = CollUtil.isEmpty(otherOrgModels) ? BigDecimal.ZERO : otherOrgModels.stream()
                    .map(SaleTaskModel::getBasicTask).reduce(BigDecimal.ZERO, BigDecimal::add);
            //上级机构设定的总任务
            SaleTaskModel higherLevel = saleTaskRepository.getById(model.getParentId());
            if (Objects.isNull(higherLevel)) {
                return WebResult.error(WebCodeMessageEnum.HIGHER_DATA_NOT_EXIST);
            }
            if (basicTaskSum.add(request.getBasicTask()).compareTo(higherLevel.getBasicTask()) < 0) {
                return WebResult.error(WebCodeMessageEnum.BASIC_TASK_GREATER_THAN_REFERENCE_TASK);
            }

            //如果是已通过的情况下去编辑，则部门任务实际值置为0，上级相应减去之前的基本任务
            if (ORG_TYPE_DEPT == model.getOrgType() && Objects.equals(SaleTaskStatusEnum.PASSED.getKey(), model.getTaskStatus())) {
                if (previousBasic.compareTo(higherLevel.getDepartmentTotal()) <= 0) {
                    higherLevel.setDepartmentTotal(NumberUtil.sub(higherLevel.getDepartmentTotal(), previousBasic));
                }
                if (previousBasic.compareTo(higherLevel.getBranchOfficeTotal()) <= 0) {
                    higherLevel.setBranchOfficeTotal(NumberUtil.sub(higherLevel.getBranchOfficeTotal(), previousBasic));
                }
                saleTaskRepository.updateById(higherLevel);
                SaleTaskModel topModel = saleTaskRepository.getById(higherLevel.getParentId());
                if (Objects.nonNull(topModel) && Objects.equals(SaleTaskModel.ROOT_PARENT_ID, topModel.getParentId())){
                    if (previousBasic.compareTo(topModel.getBranchOfficeTotal()) <= 0) {
                        topModel.setBranchOfficeTotal(NumberUtil.sub(topModel.getBranchOfficeTotal(), previousBasic));
                    }
                    if (previousBasic.compareTo(topModel.getDepartmentTotal()) <= 0) {
                        topModel.setDepartmentTotal(NumberUtil.sub(topModel.getDepartmentTotal(), previousBasic));
                    }
                    saleTaskRepository.updateById(topModel);
                }
            }
            model.setDepartmentTotal(BigDecimal.ZERO);
            model.setTaskStatus(PENDING_APPROVAL.getKey());
        }

        saleTaskRepository.updateById(model);

        // 2. 更新汇总数据
        List<SaleTaskModel> parentModels = Lists.newArrayList();
        //summaryData(model.getParentId(), parentModels, subtract);
        saleTaskRepository.updateBatchById(parentModels);

        // 3. 记录销售任务变更日志
        List<SaleTaskOpeLogModel> opeLogs = Lists.newArrayList();
        SaleTaskOpeLogModel opeLog = new SaleTaskOpeLogModel();
        opeLog.setTaskId(request.getId());
        opeLog.setPreviousBasicTask(model.getBasicTask());
        opeLog.setModifiedBasicTask(request.getBasicTask());
        opeLog.setOpeType(SaleTaskOpeLogModel.LOG_OPE_TYPE_UPDATE);
        opeLogs.add(opeLog);
        saleTaskOpeLogService.saveBatch(opeLogs);

        return WebResult.success();
    }

    private List<SaleTaskModel> loopParentModels(Long parentId, List<SaleTaskModel> models) {
        if (SaleTaskModel.ROOT_PARENT_ID.equals(parentId)) {
            return models;
        }

        SaleTaskModel parentModel = saleTaskRepository.getById(parentId);
        if (Objects.isNull(parentModel)) {
            return models;
        }
        models.add(parentModel);
        return loopParentModels(parentModel.getParentId(), models);
    }

    private void summaryData(Long parentId, List<SaleTaskModel> parentModels, BigDecimal subtract) {
        loopParentModels(parentId, parentModels);
        for (SaleTaskModel parentModel : parentModels) {
            // 总部任务更新分公司汇总
            if (SaleTaskModel.ROOT_PARENT_ID.equals(parentModel.getParentId())) {
                BigDecimal currentBasic = parentModel.getBranchOfficeTotal().add(subtract).setScale(3, RoundingMode.HALF_UP);
                parentModel.setBranchOfficeTotal(currentBasic);
            }
            // 分司更新部门任务汇总
            if (ORG_TYPE_COMPANY == parentModel.getOrgType()) {
                BigDecimal currentBasic = parentModel.getDepartmentTotal().add(subtract).setScale(3, RoundingMode.HALF_UP);
                parentModel.setDepartmentTotal(currentBasic);
            }
        }
    }

    private int getNextLevelOrgType(int orgType) {
        if (ORG_TYPE_COMPANY == orgType) {
            return ORG_TYPE_DEPT;
        }
        if (ORG_TYPE_DEPT == orgType) {
            return SaleTaskModel.ORG_TYPE_BUSINESS;
        }
        return ORG_TYPE_COMPANY;
    }

    @Override
    public SaleTaskModel getByOrgIdAndBusinessMonth(Long orgId, String businessMonth) {
        LambdaQueryWrapper<SaleTaskModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SaleTaskModel::getOrgId, orgId);
        queryWrapper.eq(SaleTaskModel::getBusinessMonth, businessMonth);
        return saleTaskRepository.getOne(queryWrapper);
    }

    @Override
    public void excelImport(MultipartFile file) throws IOException {
        UserLoginInfoDTO userInfo = UserContext.getCurrentUserInfo();
        beforeImportCheck(file, userInfo);

        List<SaleTaskModel> batchList = new ArrayList<>();
        Workbook workbook = WorkbookFactory.create(file.getInputStream());
        // 读取第一个 sheet
        Sheet sheet = workbook.getSheetAt(0);
        Iterator<Row> rowIterator = sheet.iterator();
        // 跳过2个标题行,因为销售任务导入模板数据从第3行开始
        rowIterator.next();
        rowIterator.next();

        while (rowIterator.hasNext()) {
            Row row = rowIterator.next();
            // 判断当前行是否为空，如果为空则跳过
            if (isRowEmpty(row)) {
                log.info("当前第{}行数据为空，跳过", row.getRowNum() + 1);
                continue;
            }
            SaleTaskModel entity = this.parseAndValidateRow(row);
            OrgInfoResponse orgInfo = innerService.queryOrgInfoById(entity.getOrgId());
            // 当前机构市场类别
            if (Objects.nonNull(orgInfo)) {
                Integer marketCategoryId = orgInfo.getMarketCategoryId();
                if (Objects.isNull(marketCategoryId)) {
                    entity.setMarketClassification("");
                } else {
                    entity.setMarketClassification(MarketCategoryTypeEnum.getNameByType(marketCategoryId));
                }
            }
            entity.setBranchOfficeTotal(new BigDecimal("0"));
            entity.setDepartmentTotal(new BigDecimal("0"));
            entity.setBusinessUnitTotal(new BigDecimal("0"));
            entity.setTaskStatus(UNCOMMITTED.getKey());
            //父级id放在后面处理
            entity.setParentId(0L);
            entity.setTaskId(IdUtil.getSnowflakeNextId());
            entity.setDeleteFlag(0);
            entity.setCreateTime(new Date());
            entity.setCreateUserId(userInfo.getUserId());
            entity.setCreateUserName(userInfo.getName());
            entity.setUpdateTime(new Date());
            entity.setUpdateUserId(userInfo.getUserId());
            entity.setUpdateUserName(userInfo.getName());
            entity.setOrgType(OrganizationTypeEnum.REGION.getCode());
            batchList.add(entity);
        }

        workbook.close();

        dataImportCheck(batchList);

        //获取总部信息
        Pair<Long, String> pair = getHeadQuartersOrgInfo();
        String businessMonth = batchList.get(0).getBusinessMonth();
        if (businessMonthService.isMonthFrozen(businessMonth)) {
            freezeMonthErrorLogService.add(businessMonthService.getMonthInfoByBusinessMonth(businessMonth), rowIterator);
            throw new BusinessException(WebCodeMessageEnum.BUSINESS_MONTH_FROZEN_ERROR.getMsg());
        }

        //删除存在的总部和分公司数据
        List<Long> orgIds = batchList.stream().map(SaleTaskModel::getOrgId).filter(Objects::nonNull).collect(Collectors.toList());
        LambdaQueryWrapper<SaleTaskModel> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SaleTaskModel::getBusinessMonth, businessMonth);
        wrapper.in(SaleTaskModel::getOrgId, orgIds);
        List<SaleTaskModel> models = saleTaskRepository.list(wrapper);
        if (CollectionUtils.isNotEmpty(models)) {
            List<Long> secondIds = models.stream().map(SaleTaskModel::getId).collect(Collectors.toList());
            Long count = saleTaskRepository.lambdaQuery()
                    .in(SaleTaskModel::getParentId, secondIds)
                    .eq(SaleTaskModel::getBusinessMonth, businessMonth).count();
            if (count > 0) {
                throw new BusinessException("分公司下存在下属机构下发任务，请先确认后再导入");
            }
            //删除已经存在的分公司数据
            saleTaskRepository.removeBatchByIds(secondIds);
        }

        //累加excel导入基本任务总和
        BigDecimal basicTaskTotal = new BigDecimal("0.0");
        for (SaleTaskModel saleTaskRequest : batchList) {
            basicTaskTotal = basicTaskTotal.add(saleTaskRequest.getBasicTask()).setScale(3, RoundingMode.HALF_UP);
        }

        //查询导入商务月的总部数据
        SaleTaskModel entity = saleTaskRepository.lambdaQuery().eq(SaleTaskModel::getOrgId, pair.getKey())
                .eq(SaleTaskModel::getBusinessMonth, businessMonth).last("limit 1").one();
        //说明是首次导入，先插入总部数据
        if (Objects.isNull(entity)) {
            entity = new SaleTaskModel();
            entity.setOrgId(pair.getLeft());
            entity.setOrgName(pair.getRight());
            entity.setOrgType(OrganizationTypeEnum.HEADER.getCode());
            entity.setBusinessMonth(businessMonth);
            entity.setMarketClassification(null);
            entity.setBasicTask(basicTaskTotal);
            entity.setBranchOfficeTotal(new BigDecimal("0"));
            entity.setDepartmentTotal(new BigDecimal("0"));
            entity.setBusinessUnitTotal(new BigDecimal("0"));
            entity.setTaskStatus(SaleTaskStatusEnum.ISSUED.getKey());
            entity.setParentId(0L);
            entity.setTaskId(IdUtil.getSnowflakeNextId());
            entity.setDeleteFlag(0);
            entity.setCreateTime(new Date());
            entity.setCreateUserId(userInfo.getUserId());
            entity.setCreateUserName(userInfo.getName());
            entity.setUpdateTime(new Date());
            entity.setUpdateUserId(userInfo.getUserId());
            entity.setUpdateUserName(userInfo.getName());
            saleTaskRepository.save(entity);
        } else {
            //查询存在的分公司数据
            List<SaleTaskModel> existList = saleTaskRepository.lambdaQuery()
                    .eq(SaleTaskModel::getParentId, entity.getId())
                    .eq(SaleTaskModel::getBusinessMonth, businessMonth).list();
            if (CollUtil.isNotEmpty(existList)) {
                for (SaleTaskModel saleTaskRequest : existList) {
                    //更新状态为已提交
                    saleTaskRequest.setTaskStatus(UNCOMMITTED.getKey());
                    basicTaskTotal = basicTaskTotal.add(saleTaskRequest.getBasicTask()).setScale(3, RoundingMode.HALF_UP);
                }
                saleTaskRepository.updateBatchById(existList);
            }
            //更新重新计算后的总部基本任务数据
            entity.setBasicTask(basicTaskTotal);
            //更新状态为已下发
            entity.setTaskStatus(SaleTaskStatusEnum.ISSUED.getKey());
            saleTaskRepository.updateById(entity);
        }

        //批量插入导入的分公司数据
        SaleTaskModel finalEntity = entity;
        batchList.forEach(e -> e.setParentId(finalEntity.getId()));
        saleTaskRepository.saveBatch(batchList);
    }

    @Override
    public List<OpenSaleTaskResponse> openSaleTask(OpenSaleTaskRequest request) {
        List<Long> orgIds = request.getOrgIds();
        String businessMonth = request.getBusinessMonth();
        List<SaleTaskModel> saleTaskModels = saleTaskRepository.selectSaleTaskByOrgIds(orgIds, businessMonth);
        return BeanUtil.copyToList(saleTaskModels, OpenSaleTaskResponse.class);
    }

    @Override
    public List<SaleTaskBranchResponse> queryCompanyList(String businessMonth) {
        List<SaleTaskBranchResponse> result = Lists.newArrayList();
        List<Long> orgIds = Lists.newArrayList();
        // 查询组织树，获取所有分司信息
        List<OrgInfoNodeResponse> orgInfoNodeResponses = innerService.queryKjOrgTree();
        // 中企跨境城市运营中心区 type = 2
        OrgInfoNodeResponse region = orgInfoNodeResponses.get(0);
        Map<Long, List<OrgInfoNodeResponse>> branchChild = region.getChildOrgInfoNodeRespList().stream().collect(Collectors.toMap(OrgInfoNodeResponse::getOrgId, OrgInfoNodeResponse::getChildOrgInfoNodeRespList));
        List<Long> noLeaderCompany = getNoLeaderCompany(businessMonth);
        if (CollectionUtils.isEmpty(noLeaderCompany)) {
            return Collections.emptyList();
        }
        List<SaleTaskModel> saleTaskModels = saleTaskRepository.selectSaleTaskByOrgIds(noLeaderCompany, businessMonth);
        //leader为空且分司任务状态为未提交/已撤销的分司
        List<SaleTaskModel> saleTasks = saleTaskModels.stream().filter(v -> v.getTaskStatus().equals(UNCOMMITTED.getKey()) || v.getTaskStatus().equals(REVOKED.getKey()))
                .collect(Collectors.toList());
        for (SaleTaskModel saleTask : saleTasks) {
            SaleTaskBranchResponse response = new SaleTaskBranchResponse();
            response.setCompanyName(saleTask.getOrgName());
            List<QuerySaleTaskListResponse> records = Lists.newArrayList();
            // 处理分司
            QuerySaleTaskListResponse currentOrgTask = new QuerySaleTaskListResponse();
            Long companyId = saleTask.getOrgId();
            currentOrgTask.setOrgId(companyId);
            currentOrgTask.setOrgName(saleTask.getOrgName());
            currentOrgTask.setBusinessMonth(businessMonth);
            currentOrgTask.setId(saleTask.getId());
            currentOrgTask.setOrgType(ORG_TYPE_COMPANY);
            SaleTaskModel one = saleTaskRepository.lambdaQuery()
                    .eq(SaleTaskModel::getOrgId, currentOrgTask.getOrgId())
                    .eq(SaleTaskModel::getBusinessMonth, businessMonth).one();
            if (Objects.nonNull(one)) {
                currentOrgTask.setBasicTask(one.getBasicTask());
            }
            // 查询分司的上下级
            QueryLeveRelationResponse relationResponse = innerService.queryLeveRelation(companyId);
            //当前机构市场类别
            if (Objects.nonNull(relationResponse) && Objects.nonNull(relationResponse.getCurrentLeveOrgInfo())) {
                Integer marketCategoryId = relationResponse.getCurrentLeveOrgInfo().getMarketCategoryId();
                if (Objects.isNull(marketCategoryId)) {
                    currentOrgTask.setMarketClassification("");
                } else {
                    currentOrgTask.setMarketClassification(MarketCategoryTypeEnum.getNameByType(marketCategoryId));
                }
            }
            records.add(currentOrgTask);

            //下一级机构市场类别
            if (Objects.nonNull(relationResponse) && Objects.nonNull(relationResponse.getLowerLeveOrgInfos())) {
                List<Long> orgIdList = relationResponse.getLowerLeveOrgInfos().stream().map(LowerLeveRelationResponse::getOrgId)
                        .map(Long::parseLong).distinct().collect(Collectors.toList());

                List<SaleTaskModel> list = saleTaskRepository.lambdaQuery().in(SaleTaskModel::getOrgId, orgIdList)
                        .eq(SaleTaskModel::getBusinessMonth, businessMonth).list();
                //机构基本任务map
                Map<Long, BigDecimal> basicTaskMap = CollUtil.isEmpty(list) ? Maps.newHashMap()
                        : list.stream().collect(Collectors.toMap(SaleTaskModel::getOrgId, SaleTaskModel::getBasicTask));

                List<OrgInfoNodeResponse> childOrgInfoNodeRespList = branchChild.get(companyId);
                if (CollUtil.isEmpty(childOrgInfoNodeRespList)) {
                    continue;
                }
                //真正的商务机构，过滤了commerceFlag为0的商务机构
                List<Long> orgCommerceFlagList = childOrgInfoNodeRespList.stream().filter(v -> v.getCommerceFlag().equals(NumberConstants.INTEGER_VALUE_1)).map(OrgInfoNodeResponse::getOrgId).collect(Collectors.toList());

                relationResponse.getLowerLeveOrgInfos().forEach(child -> {
                    Long orgId = Convert.toLong(child.getOrgId());
                    if (orgCommerceFlagList.contains(orgId)) {
                        QuerySaleTaskListResponse childTask = new QuerySaleTaskListResponse();
                        childTask.setOrgId(orgId);
                        childTask.setOrgName(child.getName());
                        childTask.setBusinessMonth(businessMonth);
                        childTask.setParentId(saleTask.getId());
                        childTask.setMarketClassification("");
                        childTask.setBasicTask(basicTaskMap.getOrDefault(childTask.getOrgId(), null));
                        Optional.ofNullable(child.getMarketCategoryId()).ifPresent(categoryId ->
                                childTask.setMarketClassification(MarketCategoryTypeEnum.getNameByType(categoryId)));
                        records.add(childTask);
                    }
                });
            }
            response.setRecords(records);
            result.add(response);
        }

    return result;
    }

    @Override
    public WebResult<Void> commitBatch(SaleTaskBranchCommitRequest taskList) {
        if (ObjectUtil.isEmpty(taskList)) {
            return WebResult.success();
        }
        List<SaleTaskBranchResponse> saleTasks = taskList.getSaleTasks();
        String businessMonth = taskList.getBusinessMonth();
        if (StringUtils.isNotBlank(businessMonth)&& businessMonthService.isMonthFrozen(businessMonth)) {
            freezeMonthErrorLogService.add(businessMonthService.getMonthInfoByBusinessMonth(businessMonth), taskList);
            throw new BusinessException(WebCodeMessageEnum.BUSINESS_MONTH_FROZEN_ERROR.getMsg());
        }
        UserLoginInfoDTO userInfo = UserContext.getCurrentUserInfo();
        for (SaleTaskBranchResponse value : saleTasks) {
            List<QuerySaleTaskListResponse> records = value.getRecords();
            // 第一条是分司
            QuerySaleTaskListResponse company = records.get(0);

            // 1. 如果商务月有数据直接删除（只删除下级）
            records.remove(0);
            List<Long> orgIds = records.stream().map(QuerySaleTaskListResponse::getOrgId).filter(Objects::nonNull).collect(Collectors.toList());
            LambdaQueryWrapper<SaleTaskModel> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(SaleTaskModel::getBusinessMonth, businessMonth);
            wrapper.in(SaleTaskModel::getOrgId, orgIds);
            List<SaleTaskModel> models = saleTaskRepository.list(wrapper);
            if (CollectionUtils.isNotEmpty(models)) {
                saleTaskRepository.removeBatchByIds(models.stream().map(SaleTaskModel::getId).collect(Collectors.toList()));
            }


            // 2. 插入新销售任务数据
            List<SaleTaskModel> saleTaskModels = Lists.newArrayList();
            //int orgType = getNextLevelOrgType(company.getOrgType());
            int orgType = ORG_TYPE_DEPT;
            for (QuerySaleTaskListResponse record : records) {
                SaleTaskModel saleTaskModel = new SaleTaskModel();
                BeanUtils.copyProperties(record, saleTaskModel);
                saleTaskModel.setOrgType(orgType);
                saleTaskModel.setTaskId(IdUtil.getSnowflakeNextId());
                saleTaskModel.setParentId(company.getId());
                saleTaskModel.setBusinessMonth(businessMonth);
                saleTaskModel.setUpdateUserId(userInfo.getUserId());
                saleTaskModel.setUpdateUserName(userInfo.getName());
                saleTaskModel.setTaskStatus(PENDING_APPROVAL.getKey());
                saleTaskModels.add(saleTaskModel);
            }
            saleTaskRepository.saveBatch(saleTaskModels);
            // 4. 记录操作日志
            saleTaskOpeLogService.saveBatch(saleTaskModels, SaleTaskOpeLogModel.LOG_OPE_TYPE_ADD);
            //  更新或插入汇总数据
            BigDecimal basicTaskTotal = new BigDecimal("0.0");

            // 汇总下级机构基本任务总额
            for (QuerySaleTaskListResponse saleTaskListResponse : records) {
                basicTaskTotal = basicTaskTotal.add(saleTaskListResponse.getBasicTask()).setScale(3, RoundingMode.HALF_UP);
            }
            LambdaQueryWrapper<SaleTaskModel> currentUserWrapper = new LambdaQueryWrapper<>();
            currentUserWrapper.eq(SaleTaskModel::getBusinessMonth, businessMonth);
            currentUserWrapper.eq(SaleTaskModel::getOrgId, company.getOrgId());
            SaleTaskModel currentUserTask = saleTaskRepository.getOne(currentUserWrapper);
            currentUserTask.setBasicTask(basicTaskTotal);
            currentUserTask.setTaskStatus(COMMITTED.getKey());
            currentUserTask.setUpdateTime(new Date());
            saleTaskRepository.updateById(currentUserTask);
        }
        return WebResult.success();
    }

    @Override
    public WebResult<Boolean> updateTask(SaleTaskEditRequest task) {
        UserLoginInfoDTO userInfo = UserContext.getCurrentUserInfo();
        String businessMonth = task.getBusinessMonth();
        if (StringUtils.isNotBlank(businessMonth)&& businessMonthService.isMonthFrozen(businessMonth)) {
            freezeMonthErrorLogService.add(businessMonthService.getMonthInfoByBusinessMonth(businessMonth), task);
            throw new BusinessException(WebCodeMessageEnum.BUSINESS_MONTH_FROZEN_ERROR.getMsg());
        }
        // 分司leader为空的，下面 部门 数据待审核、已驳回状态下可操作 修改
        List<Long> orgIds = getNoLeaderDept(businessMonth);
        if (CollectionUtil.isEmpty(orgIds)) {
            return WebResult.error(REQUEST_PARAM_EXCEPTION, "暂无机构领导为空的机构，请检查");
        }
        Long orgId = task.getOrgId();
        if (!orgIds.contains(orgId)) {
            return WebResult.error(REQUEST_PARAM_EXCEPTION, "机构领导不为空，不允许修改");
        }
        SaleTaskModel saleTaskModel = saleTaskRepository.selectByOrgId(orgId, businessMonth);
        if (ObjectUtil.isEmpty(saleTaskModel)) {
            return WebResult.error(REQUEST_PARAM_EXCEPTION, "该任务暂无数据");
        }
        //查询其他平级机构任务
        List<SaleTaskModel> otherOrgModels = saleTaskRepository.lambdaQuery()
                .eq(SaleTaskModel::getParentId, saleTaskModel.getParentId())
                .ne(SaleTaskModel::getId, saleTaskModel.getId())
                .list();
        //其他平级机构销售任务之和
        BigDecimal basicTaskSum = CollUtil.isEmpty(otherOrgModels) ? BigDecimal.ZERO : otherOrgModels.stream()
                .map(SaleTaskModel::getBasicTask).reduce(BigDecimal.ZERO, BigDecimal::add);
        //上级机构设定的总任务
        SaleTaskModel higherLevel = saleTaskRepository.getById(saleTaskModel.getParentId());
        if (Objects.isNull(higherLevel)) {
            return WebResult.error(WebCodeMessageEnum.HIGHER_DATA_NOT_EXIST);
        }
        // 修改之后的基本任务应该>=父级的基本任务
        if (basicTaskSum.add(task.getBasicTask()).compareTo(higherLevel.getBasicTask()) < 0) {
            return WebResult.error(WebCodeMessageEnum.BASIC_TASK_GREATER_THAN_REFERENCE_TASK);
        }
        if (saleTaskModel.getTaskStatus().equals(PENDING_APPROVAL.getKey()) || saleTaskModel.getTaskStatus().equals(REJECTED.getKey())) {
            //记录操作日志
            saleTaskOpeLogService.saveLog(saleTaskModel.getTaskId(), saleTaskModel.getBasicTask(), task.getBasicTask(), SaleTaskOpeLogModel.LOG_OPE_TYPE_UPDATE);

            saleTaskModel.setBasicTask(task.getBasicTask());
            saleTaskModel.setTaskStatus(SaleTaskStatusEnum.PENDING_APPROVAL.getKey());
            saleTaskModel.setUpdateUserId(userInfo.getUserId());
            saleTaskModel.setUpdateUserName(userInfo.getName());
            saleTaskModel.setUpdateTime(new Date());
            saleTaskRepository.updateById(saleTaskModel);
            return WebResult.success();
        } else {
            return WebResult.error(REQUEST_PARAM_EXCEPTION, "部门状态非待审核、已驳回状态，不允许修改");
        }
    }


    private SaleTaskModel parseAndValidateRow(Row row) {
        SaleTaskModel entity = new SaleTaskModel();

        // 机构名称（必填）
        String orgName = getCellStringValue(row, 0);
        if (StringUtils.isBlank(orgName)) {
            throw new BusinessException("机构名称不能为空");
        }
        entity.setOrgName(orgName);

        // 机构ID（必填）
        Long orgId = getLongCellValue(row.getCell(1));
        if (Objects.isNull(orgId)) {
            throw new BusinessException("机构ID不能为空");
        }
        entity.setOrgId(orgId);

        // 商务月（必填日期）
        String businessMonth = getCellStringValue(row, 2);
        if (StringUtils.isBlank(businessMonth)) {
            throw new BusinessException("商务月不能为空");
        }
        entity.setBusinessMonth(businessMonth);

        // 基本任务金额（必填数值）
        BigDecimal amount;
        try {
            amount = BigDecimal.valueOf(row.getCell(3).getNumericCellValue());
        } catch (Exception e) {
            throw new BusinessException("金额格式错误");
        }
        if (amount.compareTo(BigDecimal.ZERO) < 0) {
            throw new BusinessException("金额不能为负数");
        }
        entity.setBasicTask(amount);

        return entity;
    }


    private String getCellStringValue(Row row, int cellIndex) {
        Cell cell = row.getCell(cellIndex);
        return (cell != null) ? cell.getStringCellValue().trim() : "";
    }

    private Long getLongCellValue(Cell cell) {
        if (cell == null) {
            return null;
        }
        if (cell.getCellType() == CellType.NUMERIC) {
            // 强制转换为 Long
            return (long) cell.getNumericCellValue();
        } else if (cell.getCellType() == CellType.STRING) {
            try {
                return Long.parseLong(cell.getStringCellValue().trim());
            } catch (NumberFormatException e) {
                // 处理非法格式
                return null;
            }
        }
        return null;
    }

    /**
     * 获取总部组织信息
     *
     * @return Pair<Long, String> 返回一个Pair对象，包含总部组织的ID和名称
     */
    private Pair<Long, String> getHeadQuartersOrgInfo() {
        List<OrgInfoNodeResponse> orgInfoNodeResps = innerService.queryKjOrgTree();
        if (CollUtil.isEmpty(orgInfoNodeResps)) {
            return Pair.of(headquartersOrgId, headquartersName);
        }
        return Pair.of(orgInfoNodeResps.get(0).getOrgId(), orgInfoNodeResps.get(0).getName());
    }

    // 新增方法：判断行是否为空
    private boolean isRowEmpty(Row row) {
        if (row == null) {
            return true;
        }
        for (Cell cell : row) {
            if (cell != null && !StringUtils.isBlank(getCellValue(cell))) {
                return false;
            }
        }
        return true;
    }

    // 新增方法：获取单元格值
    private String getCellValue(Cell cell) {
        if (cell == null) {
            return "";
        }
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                return String.valueOf(cell.getNumericCellValue());
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            default:
                return "";
        }
    }

    /**
     * 执行导入操作之前的检查
     * 此方法旨在确保上传的Excel文件有效，并且用户具有导入权限
     *
     * @param file     用户上传的Excel文件
     * @param userInfo 用户登录信息，包含用户的数据权限信息
     */
    private void beforeImportCheck(MultipartFile file, UserLoginInfoDTO userInfo) {
        if (file.isEmpty()) {
            throw new BusinessException("请选择要上传的Excel文件");
        }
        String fileName = file.getOriginalFilename();
        if (fileName == null) {
            throw new BusinessException("文件名不能为空");
        }
        if (!(fileName.endsWith(".xlsx") || fileName.endsWith(".xls"))) {
            throw new BusinessException("只支持 Excel 文件");
        }
        UserDataPermissionsDTO dataPermissions = userInfo.getUserDataPermissionsDTO();
        if (!isHeadQuartersRole(dataPermissions)) {
            throw new BusinessException("非总部权限没有导入权限");
        }
    }


    /**
     * 校验销售任务数据导入的合法性
     * 该方法主要进行以下几项校验：
     * 1. 是否存在多个不同的商务月
     * 2. 是否存在重复的机构ID和名称
     * 3. 机构ID和名称是否匹配
     *
     * @param batchList 批量导入的销售任务模型列表
     */
    private void dataImportCheck(List<SaleTaskModel> batchList) {
        if (CollectionUtils.isEmpty(batchList)) {
            return;
        }
        // 校验集合中是否存在两个不同的商务月
        Set<String> businessMonths = batchList.stream().map(SaleTaskModel::getBusinessMonth).filter(Objects::nonNull).collect(Collectors.toSet());
        if (businessMonths.size() > 1) {
            throw new BusinessException("导入的销售任务数据中存在多个不同的商务月，请确保所有数据属于同一个商务月");
        }

        // 检查是否存在重复的机构ID和名称
        Set<Long> orgIdSet = new HashSet<>();
        Set<String> orgNameSet = new HashSet<>();
        for (SaleTaskModel task : batchList) {
            if (!orgIdSet.add(task.getOrgId())) {
                throw new BusinessException("导入的销售任务数据中存在重复的机构ID：" + task.getOrgId());
            }
            if (!orgNameSet.add(task.getOrgName())) {
                throw new BusinessException("导入的销售任务数据中存在重复的机构名称：" + task.getOrgName());
            }
        }

        List<OrgInfoNodeResponse> orgInfoNodeResponses = innerService.queryKjOrgTree();
        if (CollectionUtils.isEmpty(orgInfoNodeResponses)) {
            return;
        }
        // 将orgInfoNodeResponses及其子节点映射为Map<Long, String>
        Map<Long, String> orgIdToNameMap = new HashMap<>();
        for (OrgInfoNodeResponse org : orgInfoNodeResponses) {
            orgIdToNameMap.put(org.getOrgId(), org.getName());
            // 递归处理子节点
            processChildOrgs(org.getChildOrgInfoNodeRespList(), orgIdToNameMap);
        }

        for (SaleTaskModel task : batchList) {
            if (!orgIdToNameMap.containsKey(task.getOrgId())) {
                throw new BusinessException("导入的销售任务数据中存在不存在的机构ID：" + task.getOrgId());
            }
            String orgName = orgIdToNameMap.get(task.getOrgId());
            if (!orgName.equals(task.getOrgName())) {
                throw new BusinessException("导入的销售任务数据中存在不匹配的机构ID和名称：" + task.getOrgId() + "- " + task.getOrgName());
            }
        }
    }

    /**
     * 递归处理子机构，将其添加到映射中
     *
     * @param childOrgs      子机构列表
     * @param orgIdToNameMap 机构ID到名称的映射
     */
    private void processChildOrgs(List<OrgInfoNodeResponse> childOrgs, Map<Long, String> orgIdToNameMap) {
        if (childOrgs == null || childOrgs.isEmpty()) {
            return;
        }
        for (OrgInfoNodeResponse child : childOrgs) {
            orgIdToNameMap.put(child.getOrgId(), child.getName());
            processChildOrgs(child.getChildOrgInfoNodeRespList(), orgIdToNameMap);
        }
    }

    private void updateParentTaskTotals(List<SaleTaskModel> models, Integer auditResult) {
        //审核通过才更新数据
        if (!Objects.equals(SaleTaskAuditModel.AUDIT_PASS, auditResult)) {
            return;
        }
        // 使用Map来分组处理上级机构及其子任务
        Map<Long, List<SaleTaskModel>> parentTaskMap = models.stream()
                .collect(Collectors.groupingBy(SaleTaskModel::getParentId));

        // 获取所有受影响的上级机构ID
        Set<Long> parentIds = parentTaskMap.keySet();
        List<SaleTaskModel> parentSaleTaskModels = saleTaskRepository.listByIds(parentIds);
        Map<Long, SaleTaskModel> parentModelsMap = parentSaleTaskModels.stream()
                .collect(Collectors.toMap(SaleTaskModel::getId, model -> model));

        for (Map.Entry<Long, List<SaleTaskModel>> entry : parentTaskMap.entrySet()) {
            Long parentIdToProcess = entry.getKey();
            List<SaleTaskModel> childModels = entry.getValue();

            // 获取上级机构
            SaleTaskModel parentModel = parentModelsMap.get(parentIdToProcess);
            if (parentModel != null) {
                // 分公司视角：汇总部门实际任务
                if (ORG_TYPE_COMPANY == parentModel.getOrgType()) {
//                    BigDecimal departmentTotal = parentModel.getDepartmentTotal().add(childModels.stream()
//                            .map(SaleTaskModel::getBasicTask)
//                            .reduce(BigDecimal.ZERO, BigDecimal::add));
                    // 从表里面查实时的数据进行累加
                    List<SaleTaskModel> saleTaskModels = saleTaskRepository.selectSaleTaskByParentId(parentIdToProcess);
                    BigDecimal departmentTotal = saleTaskModels.stream()
                            .map(SaleTaskModel::getDepartmentTotal)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    parentModel.setDepartmentTotal(departmentTotal);
                    parentModel.setBranchOfficeTotal(departmentTotal);
                    //allBranchOfficeTotal = NumberUtil.add(allBranchOfficeTotal, parentModel.getBranchOfficeTotal());
                }
                saleTaskRepository.updateById(parentModel);
            }
        }


        //总部汇总分公司任务汇总
        List<Long> topIds = parentSaleTaskModels.stream().map(SaleTaskModel::getParentId).distinct().collect(Collectors.toList());
        if (CollUtil.isNotEmpty(topIds) && topIds.size() == 1) {
            SaleTaskModel topModel = saleTaskRepository.getById(topIds.get(0));
            if (ObjectUtil.isNotEmpty(topModel) && Objects.equals(SaleTaskModel.ROOT_PARENT_ID, topModel.getParentId())) {
//                BigDecimal addBranchOffice = topModel.getBranchOfficeTotal().add(models.stream()
//                        .map(SaleTaskModel::getBasicTask)
//                        .reduce(BigDecimal.ZERO, BigDecimal::add));
//                BigDecimal addDepartment = topModel.getDepartmentTotal().add(models.stream()
//                        .map(SaleTaskModel::getBasicTask)
//                        .reduce(BigDecimal.ZERO, BigDecimal::add));
                // 从表里面查实时的数据进行累加
                List<SaleTaskModel> saleTaskModels = saleTaskRepository.selectSaleTaskByParentId(topModel.getId());
                BigDecimal addBranchOffice = saleTaskModels.stream()
                        .map(SaleTaskModel::getBranchOfficeTotal)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal addDepartment = saleTaskModels.stream()
                        .map(SaleTaskModel::getDepartmentTotal)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                //  覆盖更新总部任务
                topModel.setBranchOfficeTotal(addBranchOffice);
                topModel.setDepartmentTotal(addDepartment);
                saleTaskRepository.updateById(topModel);
            }
        }
    }

    /**
     * 查询leader为空的公司
     * @return
     */
    private List<Long> getNoLeaderCompany(String businessMonth) {
        // 查询组织树，获取所有分司信息
        List<OrgInfoNodeResponse> orgInfoNodeResponses = innerService.queryKjOrgTree();
        // 中企跨境城市运营中心区 type = 2
        OrgInfoNodeResponse region = orgInfoNodeResponses.get(0);
        // 这一级是分司
        List<Long> branchIds = region.getChildOrgInfoNodeRespList().stream().map(OrgInfoNodeResponse::getOrgId).collect(Collectors.toList());
        List<Long> orgIds = Lists.newArrayList();
        String previousMonth = getPreviousMonth(parseYearMonth(businessMonth));
        String redisKey = MONTH_REPORT_COMPANY_REDIS + ":" + previousMonth;
        String cacheData = redisCache.get(redisKey);
        if (StrUtil.isBlank(cacheData)) {
            // 每个分司单独查询OrgBusiness接口，判断leader是否为空
            for (Long branchId : branchIds) {
                OrgBusinessResponse orgBusiness = innerService.getOrgBusiness(branchId, previousMonth, REQUEST_SOURCE);
                if (ObjectUtil.isNotEmpty(orgBusiness) && ObjectUtil.isEmpty(orgBusiness.getLeader())) {
                    orgIds.add(branchId);
                }
            }
            redisCache.set(redisKey, JSON.toJSONString(orgIds), CacheConstant.CacheExpire.TEN_MINUTES);
            return orgIds;
        }
        return JSON.parseObject(cacheData, new TypeReference<List<Long>>() {});
    }

    /**
     * 查询leader为空的部门事业部商务组
     * @return
     */
    private List<Long> getNoLeaderDept(String businessMonth) {
        List<Long> orgIds = Lists.newArrayList();
        String previousMonth = getPreviousMonth(parseYearMonth(businessMonth));
        String redisKey = MONTH_REPORT_DEPT_REDIS + ":" + previousMonth;
        String cacheData = redisCache.get(redisKey);
        if (StrUtil.isBlank(cacheData)) {
            List<Long> noLeaderCompany = getNoLeaderCompany(businessMonth);
            if (CollectionUtil.isEmpty(noLeaderCompany)) {
                return orgIds;
            }
            for (Long companyId : noLeaderCompany) {
                QueryLeveRelationResponse relationResponse = innerService.queryLeveRelation(companyId);
                // 这一级是分司的下一级（部门或者事业部）
                List<LowerLeveRelationResponse> lowerLeveOrgInfos = relationResponse.getLowerLeveOrgInfos();
                List<Long> ids = lowerLeveOrgInfos.stream()
                        .map(v -> {
                            try {
                                return v.getOrgId() != null ? Long.valueOf(v.getOrgId()) : null;
                            } catch (NumberFormatException e) {
                                return null;
                            }
                        })
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
                orgIds.addAll(ids);
                for (LowerLeveRelationResponse lowerLeveOrgInfo : lowerLeveOrgInfos) {
                    // 这一级是最底层一级
                    List<LowerLeveRelationResponse> child = lowerLeveOrgInfo.getChildLeveRelationResponse();
                    if (CollectionUtil.isEmpty(child)) {
                        continue;
                    } else {
                        List<Long> childIds = lowerLeveOrgInfos.stream()
                                .map(v -> {
                                    try {
                                        return v.getOrgId() != null ? Long.valueOf(v.getOrgId()) : null;
                                    } catch (NumberFormatException e) {
                                        return null;
                                    }
                                })
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList());
                        orgIds.addAll(childIds);
                    }
                }
            }
            redisCache.set(redisKey, JSON.toJSONString(orgIds), CacheConstant.CacheExpire.TEN_MINUTES);
            return orgIds;
        }
        return JSON.parseObject(cacheData, new TypeReference<List<Long>>() {});
    }

}
