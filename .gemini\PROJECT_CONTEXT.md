# GEMINI.md - bsp-achievement

## 概览

本项目是一个基于 Spring Boot 的多模块 Java 项目，名为 `bsp-achievement`，似乎是一个用于处理业绩或成就相关业务的后台服务。

### 技术栈

*   **核心框架:** Spring Boot 2.3.6.RELEASE
*   **数据访问:**
    *   MySQL (使用 Druid 连接池)
    *   MyBatis-Plus
    *   Spring Data Redis (使用 Redisson 客户端)
*   **消息队列:** RocketMQ
*   **分布式任务:** XXL-Job
*   **构建工具:** Maven
*   **辅助工具:** Lombok, FastJSON, Hutool

### 模块结构

项目被划分为多个模块，以实现关注点分离：

*   `achievement-dao`: 数据访问层，包含实体、MyBatis Mappers 和 Repositories。
*   `achievement-service`: 核心业务逻辑层。这是包含 `main` 方法的可执行模块。
*   `achievement-cache`: 缓存相关逻辑。
*   `achievement-util`: 通用工具类。
*   `achievement-support`: 提供支持或辅助功能。

## 构建与运行

### 构建

这是一个标准的 Maven 项目。在项目根目录运行以下命令来构建所有模块：

```shell
mvn clean install
```

### 运行

1.  **环境准备:**
    *   确保本地或网络可访问 MySQL、Redis 和 RocketMQ 服务。
    *   相关的连接信息在 `achievement-service/src/main/resources/` 目录下的 `application-dev.yml` (默认激活) 或其他环境特定的配置文件中。

2.  **启动服务:**
    *   可以直接在 IDE 中运行 `achievement-service` 模块的 `BspAchievementServiceApplication` 类。
    *   或者通过命令行启动打包后的 jar 文件：

    ```shell
    java -jar achievement-service/target/bsp-achievement-service-*.jar
    ```

服务将在 `8016` 端口启动，上下文路径为 `/achievement-web`。

## 开发约定

*   **代码风格:** 项目广泛使用 Lombok，请遵循其约定（例如，使用 `@Data`, `@Slf4j` 等注解）。
*   **数据库:**
    *   SQL 映射文件位于 `achievement-dao/src/main/resources/mapper/` 目录下。
    *   Mapper 接口位于 `com.xmd.achievement.dao.mapper` 包中。
*   **配置文件:**
    *   默认激活的 Spring Profile 是 `dev`。本地开发配置应放在 `application-dev.yml` 中。
    *   **注意:** `application.yml` 文件中的 `ignore.urls` 列表非常长，可能存在复制粘贴错误。在修改此部分时应格外小心，并考虑进行清理。
*   **日志:**
    *   日志配置文件为 `logback-dev.xml` 等，根据不同的环境进行配置。
    *   `application.yml` 中为不同包配置了详细的日志级别。
