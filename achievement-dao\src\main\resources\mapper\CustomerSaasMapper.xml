<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xmd.achievement.dao.mapper.CustomerSaasMapper">
    <select id="selectMaxChurnDateByCustomerId" resultType="java.util.Date">
        SELECT MAX(churn_date)
        FROM customer_saas
        WHERE customer_id = #{customerId}
          AND `status` = 1
          AND order_source = 1
          AND churn_status=3
          AND delete_flag = 0
    </select>
    
    <select id="selectMaxChurnDateByCustomerIdWithDate" resultType="java.util.Date">
        SELECT MAX(churn_date)
        FROM customer_saas
        WHERE customer_id = #{customerId}
        <if test="date != null">
            AND order_payment_time &lt; #{date}
        </if>          
          AND `status` = 1
          AND order_source = 1
          AND churn_status=3
          AND order_id != #{orderId}
          AND delete_flag = 0
    </select>
    
    <select id="countOrderBetween" resultType="int">
        SELECT COUNT(1)
        FROM customer_saas
        WHERE customer_id = #{customerId}
        <if test="start != null">
            AND order_payment_time &gt; #{start}
        </if>
        <if test="end != null">
            AND order_payment_time &lt; #{end}
        </if>
        <if test="orderSource != null">
            AND order_source = #{orderSource}
        </if>
        AND `status` = 1
        AND delete_flag = 0
    </select>

    <!-- searchCustomerSaasCount --> 

    <select id="searchCustomerSaasCount" resultType="com.xmd.achievement.dao.dto.SearchCustomerSaasDto">
    SELECT business_id AS businessId, 
    COUNT(1) AS newCustomerSaasCount
    FROM customer_saas
    WHERE business_id IN
    <foreach collection="businessIds" item="id" open="(" separator="," close=")">
        #{id}
    </foreach>
      AND saas_status = 0
      AND delete_flag = 0
      AND `status` = 1
      AND order_payment_time &gt;= #{startDate}
      AND order_payment_time &lt;= #{endDate}
    GROUP BY business_id
    </select>
    
    <!-- searchCustomerSaasGroupedPage -->
    <select id="searchCustomerSaasGroupedPage" resultType="com.xmd.achievement.dao.dto.CustomerSaasGroupDto">
        SELECT 
            cs.month,
            cs.customer_id AS customerId,
            GROUP_CONCAT(DISTINCT cs.customer_name SEPARATOR ', ') AS customerName,
            cs.business_id AS businessId,
            GROUP_CONCAT(DISTINCT cs.business_representative SEPARATOR ', ') AS businessName,
            MIN(CASE WHEN cs.order_payment_time IS NOT NULL THEN cs.order_payment_time END) AS newSaasCustomerTime,
            cs.order_no AS orderNo
        FROM customer_saas cs
        WHERE cs.delete_flag = 0
        AND cs.status = 1
        AND cs.saas_status = 0
        <if test="request.startMonth != null and request.startMonth != ''">
            AND cs.month &gt;= #{request.startMonth}
        </if>
        <if test="request.endMonth != null and request.endMonth != ''">
            AND cs.month &lt;= #{request.endMonth}
        </if>
        <if test="request.customerId != null and request.customerId != ''">
            AND cs.customer_id = #{request.customerId}
        </if>
        <if test="request.customerName != null and request.customerName != ''">
            AND cs.customer_name LIKE CONCAT('%', #{request.customerName}, '%')
        </if>
        <if test="request.businessId != null and request.businessId != ''">
            AND cs.business_id = #{request.businessId}
        </if>
        <if test="request.businessName != null and request.businessName != ''">
            AND cs.business_representative LIKE CONCAT('%', #{request.businessName}, '%')
        </if>
        GROUP BY cs.order_no, cs.month, cs.customer_id, cs.business_id
        ORDER BY cs.month DESC, MIN(CASE WHEN cs.order_payment_time IS NOT NULL THEN cs.order_payment_time END) DESC
    </select>

    <!-- searchCustomerSaasGrouped -->
    <select id="searchCustomerSaasGrouped" resultType="com.xmd.achievement.dao.dto.CustomerSaasGroupDto">
        SELECT 
            cs.month,
            cs.customer_id AS customerId,
            GROUP_CONCAT(DISTINCT cs.customer_name SEPARATOR ', ') AS customerName,
            cs.business_id AS businessId,
            GROUP_CONCAT(DISTINCT cs.business_representative SEPARATOR ', ') AS businessName,
            MIN(CASE WHEN cs.order_payment_time IS NOT NULL THEN cs.order_payment_time END) AS newSaasCustomerTime,
            cs.order_no AS orderNo
        FROM customer_saas cs
        WHERE cs.delete_flag = 0
        AND cs.status = 1
        AND cs.saas_status = 0
        <if test="request.startMonth != null and request.startMonth != ''">
            AND cs.month &gt;= #{request.startMonth}
        </if>
        <if test="request.endMonth != null and request.endMonth != ''">
            AND cs.month &lt;= #{request.endMonth}
        </if>
        <if test="request.customerId != null and request.customerId != ''">
            AND cs.customer_id = #{request.customerId}
        </if>
        <if test="request.customerName != null and request.customerName != ''">
            AND cs.customer_name LIKE CONCAT('%', #{request.customerName}, '%')
        </if>
        <if test="request.businessId != null and request.businessId != ''">
            AND cs.business_id = #{request.businessId}
        </if>
        <if test="request.businessName != null and request.businessName != ''">
            AND cs.business_representative LIKE CONCAT('%', #{request.businessName}, '%')
        </if>
        GROUP BY cs.order_no, cs.month, cs.customer_id, cs.business_id
        ORDER BY cs.month DESC, MIN(CASE WHEN cs.order_payment_time IS NOT NULL THEN cs.order_payment_time END) DESC
    </select>
    </mapper>
