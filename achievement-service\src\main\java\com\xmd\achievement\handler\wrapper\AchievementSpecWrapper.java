package com.xmd.achievement.handler.wrapper;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.xmd.achievement.dao.entity.AchievementCategoryDetailModel;
import com.xmd.achievement.dao.entity.AchievementSpecDetailModel;
import com.xmd.achievement.dao.entity.MqOrderPaymentInfoModel;
import com.xmd.achievement.dao.entity.PolicySpecDetailModel;
import com.xmd.achievement.service.IAchievementCategoryDetailService;
import com.xmd.achievement.service.IPolicyService;
import com.xmd.achievement.service.ISaasTabService;
import com.xmd.achievement.support.constant.NumberConstants;
import com.xmd.achievement.support.constant.enums.CurrentStatusEnum;
import com.xmd.achievement.support.constant.enums.SaasEnum;

import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 描述这个类的功能和用途
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/02/20/17:08
 * @since 1.0
 */
@Component
@Log4j2
public class AchievementSpecWrapper {
    @Resource
    IPolicyService policyService;

    @Resource
    private ISaasTabService saasTabService;

    public List<AchievementSpecDetailModel> buildSpecDetailModel(Integer mainSplitPerson, AchievementSpecDetailModel mastspecModel) {
        AchievementSpecDetailModel specDetailModel = new AchievementSpecDetailModel();
        specDetailModel.setAchievementSpecId(IdUtil.getSnowflakeNextId());
        specDetailModel.setStatus(CurrentStatusEnum.SERVICE.getStatus());
        PolicySpecDetailModel policyDetail = policyService.getPolicyDetailBySpecId(mastspecModel.getSpecId());
        //服务交付完成，商务实发业绩提成=商代提成业绩x（1-实发业绩提成比例);
        BigDecimal commissionRatio = policyDetail.getCommissionRatio().divide(new BigDecimal(100), RoundingMode.HALF_UP);
        specDetailModel.setAgentActCommAchv(mastspecModel.getAgentCommAchv().multiply((new BigDecimal(1).subtract(commissionRatio))));
        specDetailModel.setAgentDefCommAchv(BigDecimal.ZERO);
        specDetailModel.setMainSplitPerson(mainSplitPerson);
        specDetailModel.setCreateTime(new Date());
        specDetailModel.setUpdateTime(new Date());
        specDetailModel.setId(null);
        return null;
    }

    public List<AchievementSpecDetailModel> newBuildSpecDetailModels(Integer mainSplitPerson, List<AchievementSpecDetailModel> mastModels, MqOrderPaymentInfoModel mqOrderPaymentInfoModel) {
        List<AchievementSpecDetailModel> newSpecDetailModels = new ArrayList<>();
        for (AchievementSpecDetailModel mastModel : mastModels) {
            AchievementSpecDetailModel specDetailModel = new AchievementSpecDetailModel();
            BeanUtil.copyProperties(mastModel, specDetailModel);
            specDetailModel.setAchievementCategoryId(null);
            specDetailModel.setAchievementSpecId(IdUtil.getSnowflakeNextId());
            specDetailModel.setServeItemNo(mastModel.getServeItemNo());
            specDetailModel.setStatus(CurrentStatusEnum.SERVICE.getStatus());
            //服务交付完成，商务实发业绩提成= 产完成推实发时，应直接取支付完成的缓发 2025-08-21修改
            specDetailModel.setAgentActCommAchv(mastModel.getAgentDefCommAchv());
            specDetailModel.setAgentDefCommAchv(BigDecimal.ZERO);
            specDetailModel.setMainSplitPerson(mainSplitPerson);
            specDetailModel.setCreateTime(new Date());
            specDetailModel.setUpdateTime(new Date());
            specDetailModel.setId(null);

            if(StringUtils.isNoneEmpty(mqOrderPaymentInfoModel.getOrderProductId()) && !NumberConstants.STR_ZERO.equals(mqOrderPaymentInfoModel.getOrderProductId())){
                specDetailModel.setOrderProductId(mqOrderPaymentInfoModel.getOrderProductId());
            }

            //设置为0
            specDetailModel.setBillingPrice(BigDecimal.ZERO);
            specDetailModel.setRenewalPrice(BigDecimal.ZERO);
            specDetailModel.setStandardPrice(BigDecimal.ZERO);
            specDetailModel.setPayableAmount(BigDecimal.ZERO);
            specDetailModel.setPaidAmount(BigDecimal.ZERO);
            specDetailModel.setFirstYearQuote(BigDecimal.ZERO);
            specDetailModel.setFirstYearIncome(BigDecimal.ZERO);
            specDetailModel.setRenewalQuote(BigDecimal.ZERO);
            specDetailModel.setRenewalIncome(BigDecimal.ZERO);
            specDetailModel.setNetCash(BigDecimal.ZERO);
            specDetailModel.setAgentCommAchv(BigDecimal.ZERO);
            specDetailModel.setDeptCommAchv(BigDecimal.ZERO);
            specDetailModel.setBuCommAchv(BigDecimal.ZERO);
            specDetailModel.setBranchCommAchv(BigDecimal.ZERO);

            specDetailModel.setInstallmentNum(mqOrderPaymentInfoModel.getInstallmentNum());
            if (saasTabService.checkIsSaasKuaJing(specDetailModel.getProductId())){
                specDetailModel.setIsSaas(SaasEnum.YES.getCode());
            }else {
                specDetailModel.setIsSaas(SaasEnum.NO.getCode());
            }
            newSpecDetailModels.add(specDetailModel);
        }
        log.info("taskId:{},计算服务中业绩，最终规格业绩信息newCategoryDetailModelList:{}", mqOrderPaymentInfoModel.getTaskId(), JSONUtil.toJsonStr(newSpecDetailModels));
        return newSpecDetailModels;
    }
}
