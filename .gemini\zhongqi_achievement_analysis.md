# BSP业绩系统-中企业绩生成分析文档

## 1. 数据相关退出点详细分析

中企业绩的生成逻辑相对简单，退出点较少，主要发生在处理更新或删除任务时，未能找到对应的原始业绩数据。

### 1.1 更新/删除时未找到原始业绩数据

**代码位置**: `AchievementServiceImpl.java:1144`
**代码片段**:
```java
AchievementProductDetailModel dbModel = achievementProductDetailRepository.lambdaQuery()
        .eq(AchievementProductDetailModel::getAchievementSource, AchievementSourceEnum.ZHONGXIAO.getCode())
        .eq(AchievementProductDetailModel::getThirdAchievementId, model.getThirdId())
        .eq(AchievementProductDetailModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
        .orderByDesc(AchievementProductDetailModel::getUpdateTime)
        .last("limit 1").one();
if (TaskTypeEnum.UPDATE.getMsg().equals(model.getTaskType())) {
    AchievementProductDetailModel currentModel = achievementProductWrapper.buildFromThirdAchievement(model, mqModel);
    if (ObjectUtil.isNull(dbModel)) {
        // 如果dbModel为空，此处仅记录日志，不会抛出异常或退出
        operateLogService.save(dbModel, currentModel, OperateTypeEnum.CREATE, SourceSystemEnum.THIRDACHIEVEMENT, "中企业绩流水修改，三方业绩流水Id：" + currentModel.getThirdAchievementId());
    } else {
        // ...
    }
    // ...
}
if (TaskTypeEnum.DELETE.getMsg().equals(model.getTaskType())) {
    if (ObjectUtil.isNull(dbModel)) {
        log.info("中小三方业绩流水数据逻辑删除，原数据已删除，参数：{}", model);
        return; // 此处为退出点
    }
    // ...
}
```
**退出原因**: 在执行 `DELETE` 类型的任务时，如果根据第三方业绩ID (`thirdId`) 找不到已存在的、需要被删除的业绩记录，流程会直接退出。
**数据依赖**: `achievement_product_detail` 表中已存在的业绩数据。
**提供数据**: 无后续数据提供。
**默认值建议**: ❌ **不可处理**。这是一个正常的业务逻辑分支，表示数据已经被删除或从未创建，无需进行任何操作，直接退出是正确的。

## 2. 数据依赖关系梳理

### 2.1 核心数据流向图

```mermaid
graph TD
    A[ThirdAchievementModel 第三方业绩数据] --> C[数据转换];
    B[MqOrderPaymentInfoModel 订单支付信息] --> C;
    C -->|调用 buildFromThirdAchievement| D[AchievementProductDetailModel 商品业绩流水];
    D --> E[数据库 achievement_product_detail表];
```

### 2.2 关键数据依赖链

1.  **任务维度**: `ThirdAchievementModel.taskId` -> `ThirdAchievementModel.taskType` -> 决定执行新增、更新或删除操作。
2.  **关联维度**: `ThirdAchievementModel.thirdId` -> `AchievementProductDetailModel.thirdAchievementId` -> 关联已有业绩记录，用于更新和删除。
3.  **数据转换维度**: `ThirdAchievementModel` -> `AchievementProductWrapper` -> `AchievementProductDetailModel` -> 核心数据映射与生成。
4.  **订单信息维度**: `MqOrderPaymentInfoModel` -> `AchievementProductDetailModel` -> 补充 `customerId`, `paymentTime` 等信息。

### 2.3 数据转换字段详细映射

下表详细列出了从 `ThirdAchievementModel` (来源) 到 `AchievementProductDetailModel` (目标) 的所有字段映射关系，该逻辑主要在 `AchievementProductWrapper.buildFromThirdAchievement` 方法中实现。

| 目标字段 (Destination Field) | 来源字段 (Source Field) | 转换逻辑/备注 (Logic/Notes) |
| :--- | :--- | :--- |
| `achievementSource` | - | 硬编码为 `2` (中小) |
| `thirdAchievementId` | `model.getThirdId()` | 直接映射 |
| `achievementId` | `IdUtil.getSnowflake().nextId()` | 雪花算法生成新ID |
| `orderProductId` | `IdUtil.getSnowflake().nextIdStr()` | 雪花算法生成新ID |
| `orderId` | `IdUtil.getSnowflake().nextId()` | 雪花算法生成新ID |
| `siteFlag` | `model.getWebInfo()` | 直接映射 (是否网站) |
| `saleType` | `model.getBusinessType()` | **【完整映射见下文附表】** 通过 `setSaleType` 方法进行转换 |
| `productId` | `model.getProductId()` | 直接映射 |
| `productName` | `model.getProductName()` | 直接映射 |
| `isSaas` | `model.getProductId()` | 调用 `saasTabService.checkIsSaasZhongQi()` 判断 |
| `serveFinishTime` | `model.getServeFinishTime()` | 直接映射 (如果非空) |
| `standardPrice` | `model.getCurrentPrice()` | `BigDecimal.valueOf()` 转换 |
| `deliveryMethod` | - | 硬编码为 `1` (软件交付) |
| `orderType` | - | 硬编码为 `1` (普通订单) |
| `businessMonthId` | `mqModel.getCreateTime()` | 调用 `businessMonthService.getMonthInfo()` 获取 |
| `businessMonth` | `mqModel.getCreateTime()` | 调用 `businessMonthService.getMonthInfo()` 获取 |
| `provinceCode`, `provinceName`, `cityCode`, `cityName`, `districtCode`, `districtName` | `model.getCustId()` | 调用 `innerService.queryCustomerInfo()` 获取 |
| `company` | `model.getCompany()` | 直接映射 |
| `division` | `model.getDivision()` | 直接映射 |
| `department` | `model.getDepartment()` | 直接映射 |
| `companyId` | `model.getOrgId()` | `Long.valueOf()` 转换 |
| `regionId` | `model.getAreaId()` | `Long.valueOf()` 转换 |
| `divisionId` | `model.getBuId()` | `Long.valueOf()` 转换 (如果非空) |
| `deptId` | `model.getDeptId()` | `Long.valueOf()` 转换 (如果非空) |
| `productType` | `model.getProductType()` | `toString()` 转换 |
| `status` | `model.getState()` | 映射: `0`->`1`(有效), `1`->`3`(无效), `2`->`4`(退款), `3`->`2`(完成) |
| `orderNo` | `model.getOrderRecordCode()` | 直接映射 |
| `orderSource` | `model.getDataSource()` | **最终硬编码为 0**。代码会先尝试将来源为`1`的值改为`17`，但随后总会用`0`覆盖。 |
| `customerId` | `model.getCustId()` | 直接映射 |
| `customerName` | `model.getCustName()` | 直接映射 |
| `contractNo` | `model.getContractCode()` | 直接映射 |
| `businessId` | `model.getSalerId()` | 直接映射 |
| `businessRepresentative` | `model.getSalerName()` | 直接映射 |
| `mainSplitPerson` | `model.getShareType()` | 映射: `0`->`2`(分单), `1`->`1`(主单) |
| `payableAmount` | `model.getSingingAmount()` | `BigDecimal.valueOf()` 转换 |
| `paidAmount` | `model.getActualAccount()` | `BigDecimal.valueOf()` 转换 |
| `discountRate` | `model.getDiscountAccount()` | `BigDecimal.valueOf()` 转换 |
| `createTime` | `mqModel.getCreateTime()` | 直接映射 |
| `statisticsTime` | `mqModel.getCreateTime()` | 直接映射 |
| `signedTime` | `model.getSingingDate()` | 直接映射 |
| `paymentTime` | `model.getToAccountDate()` | 直接映射 |
| `firstYearQuote` | `model.getFirstStandardAccount()` | `BigDecimal.valueOf()` 转换 |
| `firstYearRevenue` | `model.getFirstActualAccount()` | `BigDecimal.valueOf()` 转换 |
| `renewalQuote` | `model.getRenewStandardAccount()` | `BigDecimal.valueOf()` 转换 |
| `renewalRevenue` | `model.getRenewActualAccount()` | `BigDecimal.valueOf()` 转换 |
| `netCash` | `model.getNetCashAccount()` | `BigDecimal.valueOf()` 转换 |
| `agentCommissionAchievement` | `model.getSaleHiredMoney()` | `BigDecimal.valueOf()` 转换 |
| `agentActualCommission` | `model.getRelaySaleHiredMoney()` | `BigDecimal.valueOf()` 转换 |
| `agentDeferredCommission` | `model.getDelaySaleHiredMoney()` | `BigDecimal.valueOf()` 转换 |
| `deptCommission` | `model.getManagerHiredMoney()` | `BigDecimal.valueOf()` 转换 |
| `divCommission` | `model.getCurrentPrice()` | `BigDecimal.valueOf()` 转换 |
| `branchCommission` | `model.getSubManagerHiredMoney()` | `BigDecimal.valueOf()` 转换 |
| `createUserId` | `model.getCreateUserId()` | 直接映射 |
| `createUserName` | `model.getCreateUserName()` | 直接映射 |
| `displayed` | `model.getDisplayed()` | 如果为空，默认为 `0` |
| `customerType` | `model.getCustType()` or `calculateCustomerContextV4.calculateCustomerV4()` | 根据 `model.getCreateTime()` 判断：<br>- 2025-01-13 20:04:29 之前: `1`->`2`(老), `2`->`1`(新), `3`->`3`(非新老)<br>- 之后: 调用V4逻辑计算 |

#### `saleType` 字段完整映射关系

| 来源 `businessType` 值 | 来源业务类型 (注释) | 目标 `saleType` 值 | 目标业务类型 |
|:---|:---|:---|:---|
| 1 | 新开 | 1 | 新开 (OPEN) |
| 2 | 续费 | 2 | 续费 (RENEW) |
| 3 | 扩容 | 4 | 另购 (ANOTHER_BUY) |
| 4 | 另购充值 | 4 | 另购 (ANOTHER_BUY) |
| 5 | 高价赎回 | 7 | 高价赎回 (HIGH_PRICE_REDEMPTION) |
| 6 | 转入 | 3 | 升级 (UPGRADE) |
| 7 | 升级(ZT升级ZT) | 3 | 升级 (UPGRADE) |
| 8 | zadd升级 | 3 | 升级 (UPGRADE) |
| 12 | DSP升级 | 3 | 升级 (UPGRADE) |
| 18 | 退款 | 6 | 退款 (REFUND) |
| 20 | 转款 | 5 | 转款 (TRANSFER) |
| 88 | 88升级 | 3 | 升级 (UPGRADE) |
| 169 | 大把推续费升级 | 3 | 升级 (UPGRADE) |
| 188 | 门户扩展升级 | 3 | 升级 (UPGRADE) |
| 3115 | Z+升级ZTSZT | 3 | 升级 (UPGRADE) |
| 3116 | Z+升级ZTSZM | 3 | 升级 (UPGRADE) |
| 3117 | NZ+升级ZTSZT | 3 | 升级 (UPGRADE) |
| 3118 | NZ+升级ZTSZM | 3 | 升级 (UPGRADE) |
| 3310 | 域名转入 | 1 | 新开 (OPEN) |


## 3. 默认值处理策略

由于中企业绩生成逻辑是基于上游推送的完整数据模型，系统本身未做过多的容错和默认值处理。主要策略应放在数据转换层 `AchievementProductWrapper`。

### 3.1 推荐实施的默认值策略

#### 3.1.1 高优先级（立即实施）

| 数据项 | 默认值策略 | 实现方式 | 风险等级 |
|---|---|---|---|
| **商务人员信息** | 若 `businessId` 或 `businessRepresentative` 为空，设置默认值 | 在 `buildFromThirdAchievement` 中，若 `model.getBusinessId()` 为空，则设置为 "-1" 或其他特殊标识。 | 低 |
| **客户类型** | 若 `customerType` 为空，设置默认值 | 在 `buildFromThirdAchievement` 中，若 `model.getCustomerType()` 为空，则默认为“未知客户类型”。 | 低 |
| **数值型字段** | 若金额等 `BigDecimal` 字段为 `null`，统一处理为 `BigDecimal.ZERO` | 在 `buildFromThirdAchievement` 中，对所有金额字段使用 `Optional.ofNullable(value).orElse(BigDecimal.ZERO)`。 | 低 |

### 3.2 默认值配置建议

建议在 `Wrapper` 中定义静态常量作为默认值，便于管理。

```java
// AchievementProductWrapper.java

private static final String DEFAULT_BUSINESS_ID = "-1";
private static final String DEFAULT_BUSINESS_NAME = "未知商务";
private static final Integer DEFAULT_CUSTOMER_TYPE = 99; // 假设99代表未知

public AchievementProductDetailModel buildFromThirdAchievement(...) {
    // ...
    saveModel.setBusinessId(Optional.ofNullable(model.getBusinessId()).orElse(DEFAULT_BUSINESS_ID));
    saveModel.setBusinessRepresentative(Optional.ofNullable(model.getBusinessRepresentative()).orElse(DEFAULT_BUSINESS_NAME));
    saveModel.setCustomerType(Optional.ofNullable(model.getCustomerType()).orElse(DEFAULT_CUSTOMER_TYPE));
    
    saveModel.setPaidAmount(Optional.ofNullable(model.getPaidAmount()).orElse(BigDecimal.ZERO));
    // ... 其他金额字段
}
```

## 4. 数据流程优化方案

当前流程较为直接，优化的核心在于增强其健壮性和可追溯性。

```mermaid
graph TD
    A[handelThirdAchievement] --> B{判断任务类型};
    B -->|ADD| C[buildFromThirdAchievement];
    B -->|UPDATE / DELETE| D{查询旧数据是否存在};
    
    D -->|存在| E[执行更新/删除];
    D -->|不存在| F[记录日志并退出];

    C --> G{数据校验与默认值处理};
    G -->|通过| H[保存新业绩];
    G -->|失败| I[记录异常并告警];

    E --> H;

    style F fill:#f9f,stroke:#333,stroke-width:2px
    style I fill:#f9f,stroke:#333,stroke-width:2px
```