package com.xmd.achievement.web.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 半佣金配置，用于处理广告通内需要进行半佣金处理的规格分类
 *
 * <AUTHOR>
 * @since 1.0
 */
@Data
@Component
@ConfigurationProperties(prefix = "half-commission")
public class HalfCommissionConfig {

    private List<Long> productIds;

    private List<Long> categoryIds;
}
