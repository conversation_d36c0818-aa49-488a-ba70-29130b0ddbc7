package com.xmd.achievement.dao.repository;

import com.xmd.achievement.dao.entity.SaleTaskModel;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 销售任务 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-27
 */
public interface ISaleTaskRepository extends IService<SaleTaskModel> {
    SaleTaskModel selectSaleTaskByOrgId(Long orgId, String businessMonth);

    List<SaleTaskModel> selectSaleTaskByOrgIds(List<Long> orgIds, String businessMonth);

    SaleTaskModel selectByOrgId(Long orgId, String businessMonth);

    List<SaleTaskModel> selectSaleTaskByParentId(Long orgId);

}
